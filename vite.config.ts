/*
 * @Author: “sunheng” “<EMAIL>”
 * @Date: 2025-03-05 10:12:23
 * @LastEditors: “sunheng” “<EMAIL>”
 * @LastEditTime: 2025-03-21 15:02:29
 * @FilePath: \supervise-clnt-platform-web\vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: “sunheng” “<EMAIL>”
 * @Date: 2025-03-05 10:12:23
 * @LastEditors: “sunheng” “<EMAIL>”
 * @LastEditTime: 2025-03-21 11:34:23
 * @FilePath: \supervise-clnt-platform-web\vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve as r } from 'path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import VueSetupExtend from 'vite-plugin-vue-setup-extend'
import WindiCSS from 'vite-plugin-windicss'
import inject from '@rollup/plugin-inject'
import esbuild from 'rollup-plugin-esbuild'
import { createHtmlPlugin } from 'vite-plugin-html'

const resvole = (_path) => r(__dirname, _path)
// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  console.log('🚀 ~ file: vite.config.ts:14 ~ defineConfig ~ mode, command:', mode, command)
  const env = loadEnv(mode, './')
  console.log('🚀 ~ defineConfig ~ env:', env)

  if (env.VITE_IS_LOCAL === 'true') {
  }

  return {
    base: `/${env.VITE_ROOT_DIR}/`,
    resolve: {
      alias: {
        '~': resvole('src'),
        '@': resvole('src'),
      },
    },
    server: {
      host: true,
      proxy: {
        '/api/v3/bw-svc-message-ws-service': {
          target: env.VITE_BASE_HOST.replace('http', 'ws'),
          changeOrigin: true,
          ws: true,
        },
        '/api': {
          target: env.VITE_BASE_HOST,
          changeOrigin: true,
        },
        '/img1': {
          target: env.VITE_BASE_HOST,
          changeOrigin: true,
        },
      },
    },

    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'development' ? false : true,
          drop_debugger: mode === 'development' ? false : true,
        },
      },
      outDir: env.VITE_ROOT_DIR,
      manifest: 'false',
    },
    // 其他配置...
    optimizeDeps: {
      exclude: ['index.es.js'] // 将 index.es.js 添加到排除列表
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 只添加全局变量和混入
          additionalData: `
            @import "@/assets/css/variables.scss";
          `,
        },
      },
    },
    plugins: [
      vue({}),

      {
        ...esbuild({
          target: 'chrome70',
          // 如有需要可以在这里加 js ts 之类的其他后缀
          include: /\.vue|.js|.ts$/,
          loaders: {
            '.vue': 'js',
          },
        }),
        enforce: 'post',
      },
      WindiCSS({
        scan: {
          include: ['./App.vue', './src/**/*.vue'],
        },
      }),

      VueSetupExtend(),

      createSvgIconsPlugin({
        iconDirs: [resvole('src/assets/svg')],
        symbolId: 'icon-[name]',
        customDomId: '__svg__icons__dom__',
      }),
      createHtmlPlugin({
        inject: {},
      }),
    ],
  }
})
