# 字体使用规范

## 字体文件管理

- 所有字体文件统一放在 `src/assets/fonts/` 目录下
- 字体文件命名使用小写字母和连字符，如：`youshebiaotihei.ttf`

## 字体定义

- 全局字体定义在 `src/assets/css/fonts.css` 中
- 使用 `@font-face` 定义字体族
- 始终包含 `font-display: swap` 以确保良好的加载体验

## 使用示例

```css
.font-example {
  font-family: 'YouSheBiaoTiHei', sans-serif;
}
```

## 添加新字体

1. 将字体文件放入 `src/assets/fonts/` 目录
2. 在 `src/assets/css/fonts.css` 中添加 `@font-face` 定义
3. 在样式中使用定义好的字体族
