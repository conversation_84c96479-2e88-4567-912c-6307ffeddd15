{"name": "gstanzer-supervise-web", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build2": "vue-tsc --noEmit && vite build", "build": "cross-env NODE_OPTIONS=--max-old-space-size=3072 vite build", "build:shanxi": "cross-env NODE_OPTIONS=--max-old-space-size=3072 vite build  --mode shanxi", "build3": "vite build", "test": "cross-env NODE_OPTIONS=--max-old-space-size=4096 && vite build", "preview": "vite preview", "prepare": "husky", "lint-staged": "lint-staged --no-stash", "commitlint": "commitlint --edit"}, "lint-staged": {"*.{vue,ts,tsx,js,jsx}": "eslint --ignore-path .eslint<PERSON>ore --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@rollup/plugin-inject": "^5.0.2", "@tanzerfe/tanzer-ui": "^0.0.10", "@vueuse/core": "^10.9.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "animate.css": "^4.1.1", "axios": "^0.26.0", "cropperjs": "^1.5.12", "dayjs": "^1.11.1", "echarts": "^5.3.1", "echarts-gl": "^2.0.9", "element-plus": "^2.8.12", "file-saver": "^2.0.5", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "pinia": "2.0.11", "pinia-plugin-persistedstate": "1.5.1", "pnpm": "^9.1.1", "process": "^0.11.10", "pubsub-js": "^1.9.4", "sortablejs": "^1.15.6", "uuid": "^8.3.2", "video.js": "^7.21.5", "vue": "^3.4.27", "vue-router": "^4.0.13", "vuex": "4.0.0-beta.4", "yarn": "^1.22.19", "ztree": "^3.5.24"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-angular": "^19.3.0", "@tanzerfe/eslint-config-lint": "^0.0.5", "@tsconfig/node18": "^18.2.2", "@types/echarts": "^4.9.22", "@types/jsdom": "^21.1.3", "@types/node": "^22.0.0", "@types/pubsub-js": "^1.8.3", "@vitejs/plugin-vue": "^5.0.4", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.2", "cross-env": "^7.0.3", "eslint": "^8.36.0", "fast-glob": "^3.3.1", "husky": "^9.0.11", "jsdom": "^22.1.0", "lint-staged": "^15.2.2", "npm-run-all2": "^6.0.6", "postcss-px-to-viewport": "^1.1.1", "postcss-px2rem": "^0.3.0", "rollup-plugin-esbuild": "^6.1.0", "sass": "^1.77.1", "tailwindcss": "^3.0.23", "terser": "^5.16.1", "typescript": "^5.4.5", "vite": "^5.2.12", "vite-plugin-html": "^3.2.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-plugin-windicss": "^1.8.3", "vitest": "^0.34.4", "vue-img-cutter": "^3.0.2", "vue-tsc": "^2.0.15", "windicss": "^3.5.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=22.5.1", "pnpm": ">=9.1.1"}}