<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>中国电信安徽公司智慧安全云平台</title>
    <link rel="shortcut icon" href="favicon-fire.ico" _href="favicon-fire.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="./assets/css/public.css">
    <link rel="stylesheet" type="text/css" href="./assets/css/common-gr.css">
    <link rel="stylesheet" type="text/css" href="./assets/css/common-lc.css">
    <link rel="stylesheet" type="text/css" href="./assets/css/common-wf.css">
    <link rel="stylesheet" type="text/css" href="./assets/fonts/iconfont.css">
	<link rel="stylesheet" type="text/css" href="./assets/css/alarmLayer.css">
	<script type="text/javascript">
		var token =localStorage.getItem("USER_TOKEN")
		 localStorage.getItem("USER_TOKEN");
		$.ajaxSetup({
		    dataType: "json",
		    // cache: false,
		    headers: {
		        "USER_TOKEN": token
		    },
		    // xhrFields: {
		    //     withCredentials: true
		    // },
		    complete: function(xhr) {
		        //token过期，则跳转到登录页面
				    console.log(xhr)
		        if(xhr.responseJSON && (xhr.responseJSON.code == 10001 || xhr.responseJSON.code == 401)){
              location.href = 'login.html';
		        }
		    }
		});
	</script>
    <script src="./assets/js/vue.js"></script>
    <script src="./assets/js/echarts.min.js"></script>
    <script src="./assets/js/ezuikit.js"></script>

    <!-- echarts 水球插件 (修改) -->
    <!-- <script src="http://echarts.baidu.com/resource/echarts-liquidfill-latest/dist/echarts-liquidfill.min.js"></script> -->
    <script src="./libs/echarts/echarts-liquidfill.js"></script>
    <!--引入动画库-->
    <link href="./assets/css/<EMAIL>" rel="stylesheet" type="text/css">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="./assets/element/index.css">
    <!--<link rel="stylesheet" href="./assets/css/index.css">-->
    <!-- 自定义element -->
    <link rel="stylesheet" href="./assets/css/custom-element.css">

    <!-- 引入组件库 -->
    <script src="./assets/js/elementUi.js"></script>
    <!--引入socket-->
    <script src="./assets/js/stomp.min.js"></script>
    <script src="./assets/js/sockjs.js"></script>
    <!-- 美化滚动条 -->

	<script src="./assets/js/hls.js"></script>

	<!-- rtsp video  播放 js START-->
	<script src="./assets/js/video/adapter.js"></script>
	<script src="./assets/js/video/platform.js"></script>
	<script src="./assets/js/video/h5splayer.js"></script>
	<script src="./assets/js/video/h5splayerhelper.js"></script>
	<!-- rtsp END -->

    <style type="text/css">
        body {
            width: 100%;
            height: 100%;
        }

        #app {
            position: relative;
            /* width: 3840px;
			height: 2160px; */
            width: 1920px;
            height: 1080px;
            background: no-repeat #021640;
        }

        header {
            height: 100px;
        }

        .container {
            width: 100%;
            position: absolute;
            top: 100px;
            left: 0;
            bottom: 0;
            padding: 0 20px 10px;
        }

        #sideLeft-load,
        #sideRight-load {
            width: 354px;
            height: 100%;
        }

        #sideLeft-load {
            float: left;
        }

        #sideRight-load {
            float: right;
        }

        #mainBox {
            margin: 0 374px;
            position: relative;
            border-top: 3px solid #2e4b88;
            height: 100%;
            /*border-top: 3px #2e4b88 solid;*/
            background: url("assets/images/mainbg.png") no-repeat center bottom rgba(14, 34, 75, 0.6);
        }

        #main-load,
        #main-sideLoad,
        .sideLoad {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
        }

        #main-load {
            top: 50px;
        }

        .sideLeftBox {
            font-size: 20px;
            color: #fff;
            height: 100%;
            width: 100%;
            position: relative;
        }

        .sideLeftBox a {
            color: #fff;
        }

        .sideLeftTop {
            width: 100%;
            height: 338px;
            box-sizing: border-box;
        }

        .sideLeftCenter {
            width: 100%;
            height: 318px;
        }

        .sideLeftBottom {
            width: 100%;
            height: 275px;
            overflow: hidden;
        }

        .sideRightBox {
            font-size: 20px;
            color: #fff;
            width: 100%;
            height: 100%;
            overflow: hidden;
            position: relative;
        }

        .sideRightBox a {
            color: #fff;
            background: yellowgreen;
        }

        .side_safety {
            height: 440px;
            box-sizing: border-box;
        }

        .side_patrol {
            height: 212px;
            box-sizing: border-box;
        }

        .side_danger {
            height: 318px;
            box-sizing: border-box;
        }

        #main-load,
        #main-sideLoad,
        .sideLoad {
            padding: 0 20px;
        }
        /* 视频样式 */

        #videoContainer {
            /* width:600px;
			height:340px; */
            width: 1000px;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1999;
        }

        #myPlayer {
            width: 100%;
            height: 100%;
        }

        .dsb-btn-close {
            cursor: pointer;
            z-index: 20000;
            display: block;
            position: absolute;
            right: 15px;
            top: 15px;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background-color: rgba(25, 64, 99, 0.7);
            -webkit-transform: rotate(45deg);
            -moz-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            -o-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        .dsb-btn-close:before {
            top: 3px;
            left: 50%;
            width: 2px;
            height: 18px;
            margin-left: -2px;
            transform: scale(.8);
        }

        .dsb-btn-close:after {
            top: 3px;
            left: 50%;
            width: 2px;
            height: 18px;
            margin-left: -2px;
            transform: scale(.8) rotate(90deg);
            ;
        }

        .dsb-btn-close:before,
        .dsb-btn-close:after {
            position: absolute;
            display: inline-block;
            opacity: .8;
            content: '';
            background-color: #fff;
        }

        @keyframes shake {
            from,
            to {
                transform: translate3d(0, 0, 0);
            }
            10%,
            30%,
            50%,
            70%,
            90% {
                transform: translate3d(-10px, 0, 0);
            }
            20%,
            40%,
            60%,
            80% {
                transform: translate3d(10px, 0, 0);
            }
        }

        .shake {
            animation-name: shake;
            animation: shake 1s;
            -webkit-animation: shake 1s;
        }

        .el-message-box {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .layer-video {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background-color: rgba(0, 0, 0, .8);
            z-index: 9999
        }

		/* .alarm-layer-wrapper
		.alarm-layer-wrapper.hidden */

        .alarm-layer-wrapper {
            background-color: rgba(0, 0, 0, .4);
        }

        .layer-video.hidden {
            display: none;
        }

        .global-dialog-warp {
            position: absolute;
            top: 100px;
            left: 0;
            width: 100%;
            height: 970px;
            padding: 0 20px 10px 20px;
            display: none;
        }
    </style>
</head>

<body class="AHDX-body">
    <div id="app" :loading="loading">

        <header id="header-load"></header>
        <div class="container" id="containerWrap">
            <div id="sideLeft-load" class="sideLeftBox">
                <div class="sideLeftTop" id="sideLeftTop"></div>
                <div class="sideLeftCenter" id="sideLeftCenter"></div>
                <div class="sideLeftBottom" id="sideLeftBottom"></div>
            </div>
            <div id="sideRight-load" class="sideRightBox">
                <div id="side_safetyIndex" class="side_safety sideRightCom"></div>
                <div id="side_patrolTask" class="side_patrol sideRightCom"></div>
                <div id="side_danger" class="side_danger sideRightCom"></div>
            </div>
            <div id="mainBox" @click="tests">
                <div class="navBox" id="nav-load"></div>
                <div id="main-onlineRate" class="sideLoad" v-show="mainLoad == 'main-onlineRate'"></div>
                <div id="main-xunjian" class="sideLoad" v-show="mainLoad == 'main-xunjian'"></div>
                <div id="main-statistics" class="sideLoad" v-show="mainLoad == 'main-statistics'"></div>
                <div id="main-danger" class="sideLoad" v-show="mainLoad == 'main-danger'"></div>
                <div id="main-load" v-show="mainLoad == 'main-load'"></div>
            </div>
        </div>
        <div class="global-dialog-warp" id="globalDialog"></div>

        <div class="layer-video hidden" id="layerWraper"></div>
        <div class="alarm-layer-wrapper hidden" id="alarmLaryer"></div>
    </div>
    <audio id="myAudio">
        <source src="./assets/images/notice.wav" type="audio/mpeg">
        浏览器不支持音频标签。
    </audio>
    <audio id="audio_frame">
        <source src="./assets/images/blank.wav" type="audio/mpeg">
        浏览器不支持音频标签。
    </audio>
</body>
<!--utils-->
<script src="./assets/js/md5-min.js"></script>
<script type="text/javascript" src="./assets/utils/globalTool.js"></script>
<script type="text/javascript" src="./assets/utils/utils.js"></script>
<script type="text/javascript" src="./assets/utils/cs.js"></script>

<script type="text/javascript" src="./config/config.js"></script>
<script type="text/javascript" src="./libs/crypto-js.js"></script>
<script type="text/javascript" src="./jm/decrypt.js"></script>
<script type="text/javascript" src="./pages/home-page/index.js"></script>
</html>
