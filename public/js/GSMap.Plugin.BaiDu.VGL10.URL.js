/*
 * @Author: “sunheng” “<EMAIL>”
 * @Date: 2025-03-14 16:42:06
 * @LastEditors: “sunheng” “<EMAIL>”
 * @LastEditTime: 2025-03-14 18:00:39
 * @FilePath: \supervise-clnt-platform-web\public\js\GSMap.Plugin.BaiDu.VGL10.URL.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
//离线矢量瓦片配置（必须在JS包初始化之前引入）
let urlHost = window.location.host
let urlProtocol = window.location.protocol
const gsMap = {}
gsMap.Plugin = {}
gsMap.Plugin.BaiDu = {}
gsMap.Plugin.BaiDu.VGL10 = {}
gsMap.Plugin.BaiDu.VGL10.URLHost = window.location.host + '/'
gsMap.Plugin.BaiDu.VGL10.URL_protocol = urlProtocol
window.GSMap = gsMap
