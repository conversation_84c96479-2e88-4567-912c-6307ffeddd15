@font-face {
  font-family: 'iconfont', sans-serif; /* Project id 3533844 */
  src: url('iconfont.woff2?t=1658236507529') format('woff2'),
    url('iconfont.woff?t=1658236507529') format('woff'),
    url('iconfont.ttf?t=1658236507529') format('truetype');
}

.iconfont {
  font-family: 'iconfont', sans-serif !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-jiugongge:before {
  content: '\e6a3';
}

.icon-sigongge:before {
  content: '\e615';
}
