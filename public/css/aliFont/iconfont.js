!function(e){var t,n,o,i,l,d='<svg><symbol id="icon-jiugongge" viewBox="0 0 1024 1024"><path d="M0.020978 292.554032l292.532543 0 0-292.532543-292.532543 0L0.020978 292.554032 0.020978 292.554032zM365.702518 292.554032l292.53152 0 0-292.532543-292.53152 0L365.702518 292.554032 365.702518 292.554032zM731.447503 0.020466l0 292.532543 292.53152 0 0-292.532543L731.447503 0.020466 731.447503 0.020466zM0.020978 658.297994l292.532543 0L292.553521 365.703029l-292.532543 0L0.020978 658.297994 0.020978 658.297994zM365.702518 658.297994l292.53152 0L658.234037 365.703029l-292.53152 0L365.702518 658.297994 365.702518 658.297994zM731.447503 658.297994l292.53152 0L1023.979022 365.703029l-292.53152 0L731.447503 658.297994 731.447503 658.297994zM0.020978 1023.978511l292.532543 0 0-292.53152-292.532543 0L0.020978 1023.978511 0.020978 1023.978511zM365.702518 1023.978511l292.53152 0 0-292.53152-292.53152 0L365.702518 1023.978511 365.702518 1023.978511zM731.447503 1023.978511l292.53152 0 0-292.53152-292.53152 0L731.447503 1023.978511 731.447503 1023.978511z"  ></path></symbol><symbol id="icon-sigongge" viewBox="0 0 1024 1024"><path d="M0 460.8h460.8V0H0v460.8zM563.2 0v460.8H1024V0H563.2zM0 1024h456.533333V567.466667H0V1024z m563.2 0H1024V563.2H563.2V1024z"  ></path></symbol></svg>',c=(c=document.getElementsByTagName("script"))[c.length-1].getAttribute("data-injectcss"),s=function(e,t){t.parentNode.insertBefore(e,t)};if(c&&!e.__iconfont__svg__cssinject__){e.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}function a(){l||(l=!0,o())}function r(){try{i.documentElement.doScroll("left")}catch(e){return void setTimeout(r,50)}a()}t=function(){var e,t=document.createElement("div");t.innerHTML=d,d=null,(t=t.getElementsByTagName("svg")[0])&&(t.setAttribute("aria-hidden","true"),t.style.position="absolute",t.style.width=0,t.style.height=0,t.style.overflow="hidden",t=t,(e=document.body).firstChild?s(t,e.firstChild):e.appendChild(t))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(t,0):(n=function(){document.removeEventListener("DOMContentLoaded",n,!1),t()},document.addEventListener("DOMContentLoaded",n,!1)):document.attachEvent&&(o=t,i=e.document,l=!1,r(),i.onreadystatechange=function(){"complete"==i.readyState&&(i.onreadystatechange=null,a())})}(window);