/**
 * setRem.ts - 响应式布局适配脚本
 * 功能：实现页面在不同分辨率下的自适应布局
 * 设计基准：1920px 设计稿，1rem = 192px
 * 适配范围：1360px ~ 3840px 宽度
 */

// 使用立即执行函数，避免污染全局作用域
;(function () {
  /**
   * 设置根元素字体大小
   * 根据当前视口宽度动态计算并设置 html 的 font-size
   */
  function setRem() {
    // 设计稿基准宽度（px）
    const baseWidth = 1920
    // 最小适配宽度（px）
    const minWidth = 1360
    // 最大适配宽度（px）
    const maxWidth = 3840
    // 获取 HTML 根元素
    const html = document.documentElement
    // 获取视口宽度
    let clientWidth = html.clientWidth
    // 限制视口宽度的最小值和最大值
    if (clientWidth > maxWidth) clientWidth = maxWidth
    if (clientWidth < minWidth) clientWidth = minWidth
    // 计算并设置根元素字体大小
    // 公式：当前视口宽度 / 基准宽度 * 基准字体大小
    // 基准字体大小 = 192px（即 1rem = 192px @1920px 设计稿）
    html.style.fontSize = (clientWidth / baseWidth) * 192 + 'px'

    // 如果屏幕宽大于2560小于3840 那么 字体放大一倍，大于等于3840 字体放大两倍
    // if (clientWidth >= 2560 && clientWidth < 3840) {
    //   html.style.fontSize = ((clientWidth / baseWidth) * 192) / 2 + 'px'
    // }
    // if (clientWidth >= 3840) {
    //   html.style.fontSize = ((clientWidth / baseWidth) * 192) / 3 + 'px'
    // }
  }
  setRem()
  window.addEventListener('resize', setRem)
  document.addEventListener('visibilitychange', function () {
    if (!document.hidden) {
      setRem()
    }
  })
})()
