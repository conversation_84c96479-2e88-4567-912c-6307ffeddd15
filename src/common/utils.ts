import config from '@/config'
import { Message, UserInfo } from '@/types'
import PubSub from 'pubsub-js'
import { nextTick, ref, Ref, App, isRef, computed } from 'vue'
import * as types from './types'
import { EVENT_TYPE, ROLE_GROUP, MESSAGE_RULE, SERVICE_MODE } from './eventType'
import { Router } from 'vue-router'
import { useUserInfo } from '@/store'
import { setPageJsonQueryParamsAdapter } from '@/commonTypes/jumpDataStatisticsQuery'

interface TempData {
  tempId?: string
  eventType: string
  [key: string]: string | number | undefined
}

const temp_data: Ref<TempData | null> = ref(null)

let app: App
export const loading = ref(false)

export function setApp(_app: App) {
  app = _app
}

export function getAppContext() {
  return app._context
}

export function getUserInfo(): UserInfo {
  return (JSON.parse(sessionStorage.getItem(config.USER_IFNO_NAMESPACE) as string) || {}).userInfo
}

export function isWoker(userInfo: UserInfo) {
  const rids = userInfo.roleIds
  return rids && rids.includes(ROLE_GROUP.WORKER)
}

export function isSafetyManage(userInfo: UserInfo) {
  const rids = userInfo.roleIds
  return rids && rids.includes(ROLE_GROUP.SAFETY_MANAGE)
}

export function isAdmin(userInfo: UserInfo) {
  const rids = userInfo.roleIds
  return rids && rids.includes(ROLE_GROUP.ADMIN)
}

export function isManageAdmin(userInfo: UserInfo) {
  const rids = userInfo.roleIds
  return rids && rids.includes(ROLE_GROUP.MANAGE_ADMIN)
}

export function isNineType() {
  // return  !isWoker(userInfo) && userInfo.hasSupervise === '1';
  return true
}

export function isInteger(str: any) {
  const num = str.substring(0, str.length - 1)
  const isNum = !isNaN(parseFloat(str)) && isFinite(str)
  if (parseFloat(num) === parseInt(num)) {
    return isNum ? parseInt(num) : parseInt(num) + str.substring(str.length - 1)
  } else {
    return isNum ? parseFloat(num).toFixed(2) : num + str.substring(str.length - 1)
  }
}

export function getMessageRole(): string[] {
  const ui = useUserInfo().value
  if (isSafetyManage(ui)) {
    return MESSAGE_RULE[ROLE_GROUP.SAFETY_MANAGE]
  } else if (isWoker(ui)) {
    return MESSAGE_RULE[ROLE_GROUP.WORKER]
  } else if (isManageAdmin(ui)) {
    return MESSAGE_RULE[ROLE_GROUP.MANAGE_ADMIN]
  }
  return []
}

export function normalizeAddress(item: any) {
  let address =
    (item.buildingName || item.buildName || '') +
    '' +
    (item.floorName || item.floorName || '') +
    '' +
    (item.deviceAddress || item.faultAddress || '')

  if (item.unitType != 0 && item.unitType) {
    address = (item.houseNumber || '') + (item.deviceAddress || '')
  }

  return address.trim() === '' ? '未采集' : address
}

export function addPointColor(item: any) {
  let bgColor = ''
  if (item == '火警') {
    bgColor = '#F30C0C'
  } else if (item == '故障') {
    bgColor = '#FD9905'
  } else if (item == '离线' || item == '未知') {
    bgColor = '#999999'
  } else if (item == '预警') {
    bgColor = '#9F1D8B'
  } else if (item == '隐患') {
    bgColor = '#F9E400'
  } else if (item == '动作') {
    bgColor = '#2476EE'
  } else if (item == '正常') {
    bgColor = '#52C41A'
  } else if (item == '正常监视') {
    bgColor = '#52C41A'
  } else if (item == '在线') {
    bgColor = '#0080FF'
  } else if (item == '未激活') {
    bgColor = '#BFBFBF'
  } else if (item == '待处置') {
    bgColor = '#FEA11C'
  } else if (item == '已处置') {
    bgColor = '#52C41A'
  } else if (item == '处置中') {
    bgColor = '#0080FF'
  } else if (item == '2') {
    bgColor = '#52C41A'
  } else if (item == '0') {
    bgColor = '#FEA11C'
  } else if (item == '1') {
    bgColor = '#0080FF'
  } else if (item == '3') {
    bgColor = '#F5222D'
  } else if (item == '0' || item == '') {
    bgColor = '#13A513'
  } else {
    bgColor = ''
  }
  return bgColor
}
export function addPointColorNew(item: any) {
  let bgColor = ''
  if (item == '1') {
    bgColor = '#F30C0C'
  } else if (item == '3') {
    bgColor = '#FD9905'
  } else if (item == '1') {
    bgColor = '#999999'
  } else if (item == '2') {
    bgColor = '#9F1D8B'
  } else if (item == '4') {
    bgColor = '#F9E400'
  } else if (item == '5') {
    bgColor = '#2476EE'
  } else if (item == '7') {
    bgColor = '#999999'
  } else if (item == '0' || '') {
    bgColor = '#2476EE'
  } else {
    bgColor = ''
  }
  return bgColor
}

export function isEmptyHandle(data) {
  let isEmpty = false
  if (Array.isArray(data)) {
    isEmpty = data.every((item) => parseFloat(item) == 0)
  } else {
    isEmpty = Object.keys(data).every((key) => {
      if (data[key].length == 0 || Object.keys(data[key]).length == 0) return true
      return parseFloat(data[key]) == 0
    })
    if (Object.keys(data).length === 0) isEmpty = true
  }

  return isEmpty
}

export function addJcquxShadowColor(item) {
  if (item == 'A') {
    return '#eaf4ed'
  }
  if (item == 'B') {
    return '#eaf4ed'
  }
  if (item == 'C') {
    return '#faf2f2'
  } else {
    return '#eaf4ed'
  }
}
export function addcommunicationCardColor(item) {
  if (item == '待服务' || item == '未开始') {
    return '#FEA11C'
  }
  if (item == '服务中' || item == '进行中') {
    return '#0080FF'
  }
  if (item == '已到期') {
    return '#BFBFBF'
  }
  if (item == '即将到期') {
    return '#BFBFBF'
  }
  if (item == '未开始') {
    return '#FEA11C'
  }
}
export function addOffPostColor(item) {
  if (item == '0' || item == '已离岗') {
    return '#BDBDBD'
  }
  if (item == '1' || item == '正常在岗') {
    return '#52C41A'
  }
}
export function normalizeEffectDate(item: any) {
  if (item.startTime == '--' && item.endTime == '--') {
    return '--'
  } else if (!item.startTime && !item.endTime) {
    return '--'
  } else {
    return (item.startTime.substring(0, 10) || '') + '~' + (item.endTime.substring(0, 10) || '')
  }
}
export function normalizeLoop(item: any) {
  const loop =
    (item.laMake || '') +
    (item.laLoop ? '-' + (item.laLoop || '') : '') +
    (item.laPoint ? '-' + (item.laPoint || '') : '')
  if (loop === '--') {
    return item.deviceClassification == 1 ? '未知' : '无'
  }
  return loop
}

export function nornalizefaultAddress(item: any) {
  if (!item.buildingName && !item.floorName && !item.faultAddress) {
    return '未知'
  } else {
    return (item.buildingName || '') + (item.floorName || '') + (item.faultAddress || '')
  }
}
export function nornalizehazardAddress(item: any) {
  if (!item.buildingName && !item.floorName && !item.hazardAddress) {
    return '未知'
  } else {
    return (item.buildingName || '') + (item.floorName || '') + (item.hazardAddress || '')
  }
}

export function isTibetUnit(unitId: string) {
  return unitId === '540102DZDA202206010001'
}

export function getRem(size: number, clientWidth = 1920) {
  clientWidth = document.documentElement.clientWidth
  return size / (clientWidth / 10)
}

// 位置
export function viewLocation(data) {
  PubSub.publish(types.OPEN_FLOOR_VIEW, data)
}

export function viewNineLocation(
  data: {
    x: number | string
    y: number | string
    title: string
  } = {
    x: '',
    y: '',
    title: '实时监测',
  }
) {
  PubSub.publish(types.OPEN_NICE_FLOOR_VIEW, data)
}

export function hasFloorMap(code: string | number) {
  return code == 2 || code == 3
}

export function setTempData(data: any) {
  temp_data.value = data
}
export function isShowViewLocation() {
  const ui = useUserInfo().value
  return ui.serviceModelCode === SERVICE_MODE.DRAWING || ui.serviceModelCode === SERVICE_MODE.TRUSTEESHIP
}
export function getTempData(isConsume = true) {
  try {
    return temp_data
  } finally {
    isConsume && nextTick(() => (temp_data.value = null))
  }
}

export function getTempVal(tempData) {
  if (isRef(tempData)) {
    return tempData.value
  }
  return tempData
}
export function download(url, fileName = '') {
  const a = document.createElement('a')
  const ev = document.createEvent('MouseEvents')
  ev.initEvent('click', false, true)
  a.href = url
  a.download = fileName ? url : fileName
  a.dispatchEvent(ev)
}

export function downloadPdf(url, fileName = '', title = '') {
  const index = fileName.lastIndexOf('/')
  fileName = fileName.substring(index + 1, fileName.length)
  const oReq = new XMLHttpRequest()
  oReq.open('GET', url, true)
  oReq.responseType = 'blob'
  oReq.onload = function () {
    const file = new Blob([oReq.response], {
      type: 'application/pdf',
    })
    const blobUrl = window.URL.createObjectURL(file)
    const a = document.createElement('a')
    a.href = blobUrl
    a.download = title ? title + '.pdf' : fileName
    a.click()
    // FileSaver.saveAs(file, fileName)
  }
  oReq.send()
}

export function handleMessageClick(item: Message, router: Router, isNotify = false) {
  const t = item.messageType.toString()
  if (
    isNotify &&
    item.deviceId &&
    (t == EVENT_TYPE.ACTION || t == EVENT_TYPE.ALARM || t == EVENT_TYPE.FAULT || t == EVENT_TYPE.WARING)
  ) {
    viewLocation(item)
  } else if (t == EVENT_TYPE.URGE) {
    const itemType = item.itemType!
    const obj: any = {
      tempId: item.businessId,
      eventType: itemType,
      eventSource: item.eventSource || item.itemType,
    }
    setTempData(obj)
    router.push(getPath(itemType as string)!)
  } else if (t == EVENT_TYPE.SINGLE_SEAL || t == EVENT_TYPE.SYSTEM) {
    // 单设备封停或系统通知
    // dialog({
    //     messageData: item
    // })
  } else if (t == EVENT_TYPE.SINGLE_SEAL || t == EVENT_TYPE.SERVICE_EXCEED) {
    // dialog({
    //     messageData: item
    // })
  } else {
    const obj: any = {
      tempId: item.businessId,
      eventType: t,
      eventSource: item.eventSource || item.itemType,
      subCenterCode: item.subCenterCode || '',
    }
    if (t == EVENT_TYPE.REFORM_SUGGESTION) {
      obj.eventSource = '4'
    }
    setTempData(obj)
    router.push(getPath(t)!)
  }
}

export function openMessageDetails(item: Message, router: Router) {
  if (item.messageType.toString() == '8') {
    const url = `/fireIntelligent-judgment/suspectedTruePolice?unitId=${item.unitId}&unitName=${item.unitName}&monitorReceiveTime=${item.monitorReceiveTime}`
    router.push(url)
    return
  }

  let eventType = item.messageType
  if (item.messageType == '106') {
    eventType = '4'
  }
  if (item.messageType == '111') {
    eventType = '3'
  }

  router.push({
    path: '/fireRemoteManage/eventHanding',
    query: setPageJsonQueryParamsAdapter({
      eventType,
      disposeId: item.disposeId || item.businessId,
    }),
  })
}

export function getPath(t: string) {
  switch (t) {
    case EVENT_TYPE.ALARM:
    case EVENT_TYPE.REAL_ALARM:
      return '/fire-supervision/treatment/alarm'
    case EVENT_TYPE.WARING:
      return '/fire-supervision/treatment/warning'
    case EVENT_TYPE.FAULT:
      return '/fire-supervision/treatment/fault'
    case EVENT_TYPE.ACTION:
      return '/fire-supervision/treatment/action'
    case EVENT_TYPE.HIDDEN_DANGER:
    case EVENT_TYPE.HIDDEN_DANGER_EXCEED:
    case EVENT_TYPE.REFORM_SUGGESTION:
      return '/fire-supervision/treatment/hidden-danger'
    case EVENT_TYPE.SAFETY_RISK:
      return '/fire-index-safety/assessment'
    case EVENT_TYPE.LEAVE_POST:
      return '/fire-control-safety/leave-post-record'
    case EVENT_TYPE.OFFLINE:
      return '/monitor/real-time-monitor/real-offline'
  }
}

export function getDeviceFieldList(data) {
  if (!data) {
    data = {
      produceInfo: {},
      installInfo: {},
    }
  }
  const deviceClass = data.deviceClassification
  const address = normalizeAddress(data)
  const loop = normalizeLoop(data)
  const deviceNum = data.deviceNum || '无'
  let temp: { label: string; value: any }[] = []
  if (typeof data.produceInfo === 'string') {
    data.produceInfo = JSON.parse(data.produceInfo || '{}')
  }

  if (typeof data.installInfo === 'string') {
    data.installInfo = JSON.parse(data.installInfo || '{}')
  }
  data.produceInfo = data.produceInfo || {}
  data.installInfo = data.installInfo || {}
  if (deviceClass == 1) {
    temp = [
      { label: '设备编号', value: data.deviceId || '无' },
      { label: '主机回路点位', value: loop },
      { label: '二次码', value: data.twoCode || '无' },
    ]
  } else if (deviceClass == 3 || deviceClass == 6) {
    temp = [{ label: 'IMEI', value: deviceNum }]
  } else {
    temp = [{ label: '设备编号', value: deviceNum }]
  }
  const base = [
    {
      label: '系统类型',
      value: data.deviceTypePname || data.deviceTypePName || '未知采集系统',
    },
    { label: '设备类型', value: data.deviceTypeName || '未知采集设备' },
    { label: '设备位置', value: address, isViewLocation: true },
    { label: '设备品牌', value: data.produceInfo.brand || '--' },
    { label: '规格型号', value: data.produceInfo.model || '--' },
    { label: '安装日期', value: data.installInfo.install_date || '--' },
  ]
  return temp.concat(base)
}

/**
 * 数据结构-树搜索-模拟模糊搜索
 * @param {key}     需要递归的key名
 * @param {value}   需要搜索的关键字
 * @param {treeList}    遍历tree列表
 * @return {saveList}   返回查询数组列表
 */
export function onFuzzyTreeList(key, value, treeList, saveList: any = []) {
  const list = treeList.value?.[0]?.isFirst ? treeList[0]?.children : treeList // 先判断是否是含有指标树的数据结构
  return new Promise((resolve) => {
    list?.forEach((item: any) => {
      if (item[key].indexOf(value) > -1) {
        saveList.push(item)
      } else {
        if (item.children && item.children.length > 0) {
          const _data: any = onFuzzyTreeList(key, value, item.children, saveList)
          if (_data && _data.length > 0) {
            saveList.push({
              ...item,
              children: _data,
            })
          }
        }
      }
    })
    resolve(saveList)
  })
}

// 防止浮点数相加精度丢失
export function floatAdd(...rest) {
  const args = rest
  let initFloatLen = 0
  let sum = 0

  let strItem = ''
  let tmpLen = 0
  for (const key in args) {
    strItem = args[key].toString()
    if (strItem.indexOf('.') != -1) {
      tmpLen = strItem.split('.')[1].length
      initFloatLen = initFloatLen < tmpLen ? tmpLen : initFloatLen
    }
  } // for
  const m = Math.pow(10, initFloatLen)
  for (const key in args) {
    sum += args[key] * m
  }
  return sum / m
}

export function sortAndCollectIds(_treeData, defaultExpandedKeys) {
  _treeData.sort((a, b) => {
    const aMatch = a.unitType == '0' || a.isEmpty == '0'
    const bMatch = b.unitType == '0' || b.isEmpty == '0'

    if (aMatch && !bMatch) {
      return -1 // a在b之前
    }
    if (!aMatch && bMatch) {
      return 1 // a在b之后
    }

    return 0 // 保持原顺序
  })

  for (const node of _treeData) {
    if ((node.unitType == '0' || node.isEmpty == '0') && !node.deviceId) {
      defaultExpandedKeys.push(node.superviseId)
    }
    if (node.child && node.child.length > 0) {
      sortAndCollectIds(node.child, defaultExpandedKeys)
    }
  }

  return _treeData
}

export function filterTreeData(_treeData) {
  const filteredData = <any[]>[]
  for (const node of _treeData) {
    if (node.unitType == '0' || node.isEmpty == '0') {
      filteredData.push(node)
    }
    if (node.child && node.child.length > 0) {
      const filteredChildren = filterTreeData(node.child)
      if (filteredChildren.length > 0) {
        node.child = filteredChildren
      }
    }
  }
  return filteredData
}

export const operateDisabled = computed(() => {
  return (item, field) => {
    let flag = false
    if (!item || !field || !item[field]) flag = true
    else if (item[field]) {
      const fileSuffix = item[field].substring(item[field].lastIndexOf('.') + 1)
      if (['pdf', 'png', 'jpg', 'jpeg'].includes(fileSuffix)) flag = false
      else flag = true
    }
    return flag
  }
})

/**
 * 将像素值转换为 rem 单位
 * @param {string} px - 需要转换的像素值，可以是带单位的字符串（如 '16px'）或纯数字字符串
 * @returns {string} 转换后的 rem 值，如果输入包含百分号则原样返回
 * @example
 * px2rem('16')   // 返回 '0.08333333333333333rem'
 * px2rem('16px') // 返回 '0.08333333333333333rem'
 * px2rem('50%')  // 返回 '50%'
 */
export function px2rem(px: string): string {
  // 检查是否为百分比值，是则直接返回
  if (/%/gi.test(px)) {
    return px
  } else {
    // 将像素值转换为 rem（基于设计稿 1920px，1rem = 192px）
    return parseFloat(px) / 192 + 'rem'
  }
}
