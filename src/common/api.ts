import Axios, { AxiosRequestConfig } from 'axios'
import config from '~/config'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { useUserInfo } from '@/store'
import router from '~/router'
import { addSignal, getSignal, delSignal } from '@/hooks/controller/cancel'
import { loginPath } from '@/router/loginPath'

export type RequestConfig = AxiosRequestConfig & {
  loading?: boolean
}

const axios = Axios.create({
  timeout: 1000 * 60 * 10,
  // cancelToken: axios.CancelToken.source(),
  headers: {
    // 'Content-Type': 'application/json'
  },
  paramsSerializer(params = {}) {
    Object.keys(params).forEach((key) => {
      const item = params[key]
      if (typeof item === 'object') {
        params[key] = JSON.stringify(item)
      }
    })
    return new URLSearchParams(params).toString()
  },
})
const filterPrefix = ['/img1', `/${config.root_dir}`]
axios.interceptors.request.use((payload) => {
  if (!payload.url?.startsWith('/')) {
    payload.url = '/' + payload.url
  }
  if (!payload.url?.startsWith('/api') && !filterPrefix.some((p) => payload.url?.startsWith(p))) {
    payload.url = config.base_url + payload.url
  }
  // payload.signal = controller.signal

  addSignal(payload.url)
  payload.signal = getSignal(payload.url).signal
  const userInfo = useUserInfo().value
  payload.headers = {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json',
    // 'gst-token': userInfo.userToken || '',
    'org-code': userInfo.orgCode || '',
    ...payload.headers,
  }
  return payload
}, handleError)

axios.interceptors.response.use((response) => {
  delSignal(response.config.url!)
  if (redirectLogin(response)) {
    if (response.request.responseType === 'blob') {
      // blob请求时，返回包含头部信息的response
      return response
    } else {
      return response.data
    }
  }
}, handleError)

axios.interceptors.response.use((response: any) => {
  if (isResponseModel(response) && response.code !== 'success') {
    const msg = response.message ? response.message : '服务错误'
    ElMessage.error(msg)
  }
  return response
})

const $API = {
  post(config: AxiosRequestConfig) {
    config.method = 'post'

    if (config.params) {
      const keys = Object.keys(config.params)
      keys.forEach((i) => {
        if (config.params[i] === undefined) config.params[i] = ''
      })
    }
    return axios(config)
  },

  get(config: AxiosRequestConfig) {
    config.method = 'get'
    // config.signal = controller

    return axios(config)
  },

  formData(requestConfig: AxiosRequestConfig) {
    const config: AxiosRequestConfig = Object.assign(requestConfig, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      },
      data: paramsToFormData(requestConfig.data || requestConfig.params),
    })

    return $API.post(config)
  },
  // 前一个月的日期范围
  initMonthDate() {
    const old = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
    const today = dayjs().format('YYYY-MM-DD')
    return [old, today]
  },
  // 昨天
  yesterday() {
    return dayjs().subtract(1, 'day').format('YYYY-MM-DD')
  },
  // 今天
  todayForDate() {
    return dayjs().format('YYYY-MM-DD')
  },
  // 今天
  today() {
    return dayjs().format('YYYY-MM-DD HH:mm:ss')
  },
  // 下一个半点
  nextHalfHour() {
    const now = dayjs()
    const nextHalfHourTime = now.startOf('hour').add(30, 'minute')
    if (now.isAfter(nextHalfHourTime)) {
      return dayjs(nextHalfHourTime.add(0.5, 'hour')).format('YYYY-MM-DD HH:mm:ss')
    }
    return dayjs(nextHalfHourTime).format('YYYY-MM-DD HH:mm:ss')
  },
  laskWeek() {
    return dayjs().subtract(1, 'week').format('YYYY-MM-DD')
  },
  lastMonth() {
    return dayjs().subtract(1, 'month').format('YYYY-MM-DD')
  },
  // 去年
  lastYear() {
    return dayjs().subtract(1, 'year').format('YYYY-MM-DD')
  },
  // 前一天的日期范围  HH:mm:ss
  lastDay() {
    const old = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    const today = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    return [old, today]
  },
  getDays(num = 0) {
    const old = dayjs().subtract(num, 'day').format('YYYY-MM-DD')
    const today = dayjs().format('YYYY-MM-DD')
    return [old, today]
  },
  getDayTime() {
    const dayjsTime = dayjs(`${new Date()}`).format('YYYY-MM-DD HH:mm:ss')
    return dayjsTime
  },
  getTimeRange(timeRange: any = '1', str = 'YYYY-MM-DD HH:mm:ss') {
    // timeRange 1: 近一周 2: 近一月 3: 近半年 默认值为7天
    // 获取当前日期
    const currentDate = dayjs().subtract(1, 'day')
    // 获取5个月前的日期
    const sixMonthsAgo = currentDate.subtract(5, 'months')
    const lastMounth = dayjs().subtract(1, 'month').endOf('month')
    const lastHalfAYear = lastMounth.subtract(5, 'months').startOf('month')
    // 获取本月的月初日期
    const startOfMonth = currentDate.startOf('month')
    // 计算距离月初有多少天，包括当天
    const daysUntilStartOfMonth = currentDate.diff(startOfMonth, 'day')
    // 计算总天数，包括当前月，每过1天算一个月
    const tNum = currentDate.diff(sixMonthsAgo, 'day') + daysUntilStartOfMonth
    const num = timeRange == '1' ? 6 : timeRange == '2' ? 29 : timeRange == '3' ? tNum : 6

    const today = currentDate.endOf('date').format(str)
    const old = currentDate.startOf('date').subtract(num, 'day').format(str)
    if (timeRange == 3) {
      return [lastHalfAYear.format(str), lastMounth.format(str)]
    }

    return [old, today]
  },

  /**
   * 根据类型获取日期范围
   * @param type 1: 今日, 2: 本月, 3: 本年
   * @param format 日期格式，默认 'YYYY-MM-DD'
   * @returns [开始日期, 结束日期]
   */
  getTypeDaysInMonth(type: '1' | '2' | '3' | '0', format = 'YYYY-MM-DD'): [string, string] {
    const now = dayjs()
    switch (type) {
      case '1': // 今日
        return [now.startOf('day').format(format), now.endOf('day').format(format)]
      case '2': // 本月
        return [now.startOf('month').format(format), now.endOf('month').format(format)]
      case '3': // 本年
        return [now.startOf('year').format(format), now.endOf('year').format(format)]
      case '0': //当前
        return ['', '']
    }
  },

  getDaysInMonth(day) {
    const monthDate = dayjs(day)
    // 获取传入月份的起始日期和截止日期
    const startOfMonth = monthDate.startOf('month')
    let endOfMonth = monthDate.endOf('month')
    const currentMonth = dayjs().format('YYYY-MM') // 获取当前月份
    if (day === currentMonth) endOfMonth = dayjs()
    return [startOfMonth.format('YYYY-MM-DD'), endOfMonth.format('YYYY-MM-DD')]
  },
  fomartNum(num: any = 0) {
    if (typeof num == 'string') {
      num = num.replace('%', '')
    }
    if (isNaN(Number(num))) {
      // 非数字
      console.error('foomartNum--' + num + '非数字类型且无法转为数字')
      return num
    }
    const numNew = Number(num)
    if (numNew % 1 === 0) {
      return numNew
    } else {
      return (parseInt(`${numNew * 100}`) / 100).toFixed(2)
    }
  },
  jump(url, params = {}) {
    if (!url) return
    router.push({
      path: url,
      query: {
        ...params,
      },
    })
  },
}

//后台返回token请求，重定向到登录页
function redirectLogin(response) {
  //未经授权或token过期；
  // const base_url=config.base_host;
  if (response.data.code == 'tokenError') {
    localStorage.removeItem('userToken')
    router.push(loginPath.value)
    return false
  } else {
    if (response.headers['gst-refresh-token']) {
      localStorage.setItem('userToken', response.headers['gst-refresh-token'])
    }
    return true
  }
}

function paramsToFormData(obj) {
  const formData = new FormData()
  Object.keys(obj).forEach((key) => {
    if (obj[key] instanceof Array) {
      obj[key].forEach((item) => {
        formData.append(key, item)
      })
      return
    }
    formData.append(key, obj[key])
  })
  return formData
}

function handleError(error: any) {
  if (error.message === 'canceled') {
    return Promise.reject(error)
  }

  if (error.message.includes('timeout')) {
    ElMessage.error({
      message: '请求超时',
    })
  } else {
    if (error.response && error.response.status) {
      errorHandle(error.response.status)
    } else {
      ElMessage.error({
        message: '服务错误',
      })
    }
  }
  return Promise.reject(error)
}

// 状态码判断
function errorHandle(status: any) {
  switch (status) {
    case 400:
      ElMessage.error({
        message: '400错误',
      })
      break
    case 401:
      ElMessage.error({
        message: '401错误',
      })
      break
    case 404:
      ElMessage.error({
        message: '404错误，请求的资源不存在',
      })
      break
    case 500:
      ElMessage.error({
        message: '500错误，服务器内部错误',
      })
      break
    case 503:
      ElMessage.error({
        message: '服务错误',
      })
      break
    default:
      ElMessage.error({
        message: '服务器错误',
      })
      break
  }
}

function isResponseModel(obj): boolean {
  if (obj?.constructor != Object) return true
  return obj && 'code' in obj && 'data' in obj && 'message' in obj
}

export default $API
