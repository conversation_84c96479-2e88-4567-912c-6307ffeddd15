/**
 * 动态计算搜索区域高度，并确定是否添加展开收起功能按钮
 * 需要搜索区域添加 main_search 类名,操作栏需添加action类名
 */

const columns = 4 // 搜索区域列数

// 上下箭头svg
const top: any = `<span role="img" aria-label="up" class="anticon anticon-up" style="margin-left: 3px;" data-v-89ce1e02=""><svg focusable="false" class="" data-icon="up" width="1em" height="1em" fill="currentColor" aria-hidden="true" viewBox="64 64 896 896"><path d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"></path></svg></span>`
const down: any = `<span role="img" aria-label="down" class="anticon anticon-down" style="margin-left: 3px;" data-v-89ce1e02=""><svg focusable="false" class="" data-icon="down" width="1em" height="1em" fill="currentColor" aria-hidden="true" viewBox="64 64 896 896"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg></span>`

let searchDomWidth: any = '' // 搜索项宽度
let actionDomWidth: any = '' // 操作按钮区域宽度

const limitLines = 1 // 超出几行需要隐藏

const className = 'expand-put-away-action' // 展开收起按钮class名称

let expandOrPutAwayBtnText = '' // 收起展开按钮文字

// 搜索区域 展开 or 收起  处理
function mainSearchDomDeal(options?: {
  rootDomClassName?: string
  index?: number
}) {
  if (!options) {
    options = {
      rootDomClassName: '',
      index: 0
    }
  }
  let { rootDomClassName, index } = options

  if (!rootDomClassName) {
    rootDomClassName = 'main_search'
  }
  if (!index) {
    index = 0
  }

  let doms: any = [null]
  doms = document.querySelectorAll('.' + rootDomClassName)

  const dom = doms[index]

  if (dom) {
    getChildWidth(dom)
    const { serachItemSum, childs, startIndex } = getDomInfo(dom)

    let actionDom: any = null

    for (let index = 0; index < dom.children.length; index++) {
      const childClassName = dom.children[index].getAttribute('class')

      if (childClassName.includes('expand-put-away-action')) {
        // 展开收起的dom
        actionDom = dom.children[index]
      }
    }

    // 初始化判断需不需要添加展开按钮
    if (startIndex !== serachItemSum) {
      // 超过n行
      if (!actionDom) {
        // 隐藏需要隐藏的搜索项
        // for (let index = startIndex; index < serachItemSum; index++) {
        //   childs[index].style.display = 'none'
        // }

        // 添加收起按钮
        const actionDiv: any = document.createElement('div') // 按钮父元素
        actionDiv.style['flex'] = 0
        actionDiv.setAttribute('class', `form_item_btn ${className} ml-10px`)
        const btn: any = document.createElement('button')
        // expandOrPutAwayBtnText = '展开'
        // btn.innerHTML = expandOrPutAwayBtnText + down
        expandOrPutAwayBtnText = '收起'
        btn.innerHTML = expandOrPutAwayBtnText + top
        btn.setAttribute('class', `${className}`)
        // 设置点击事件
        btn.onclick = function () {
          if (expandOrPutAwayBtnText === '展开') {
            expand(btn)
          } else if (expandOrPutAwayBtnText === '收起') {
            putAway(btn)
          }
        }
        actionDiv.appendChild(btn)
        dom.appendChild(actionDiv)
      } else {
        if (doms.length === 1) {
          // 添加过按钮，默认设置为显示，防止因tab切换时已经将该dom设置为隐藏的问题
          // actionDom.style.display = 'block'

          // 这里用于再次返回时，保持原样不动
          if (expandOrPutAwayBtnText === '展开') {
            for (let index = startIndex; index < serachItemSum; index++) {
              childs[index].style.display = 'none'
            }
          }
        } else {
          // 有多个dom时，不须做任何操作
        }
      }

      // 收起
      function putAway(btn: any) {
        const { serachItemSum, childs, startIndex } = getDomInfo(dom) // 重新获取index
        for (let index = startIndex; index < serachItemSum; index++) {
          if (!childs[index].className.includes('action')) {
            childs[index].style.display = 'none' // 隐藏
          }
        }

        // 修改按钮信息
        expandOrPutAwayBtnText = '展开'
        btn.innerHTML = expandOrPutAwayBtnText + down
      }
      // 展开
      function expand(btn: any) {
        const { serachItemSum, childs, startIndex } = getDomInfo(dom) // 重新获取index

        for (let index = startIndex; index < serachItemSum; index++) {
          childs[index].style.display = 'block' // 展示
          if (childs[index].className.includes('action')) {
            childs[index].style.textAlign = 'right' // 展示
          }
        }

        // 修改按钮信息
        expandOrPutAwayBtnText = '收起'
        btn.innerHTML = expandOrPutAwayBtnText + top
      }
    } else {
      // 没有超过n行
      if (actionDom) {
        // 如果已经存在，隐藏起来
        actionDom.style.display = 'none'
      }
    }
  }
}

// 查看dom信息
function getDomInfo(dom: any) {
  const childs: any = dom?.children

  let needPutAwayDom: any = [] // 需要隐藏的dom集合

  for (let index = 0; index < childs.length; index++) {
    const child: any = childs[index]

    const childClassName = child.getAttribute('class')

    if (
      childClassName === 'form_item' ||
      childClassName.includes('form_item')
    ) {
      // 搜索项
      needPutAwayDom.push(child)
    }
  }

  const serachItemSum = needPutAwayDom?.length //搜索项dom集合子元素个数

  // 搜索区域宽度每超过一个搜索项宽度，需要隐藏的搜索项就要多一个
  let overWidth = Math.floor(actionDomWidth / searchDomWidth)
  let startIndex = serachItemSum

  if (dom.children.length + overWidth > columns * limitLines) {
    // 需要隐藏掉部分元素
    startIndex = columns * limitLines - 1 // 需要隐藏的元素起始index

    if (overWidth > 0) {
      startIndex -= overWidth
    }
  }

  return {
    serachItemSum,
    columns,
    childs,
    startIndex
  }
}

// 获取搜索区域每项宽度
function getChildWidth(dom: any) {
  const childs: any = dom?.children

  for (let index = 0; index < childs.length; index++) {
    const child: any = childs[index]

    const childWidth = child.getBoundingClientRect().width

    const childClassName = child.getAttribute('class')

    if (
      (childClassName === 'form_item' ||
        childClassName.includes('form_item')) &&
      index === 0
    ) {
      // 搜索项
      searchDomWidth = childWidth
    }

    if (childClassName === 'form_item_btn') {
      // 操作按钮区域
      const btnsLength = child.children.length
      let sumWidth = 0
      for (let childIndex = 0; childIndex < btnsLength; childIndex++) {
        const btn = child.children[childIndex] // 子元素
        const btnWidth = btn.getBoundingClientRect().width
        sumWidth += btnWidth
      }
      sumWidth += 15 * (btnsLength - 1) + 80 // 子元素的宽度, 这里为什么加上15，因为子元素之间有间距，暂且假设间距为15;为什么加上80，因为展开收起区域宽度大概在80左右

      actionDomWidth = sumWidth // 所有子元素的宽度
    }
  }
}

export default mainSearchDomDeal
