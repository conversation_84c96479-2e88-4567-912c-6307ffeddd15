import { Message } from '~/types'

export enum ROLE_GROUP {
  ADMIN = '1508632244839190530',

  SAFETY_MANAGE = '1508733414867021826',

  DUTY = '1515872239323451393',

  WORKER = '1508634027368058881',
  //企业领导 & 管理员
  MANAGE_ADMIN = '1535505308649127937',
  //B+C领导
  B_TO_C_MANAGE = '1535508095369871362',
  //B+C领导&管理员
  B_TO_C_MANAGE_ADMIN = '1535508130383921154',
}

export enum EVENT_TYPE {
  /** 火警 */
  ALARM = '1',
  /** 预警 */
  WARING = '2',
  /** 故障 */
  FAULT = '3',
  /** 隐患 */
  HIDDEN_DANGER = '4',
  /** 动作 */
  ACTION = '5',
  /** 离线 */
  OFFLINE = '7',
  /** 高度疑似真警 */
  MAYBE_ALARM = '8',
  /** 屏蔽 */
  SHIELD = '9',
  /** 普通(未知) */
  NORMAL = '0',
  /** 催促 */
  URGE = '101',
  /** 真警 */
  REAL_ALARM = '102',
  /** 安全风险提醒 */
  SAFETY_RISK = '103',
  /** 单设备封停 */
  SINGLE_SEAL = '104',
  /** 系统通知 */
  SYSTEM = '105',
  /** 隐患超期 */
  HIDDEN_DANGER_EXCEED = '106',
  /** 离岗 */
  LEAVE_POST = '107',
  /** 服务到期提醒 */
  SERVICE_EXCEED = '108',
  /** 整改意见 */
  REFORM_SUGGESTION = '109',
  /** 通知通告 */
  Notification_Notice = '501',
  /** 超期故障 */
  FAULT_EXCEED = '111',
}

export const EVENT_LEVEL = {
  [EVENT_TYPE.OFFLINE]: 0,
  [EVENT_TYPE.ALARM]: 1,
  [EVENT_TYPE.WARING]: 2,
  [EVENT_TYPE.ACTION]: 3,
  [EVENT_TYPE.FAULT]: 4,
  [EVENT_TYPE.HIDDEN_DANGER]: 5,
  [EVENT_TYPE.NORMAL]: 6,
}

export const EVENT_LABEL = {
  [EVENT_TYPE.ALARM]: '火警',
  [EVENT_TYPE.WARING]: '预警',
  [EVENT_TYPE.FAULT]: '故障',
  [EVENT_TYPE.ACTION]: '动作',
  [EVENT_TYPE.OFFLINE]: '离线',
  [EVENT_TYPE.HIDDEN_DANGER]: '隐患',
  [EVENT_TYPE.URGE]: '催促',
  [EVENT_TYPE.REAL_ALARM]: '真警提醒',
  [EVENT_TYPE.SAFETY_RISK]: '安全风险',
  [EVENT_TYPE.SINGLE_SEAL]: '单设备封停',
  [EVENT_TYPE.SYSTEM]: '系统通知',
}

export const EVENT_MESSAGE_TITLE = {
  [EVENT_TYPE.ALARM || EVENT_TYPE.REAL_ALARM]: '火警提醒',
  [EVENT_TYPE.MAYBE_ALARM]: '高度疑似真警',
  [EVENT_TYPE.WARING]: '预警提醒',
  [EVENT_TYPE.FAULT]: '故障提醒',
  [EVENT_TYPE.ACTION]: '动作提醒',
  [EVENT_TYPE.OFFLINE]: '主机失联提醒',
  [EVENT_TYPE.HIDDEN_DANGER]: '隐患提醒',
  [EVENT_TYPE.URGE]: '催促提醒',
  [EVENT_TYPE.SAFETY_RISK]: '安全风险提醒',
  [EVENT_TYPE.HIDDEN_DANGER_EXCEED]: '隐患超期提醒',
  [EVENT_TYPE.FAULT_EXCEED]: '故障超期提醒',
  [EVENT_TYPE.REAL_ALARM]: '真警通知',
  [EVENT_TYPE.LEAVE_POST]: '消控室离岗通知',
  [EVENT_TYPE.SYSTEM]: '系统通知',
  [EVENT_TYPE.REFORM_SUGGESTION]: '整改意见',
  [EVENT_TYPE.SHIELD]: '屏蔽提醒',
  [EVENT_TYPE.Notification_Notice]: '通知通告',
}

export const EVENT_ICON = {
  [EVENT_TYPE.ALARM]: getIcon(EVENT_TYPE.ALARM),
  [EVENT_TYPE.WARING]: getIcon(EVENT_TYPE.WARING),
  [EVENT_TYPE.FAULT]: getIcon(EVENT_TYPE.FAULT),
  [EVENT_TYPE.ACTION]: getIcon(EVENT_TYPE.ACTION),
  [EVENT_TYPE.OFFLINE]: getIcon(EVENT_TYPE.OFFLINE),
  [EVENT_TYPE.HIDDEN_DANGER]: getIcon(EVENT_TYPE.HIDDEN_DANGER),
  [EVENT_TYPE.URGE]: getIcon(EVENT_TYPE.URGE),
  [EVENT_TYPE.SAFETY_RISK]: getIcon(EVENT_TYPE.SAFETY_RISK),
  [EVENT_TYPE.SINGLE_SEAL]: getIcon(EVENT_TYPE.SINGLE_SEAL),
  [EVENT_TYPE.SYSTEM]: getIcon(EVENT_TYPE.SYSTEM),
  [EVENT_TYPE.HIDDEN_DANGER_EXCEED]: getIcon(EVENT_TYPE.HIDDEN_DANGER_EXCEED),
  [EVENT_TYPE.FAULT_EXCEED]: getIcon(EVENT_TYPE.FAULT_EXCEED),
  [EVENT_TYPE.SERVICE_EXCEED]: getIcon(EVENT_TYPE.SERVICE_EXCEED),
  [EVENT_TYPE.REAL_ALARM]: getIcon(EVENT_TYPE.REAL_ALARM),
  [EVENT_TYPE.LEAVE_POST]: getIcon(EVENT_TYPE.LEAVE_POST),
  [EVENT_TYPE.REFORM_SUGGESTION]: getIcon(EVENT_TYPE.REFORM_SUGGESTION),
  [EVENT_TYPE.MAYBE_ALARM]: getIcon(EVENT_TYPE.MAYBE_ALARM),
  [EVENT_TYPE.SHIELD]: getIcon(EVENT_TYPE.SHIELD),
}

export const MESSAGE_RULE = {
  [ROLE_GROUP.SAFETY_MANAGE]: [
    EVENT_TYPE.REAL_ALARM,
    EVENT_TYPE.SAFETY_RISK,
    EVENT_TYPE.SYSTEM,
    EVENT_TYPE.HIDDEN_DANGER_EXCEED,
    EVENT_TYPE.SERVICE_EXCEED,
    EVENT_TYPE.LEAVE_POST,
    EVENT_TYPE.REFORM_SUGGESTION,
  ],

  [ROLE_GROUP.WORKER]: [
    EVENT_TYPE.ALARM,
    EVENT_TYPE.WARING,
    EVENT_TYPE.ACTION,
    EVENT_TYPE.OFFLINE,
    EVENT_TYPE.FAULT,
    EVENT_TYPE.HIDDEN_DANGER,
    EVENT_TYPE.HIDDEN_DANGER_EXCEED,
    EVENT_TYPE.REAL_ALARM,
    EVENT_TYPE.SYSTEM,
    EVENT_TYPE.URGE,
    EVENT_TYPE.SINGLE_SEAL,
    EVENT_TYPE.SERVICE_EXCEED,
    EVENT_TYPE.REFORM_SUGGESTION,
    EVENT_TYPE.MAYBE_ALARM,
    EVENT_TYPE.SHIELD,
  ],
}

MESSAGE_RULE[ROLE_GROUP.DUTY] = MESSAGE_RULE[ROLE_GROUP.SAFETY_MANAGE]
MESSAGE_RULE[ROLE_GROUP.MANAGE_ADMIN] = MESSAGE_RULE[ROLE_GROUP.SAFETY_MANAGE]

export const SERVICE_MODE = {
  /** 免费模式 */
  FREE: '0',
  /** 监测模式 */
  MONITOR: '1',
  /** 图纸模式 */
  DRAWING: '2',
  /** 全托管模式 */
  TRUSTEESHIP: '3',
  /** 转接模式 */
  TRANSFER: '4',
  /** 列表模式 */
  LIST: '5',
  /** 监管模式 */
  SUPERVISE: '6',
}

export interface EventData {
  label: string
  messageTitle: string
  icon?: string
}

export function getEventData(eventType: EVENT_TYPE): EventData {
  return {
    label: EVENT_LABEL[eventType],
    messageTitle: EVENT_MESSAGE_TITLE[eventType],
    icon: EVENT_ICON[eventType],
  }
}

export function getPagePath(item: Message): string {
  const t = item.messageType
  const isUrge = t == EVENT_TYPE.URGE
  if (t == EVENT_TYPE.ALARM) {
    return '/treatment/alarm'
  } else if (t == EVENT_TYPE.WARING || (isUrge && item.itemType == EVENT_TYPE.WARING)) {
    return '/treatment/warning'
  } else if (t == EVENT_TYPE.FAULT || (isUrge && item.itemType == EVENT_TYPE.FAULT)) {
    return '/treatment/fault'
  } else if (t == EVENT_TYPE.ACTION || (isUrge && item.itemType == EVENT_TYPE.ACTION)) {
    return '/treatment/action'
  } else if (t == EVENT_TYPE.HIDDEN_DANGER || (isUrge && item.itemType === EVENT_TYPE.HIDDEN_DANGER)) {
    return '/treatment/hidden-danger'
  } else if (t == EVENT_TYPE.LEAVE_POST) {
    return '/fire-control-safety/leave-post-record'
  }
  return ''
}

export function isDispose(t: string) {
  return t == EVENT_TYPE.ALARM || t == EVENT_TYPE.WARING || t == EVENT_TYPE.FAULT || t == EVENT_TYPE.ACTION
}

function getIcon(eventType: EVENT_TYPE) {
  return `./images/messgae/icon_${eventType}.png`
}
