import { MenuItem } from '@/types'

export const menuData: MenuItem[] = [
  // {
  //   resIcon: "homePage",
  //   resName: "值班人首页",
  //   resUrl: "/home-page",
  //   id: "0",
  // },
  // {
  //   resIcon: "homePage",
  //   resName: "负责人首页",
  //   name: "principalPage",
  //   id: "99",
  // },
  {
    resIcon: 'overview',
    resName: '首页',
    resUrl: '/overview',
    id: '0'
  },
  {
    resIcon: 'monitor',
    resName: '设备监测',
    resUrl: '/monitor',
    id: '1',
    children: [
      {
        resName: '实时监测',
        name: 'realTimeMonitor',
        resUrl: '/monitor/real-time-monitor',
        id: '1-0',
        children: [
          {
            resName: '火警',
            // name: "equipmentShutdown",
            resUrl: '/monitor/real-time-monitor/fire-alarm',
            id: '1-0-1'
          },
          {
            resName: '预警',
            id: '1-0-2',
            resUrl: '/monitor/real-time-monitor/early-warning'
          },
          {
            resName: '动作',
            id: '1-0-3',
            resUrl: '/monitor/real-time-monitor/action-ye'
          },
          {
            resName: '故障',
            id: '1-0-4',
            resUrl: '/monitor/real-time-monitor/real-fault'
          },
          {
            resName: '隐患',
            id: '1-0-5',
            resUrl: '/monitor/real-time-monitor/real-hazard'
          },
          {
            resName: '在离线',
            id: '1-0-6',
            resUrl: '/monitor/real-time-monitor/real-offline'
          }
        ]
      },
      {
        resName: '消防系统运行监测',
        name: 'fireSystemMonitor',
        resUrl: '/monitor/fire-system-monitor',
        id: '1-1'
      },
      {
        resName: '视频监控',
        name: 'visualizationMonitor',
        resUrl: '/monitor/visualization-monitor',
        id: '1-2'
        // children: [
        //   {
        //     resName: '消防系统运行监测',
        //     resUrl: '/monitor/fire-system-monitor/fire-system-monitor-list',
        //     id: '1-1-1'
        //   }
        // ]
      }
      // {
      //   resName: "视频监控",
      //   name: "videoSurveillance",
      //   id: "1-2",
      // },
      // {
      //   resName: "设备封停",
      //   id: "1-3",
      //   children: [
      //     {
      //       resName: "单设备封停",
      //       name: "equipmentShutdown",
      //       id: "1-3-0",
      //       children: [
      //      {
      //       resName: "单设备封停操作页",
      //      name: "shutDownOperation",
      //      id: "1-3-0-1"
      //     }
      //     ]
      //     },
      //     {
      //       resName: "设备批量封停",
      //       id: "1-3-1",
      //     },
      //   ],
      // },
    ]
  },
  {
    resIcon: 'fireSupervision',
    resName: '消防监督',
    resUrl: '/fire-supervision',
    id: '2',
    children: [
      {
        resName: '事件中心',
        name: 'treatment',
        resUrl: '/fire-supervision/treatment',
        id: '2-0',
        children: [
          {
            resName: '火警',
            name: 'treatment_alarm',
            resUrl: '/fire-supervision/treatment/alarm',
            id: '2-0-0'
          },
          {
            resName: '预警',
            name: 'treatment_warning',
            resUrl: '/fire-supervision/treatment/warning',
            id: '2-0-1'
          },
          {
            resName: '故障',
            name: 'treatment_fault',
            resUrl: '/fire-supervision/treatment/fault',
            id: '2-0-2'
          },
          {
            resName: '动作',
            name: 'treatment_action',
            resUrl: '/fire-supervision/treatment/action',
            id: '2-0-3'
          },
          {
            resName: '隐患',
            name: 'treatment_hidden_danger',
            resUrl: '/fire-supervision/treatment/hidden-danger',
            id: '2-0-4'
          }
          // {
          //   resName: '动作',
          //   name: 'controlRoomDutyResignationRecord',
          //   resUrl: '/fire-supervision/control-room-duty/resignation-record/index',
          //   id: '2-7-3'
          // },
          // {
          //   resName: '隐患',
          //   name: 'controlRoomDutyResignationRecord',
          //   resUrl: '/fire-supervision/control-room-duty/resignation-record/index',
          //   id: '2-7-4'
          // },
        ]
      },
      {
        resName: '消控室值班',
        name: 'controlRoomDuty',
        resUrl: '/fire-supervision/control-room-duty',
        id: '2-1',
        children: [
          {
            resName: '消控室值班计划',
            name: 'controlRoomDutyPlan',
            resUrl: '/fire-supervision/control-room-duty/plan/index',
            id: '2-1-0'
          },
          {
            resName: '消控室值班任务',
            name: 'controlRoomDutyTask',
            resUrl: '/fire-supervision/control-room-duty/task/index',
            id: '2-1-1'
          },
          {
            resName: '消控室人员在离岗记录',
            name: 'controlRoomDutyResignationRecord',
            resUrl:
              '/fire-supervision/control-room-duty/resignation-record/index',
            id: '2-1-2'
          }
        ]
      },
      {
        resName: '防火巡查',
        name: 'fireInspection',
        resUrl: '/fire-supervision/fire-inspection',
        id: '2-2',
        children: [
          {
            resName: '防火巡查计划',
            name: 'fireInspectionPlan',
            resUrl: '/fire-supervision/fire-inspection/plan/index',
            id: '2-2-0'
          },
          {
            resName: '防火巡查任务',
            name: 'fireInspectionTask',
            resUrl: '/fire-supervision/fire-inspection/task/index',
            id: '2-2-1'
          }
        ]
      },
      {
        resName: '防火检查',
        name: 'fireChecking',
        resUrl: '/fire-supervision/fire-checking',
        id: '2-3',
        children: [
          {
            resName: '防火检查计划',
            name: 'fireCheckingPlan',
            resUrl: '/fire-supervision/fire-checking/plan/index',
            id: '2-3-0'
          },
          {
            resName: '防火检查任务',
            name: 'fireCheckingTask',
            resUrl: '/fire-supervision/fire-checking/task/index',
            id: '2-3-1'
          }
        ]
      },
      {
        resName: '建筑消防设施',
        name: 'buildingFirefightingFacilitiesChecking',
        resUrl: '/fire-supervision/building-firefighting-facilities-checking',
        id: '2-4',
        children: [
          {
            resName: '设施巡查计划',
            name: 'buildingFirefightingFacilitiesCheckingPlan',
            resUrl:
              '/fire-supervision/building-firefighting-facilities-checking/plan/index',
            id: '2-4-0'
          },
          {
            resName: '设施巡查任务',
            name: 'buildingFirefightingFacilitiesCheckingTask',
            resUrl:
              '/fire-supervision/building-firefighting-facilities-checking/task/index',
            id: '2-4-1'
          }
        ]
      },
      {
        resName: '消防设施维护保养',
        name: 'fireFacilitiesMaintenance',
        resUrl: '/fire-supervision/fire-facilities-maintenance',
        id: '2-5',
        children: [
          {
            resName: '例行维保',
            name: 'routineMaintenance',
            resUrl:
              '/fire-supervision/fire-facilities-maintenance/routine-maintenance/index',
            id: '2-5-1'
          },
          {
            resName: '预约维保',
            name: 'appointmentMaintenance',
            resUrl:
              '/fire-supervision/fire-facilities-maintenance/appointment-maintenance/index',
            id: '2-5-2'
          },
          {
            resName: '应急维修',
            name: 'emergencyMaintenance',
            resUrl:
              '/fire-supervision/fire-facilities-maintenance/emergency-maintenance/index',
            id: '2-5-3'
          },
          {
            resName: '维保合同',
            name: 'maintenanceContract',
            resUrl:
              '/fire-supervision/fire-facilities-maintenance/maintenance-contract/index',
            id: '2-5-4'
          }
        ]
      },
      {
        resName: '火灾',
        name: 'fire',
        resUrl: '/fire-supervision/fire',
        id: '2-6',
        children: [
          {
            resName: '火灾记录',
            name: 'fireRecord',
            resUrl: '/fire-supervision/fire/fire-record/index',
            id: '2-6-1'
          }
        ]
      }
    ]
  },

  {
    resIcon: 'society',
    resName: '社会单位管理',
    resUrl: '/societyManage',
    id: '3',
    children: [
      {
        resName: '企业单位',
        name: 'enterprises',
        resUrl: '/societyManage/societymanage-enterprise',
        id: '3-0'
      },
      {
        resName: '九小场所/家庭',
        name: 'nineFamily',
        resUrl: '/societyManage/societymanage-nine-family/index',
        id: '3-1'
      }
    ]
  },

  {
    resIcon: 'society',
    resName: '设备【基础设施】',
    resUrl: '/equipmentInfrastructure',
    id: '4',
    children: [
      {
        resName: '设备档案',
        name: 'deviceFile',
        resUrl: '/equipmentInfrastructure/device-file/index',
        id: '4-0-1'
      },
      {
        resName: '通讯卡',
        name: 'communicationCard',
        resUrl: '/equipmentInfrastructure/communication-card/index',
        id: '4-0-2'
      }
    ]
  },

  {
    resIcon: 'serviceOrg2',
    resName: '消防技术服务机构',
    resUrl: '/serviceOrg',
    id: '4'
  },

  {
    resIcon: 'statisticalQuery',
    resName: '统计分析',
    resUrl: '/statisticalQuery',
    id: '5',
    children: [
      {
        resName: '火警处置分析',
        name: 'fireHandle',
        resUrl: '/statisticalQuery/fireHandle',
        id: '5-0-1'
      },
      {
        resName: '预警处置分析',
        name: 'earlyWarningHandle',
        resUrl: '/statisticalQuery/earlyWarningHandle',
        id: '5-0-2'
      },
      {
        resName: '隐患处置分析',
        name: 'hiddenHandle',
        resUrl: '/statisticalQuery/hiddenHandle',
        id: '5-0-3'
      },
      {
        resName: '设备离线分析',
        name: 'offLineHandle',
        resUrl: '/statisticalQuery/offLineHandle',
        id: '5-0-4'
      },
      {
        resName: '消防安全报告',
        name: 'fireSafetyReport',
        resUrl: '/statisticalQuery/fireSafetyReport',
        id: '5-0-4'
      }
    ]
  },

  {
    resIcon: 'systemManage',
    resName: '系统管理',
    resUrl: '/systemManage',
    id: '6',
    children: [
      {
        resName: '组织机构管理',
        name: 'organizational',
        resUrl: '/systemManage/sys-organizational',
        id: '6-0-1'
      },
      {
        resName: '角色管理',
        name: 'role',
        resUrl: '/systemManage/sys-role/',
        id: '6-0-2'
      },
      {
        resName: '用户管理',
        name: 'user',
        resUrl: '/systemManage/sys-user',
        id: '6-0-3'
      },
      {
        resName: '监管对象管理',
        name: 'supervisionObject',
        resUrl: '/systemManage/supervisionObject',
        id: '6-0-4'
      },
      {
        resName: '个性化配置',
        name: 'personalization',
        resUrl: '/systemManage/personalization',
        id: '6-0-5'
      },
      {
        resName: '业务参数配置',
        name: 'businessConfigure',
        resUrl: '/systemManage/businessConfigure',
        id: '6-0-6'
      }
    ]
  }

  //     {
  //       resName: "消防档案",
  //       id: "3-3",
  //       name: "fireFightingArchives",
  //       // children: [
  //       //     {
  //       //         title: '防火巡查计划',
  //       //         name: 'firePatrolPlan',
  //       //         id: '3-1-0'
  //       //     },
  //       //     {
  //       //         title: '防火巡查任务',
  //       //         name: 'firePatrolTask',
  //       //         id: '3-1-1'
  //       //     }
  //       // ]
  //     },
  //     {
  //       resName: "防火检查",
  //       id: "3-4",
  //       children: [
  //         {
  //           title: "防火检查计划",
  //           name: "fireInspectPlan",
  //           id: "3-4-0",
  //         },
  //         {
  //           title: "防火检查任务",
  //           name: "fireInspectTask",
  //           id: "3-4-1",
  //         },
  //       ],
  //     },
  //     {
  //       title: "消防安全重点部位",
  //       id: "3-5",
  //       name: "fireKeyPosition",
  //     },
  //   ],
  // },
  // {
  //   icon: "location",
  //   title: "消防设施点位图",
  //   id: "4",
  //   children: [
  //     {
  //       title: "楼层平面图",
  //       name: "buildFloorPoint",
  //       id: "4-0",
  //     },
  //   ],
  // },
  // {
  //   icon: "dossier",
  //   title: "设备档案",
  //   name: "equipmentRecord",
  //   id: "5",
  // },
  // {
  //   icon: "dossier",
  //   title: "人员信息",
  //   name: "personnelMgmt",
  //   id: "6",
  // },
  // {
  //   icon: "dossier",
  //   title: "用户管理",
  //   name: "userMgmt",
  //   id: "7",
  // },
  // {
  //   icon: "dossier",
  //   title: "公告管理",
  //   name: "announceMgmt",
  //   id: "8",
  // },
  // {
  //   icon: "dossier",
  //   title: "日志查询",
  //   id: "9",
  //   children: [
  //     {
  //       title: "登录日志",
  //       name: "loginLog",
  //       id: "9-0",
  //     },
  //     {
  //       title: "操作日志",
  //       name: "operateLog",
  //       id: "9-1",
  //     },
  //   ],
  // },
  // {
  //   icon: "dossier",
  //   title: "系统配置",
  //   id: "10",
  //   children: [
  //     {
  //       title: "系统个性化配置",
  //       name: "sysConfig",
  //       id: "10-0",
  //     },
  //     {
  //       title: "业务个性化配置",
  //       name: "bizConfig",
  //       id: "10-1",
  //     },
  //   ],
  // },
]
