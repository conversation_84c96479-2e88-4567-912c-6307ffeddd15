import { App } from 'vue'
import * as icons from '@element-plus/icons-vue'
// import progressComponent from '@/components/public/progressComponent.vue'
import popupComponent from '@/components/public/popup/index.vue'
import popupSide from '@/components/public/popup/popupSide.vue'
import popupWrap from '@/components/public/popup/popupWrap.vue'
import popupVideo from '@/components/public/popup/popupVideo.vue'
import videoAlarm from '~/components/public/popup/videoAlarm.vue'
import videoComponent from '@/components/public/videoComponent.vue'
import audioComponent from '@/components/public/audioComponent.vue'
import svgIcon from '@/components/public/svgIcon.vue'
import annularComponent from '@/components/public/annularComponent.vue'
import tableList from '@/components/public/tableList.vue'
import headerItem from '@/components/public/headerItem.vue'
import myTooltip from '@/components/echartMap/myToolTip.vue'
import myTooltips from '@/components/echartMap/myToolTip.vue'

import breadcrumbNavigation from '@/components/public/BreadcrumbNavigation.vue'
import customEmpty from '@/components/public/customEmpty.vue'

import noData from '@/components/public/noData.vue'
import { ClickOutside } from 'element-plus'
import fragment from '@/components/public/fragment.vue'
export default function registerComponent(app: App) {
  Object.keys(icons).forEach((key) => {
    app.component(`Icon${key}`, icons[key])
  })
  app.component(fragment.name!, fragment)

  app.component(popupComponent.name!, popupComponent)

  app.component(popupSide.name!, popupSide)

  app.component(popupWrap.name!, popupWrap)

  app.component(popupVideo.name!, popupVideo)

  app.component(videoAlarm.name!, videoAlarm)

  app.component(videoComponent.name!, videoComponent)

  app.component(audioComponent.name!, audioComponent)

  app.component(svgIcon.name!, svgIcon)

  app.component(tableList.name!, tableList)

  app.component(annularComponent.name!, annularComponent)

  app.component(headerItem.name!, headerItem)

  app.component(breadcrumbNavigation.name!, breadcrumbNavigation)

  app.component(customEmpty.name, customEmpty)

  app.component(noData.name!, noData)

  app.component(myTooltip.name!, myTooltip)
  app.component(myTooltips.name!, myTooltips)
  app.directive('clickoutside', ClickOutside)
  app.directive('isClick', (el, binding) => {
    let downTime: any = null
    let upTime: any = null
    el.addEventListener('mousedown', (event) => {
      downTime = new Date().getTime()
      el._stop = event.stopPropagation // 阻止点击事件冒泡
    })
    el.addEventListener('mouseup', () => {
      upTime = new Date().getTime()
      const selectionTxt = window.getSelection()?.toString().trim()
      if (upTime - downTime < 100 || selectionTxt!.length == 0) {
        el.setAttribute('event-type', 'click')
      } else {
        el.setAttribute('event-type', 'copy')
      }
      cb()
    })

    function cb() {
      const methodToCall = binding.value // 获取传递的方法
      const condition = binding.arg // 获取传递的参数
      if (typeof methodToCall === 'function') {
        console.log(el.getAttribute('event-type'))
        // 根据条件判断是否要调用方法
        if (el.getAttribute('event-type') === 'click') {
          methodToCall(condition) // 调用传递的方法，并传入参数
        }
      }
    }
  })
}
