import { toRaw, ref, watch, onMounted, computed, nextTick } from 'vue'
import { useRouter, useRoute, RouteRecordRaw } from 'vue-router'

// export
function checkBtnPermission(page = '', parent = '') {
  if (!page) return
  const route = useRoute()
  const { path } = route
  const paths = path.split('/')
  let menuIdMap
  let menuIdArr
  try {
    menuIdArr = sessionStorage.getItem('menuIdArr')
      ? JSON.parse(sessionStorage.getItem('menuIdArr'))
      : []
  } catch (e) {
    //TODO handle the exception
    menuIdArr = []
  }
  let arr = menuIdArr.filter((i) => i.resUrl == paths.join('/'))
  let tabs = []
  let btnArr = []
  if (arr.length > 0) {
    let btnAdnTabsArr = arr[0].children
    tabs = btnAdnTabsArr.filter((i) => i.resIdent == 'tabs')
    btnArr = btnAdnTabsArr.filter((i) => i.resType == '2')
  }
  if (tabs.length > 0) {
    tabs.forEach((i) => {
      btnArr.push(...i.children)
    })
  }
  let obj = {}
  btnArr.forEach((i) => {
    let key = i.resUrl.split('/')
    if (key.indexOf(page) != -1) {
      obj[key[key.length - 1]] = true
    }
  })
  return obj
}

const getPermission = (type = 'tabs', parent = '') => {
  const route = useRoute()
  const { path } = route
  const paths = path.split('/')
  let menuIdArr
  try {
    menuIdArr = sessionStorage.getItem('menuIdArr')
      ? JSON.parse(sessionStorage.getItem('menuIdArr'))
      : []
  } catch (e) {
    //TODO handle the exception
    menuIdArr = []
  }
  let arr = menuIdArr.filter((i) => i.resUrl == paths.join('/'))
  let newArr = flatten(arr)
  let result = []
  if (arr.length > 0) {
    result = newArr.filter((i) => i.resIdent == type)
  }
  result.forEach((i) => {
    let key = i.resUrl.split('/')
    i.key = key[key.length - 1]
    i.parent = key[key.length - 2]
  })
  if (parent) {
    return result.filter(i.parent == parent)
  } else {
    return result
  }
}
const flatten = (arr) => {
  return arr.reduce((pre, cur) => {
    const { children = [], ...other } = cur
    return pre.concat([{ ...cur }], flatten(children))
  }, [])
}

export default { checkBtnPermission, getPermission }
