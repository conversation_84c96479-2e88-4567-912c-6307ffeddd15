import { SocketOptions } from '@/types'
import config from '@/config'

const SockJS = (window as any).SockJS,
  Stomp = (window as any).Stomp

class SocketUtil {
  socket: any
  stomp: any
  onmessage?: any
  optionsObj: any
  subscriber?: { unsubscribe: () => void }

  constructor(options: SocketOptions) {
    this.optionsObj = options
    this.connects()
  }

  connects() {
    this.socket = new SockJS(config.socket_url, null, {
      transports: ['websocket'],
    })
    this.stomp = Stomp.over(this.socket)
    this.stomp.connect(this.optionsObj.headers, () => {
      this.subscriber = this.stomp.subscribe(this.optionsObj.topic, (response) => {
        const body = JSON.parse(response.body)
        Object.keys(body).forEach((key) => (body[key] = body[key].toString()))
        this.onmessage && this.onmessage(body)
      })
    })
    this.socket.onclose = () => {
      this.subscriber?.unsubscribe()
      this.stomp.disconnect()
      this.socket = null
      this.stomp = null
      const timer = setTimeout(() => {
        this.connects()
        clearTimeout(timer)
      }, 10000)
    }
  }

  distory() {
    this.stomp.unsubscribe()
  }
}

export default SocketUtil
