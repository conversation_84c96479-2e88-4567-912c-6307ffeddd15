import $API from '../../common/api'
import { ElMessage, ElLoading } from 'element-plus'
import config from '~/config/index'

// 删除对象空值
export function deleteObjectNullProp(obj: any) {
  for (const key in obj) {
    if (obj[key] !== 0) {
      if (!obj[key]) {
        delete obj[key]
      }
    }
  }
}

/**
 * 导出、预览、下载公用接口
 * @param options 配置参数 包含接口地址url; 接口参数params;responseType：默认值：json、可选值：flow（流）; type 默认值：export（导出）、可选值：download（下载）、preview（预览）
 */
export function exportFile(
  options: {
    url: string
    params: any
    type?: string
    responseType?: string
    method?: string
    successText?: string
  },
  exportFields?: {
    key: string
    title: string
  }[]
) {
  const LoadingOptions: any = {
    fullscreen: true,
    lock: true,
    text: '加载中...',
  }
  const loading = ElLoading.service(LoadingOptions)
  const { url, params, type = 'export', responseType = 'json', method = 'post', successText = '' } = options

  const apiOptions: any = {
    url,
    params,
    data: exportFields,
  }

  let fileName = '' // 文件名

  if (type === 'export' || type === 'download') {
    // 下载
    if (responseType === 'flow') {
      apiOptions.responseType = 'blob'
    }
    let elMessageTypeName = successText || '导出'
    if (type === 'download' && !successText) {
      elMessageTypeName = '下载'
    }
    if (method === 'get') {
      $API
        .get(apiOptions)
        .then((res) => {
          const blob = new Blob([res.data], {
            type: 'application/vnd.ms-excel',
          })
          const fileName = decodeURI(res.headers['content-disposition'].split(';')[1].substr(9))
          const downloadElement = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          downloadElement.href = href
          downloadElement.download = fileName
          document.body.appendChild(downloadElement)
          downloadElement.click()
          document.body.removeChild(downloadElement)
          window.URL.revokeObjectURL(href)
          loading.close()
        })
        .catch((err) => {
          loading.close()
        })
    } else {
      return new Promise((resolve, reject) => {
        $API.post(apiOptions).then(async (res: any) => {
          if (
            (res && res.code && res.code == 'error') ||
            (res && res.data && res.data.code && res.data.code == 'error')
          ) {
            // ElMessage({
            //   message: res.message||elMessageTypeName+'失败',
            //   type: 'error'
            // })
            loading.close()
            reject('error')
            return
          }

          if (responseType === 'json') {
            // 返回的是json格式数据，res.data即为文件地址
            const a = document.createElement('a')
            a.href = config.base_host + res.data
            if (type != 'download') {
              a.target = '_blank'
            }
            a.download = fileName
            a.click()
            loading.close()
            ElMessage({
              message: elMessageTypeName + '成功',
              type: 'success',
            })
            resolve('success')
          } else if (responseType === 'flow') {
            // 返回的是文件流
            if (res && res.code && res.code == 'error') {
              // ElMessage({
              //   message: res.message||elMessageTypeName+'失败',
              //   type: 'error'
              // })
              loading.close()
              reject('error')
              return
            }
            apiOptions.responseType = 'blob'
            if (res?.headers['content-disposition']) {
              try {
                fileName = decodeURI(res.headers['content-disposition'].split(';')[1].substr(9).replace('=', ''))
                const data = new Blob([res.data], {
                  type: 'application/vnd.ms-excel',
                })

                const blobUrl = window.URL.createObjectURL(data)
                const a = document.createElement('a')
                a.href = blobUrl
                a.download = fileName
                a.click()
                loading.close()
                ElMessage({
                  message: elMessageTypeName + '成功',
                  type: 'success',
                })
                resolve('success')
              } catch (error) {
                loading.close()
                ElMessage({
                  message: '未知错误',
                  type: 'error',
                })
                reject(res)
              }
            } else {
              loading.close()
              if (res.data.type === 'application/json') {
                const text = await res.data.text()
                const jsonText = JSON.parse(text)
                ElMessage.error(jsonText.message)
                resolve(res)
                return
                // const reader = new FileReader(res.data);
                // 	reader.onload = function(){
                // 	    const { errorMsg } = JSON.parse(reader.result);
                // 		//处理错误
                // 	};
                // 	reader.readAsText(res.data);
              }

              ElMessage({
                message: (res && res.message) || elMessageTypeName + '失败',
                type: 'error',
              })
              resolve(res)
            }
          }
        })
      })
    }
  } else {
    // 预览
    return new Promise((resolve, reject) => {
      $API.post(apiOptions).then((res: any) => {
        loading.close()
        if (res && res.code) {
          if (res.code == 'error') {
            ElMessage({
              message: '未知错误',
              type: 'error',
            })
            reject()
          } else {
            if (res.data == '') {
              ElMessage({
                message: '暂无数据',
                type: 'warning',
              })
              reject()
              return
            }
            try {
              const fileName = res.data.split('/').slice(-1)[0] // 文件名
              resolve('success')
              window.open(config.base_host + res.data, fileName)
            } catch (error) {
              ElMessage({
                message: '解析文件出错',
                type: 'error',
              })
              reject()
            }
          }
        }
      })
    })
  }
}

/**
 * 根据返回的文件路径 预览、下载公用方法
 * @param options 配置参数 包含地址url;  type 默认值：download 可选值：download（下载）、preview（预览）
 */
export function previewOrDownload(options: { originUrl: string; type?: string; data?: any }) {
  const { originUrl, type = 'download', data } = options
  const LoadingOptions: any = {
    fullscreen: true,
    lock: true,
    text: '加载中...',
  }
  const loading = ElLoading.service(LoadingOptions)

  if (type === 'preview') {
    const lastIndex = originUrl.lastIndexOf('.')
    const suffix = originUrl.substring(lastIndex + 1)
    //文件格式是word文档、ppt、excel文件文件时
    if (['doc', 'docx', 'ppt', 'xls', 'xlsx'].includes(suffix)) {
      const fileUrl = encodeURIComponent(originUrl)
      //使用Office Web查看器
      const officeUrl = 'http://view.officeapps.live.com/op/view.aspx?src=' + fileUrl
      window.open(officeUrl, '_target')
      loading.close()
    } else {
      //其他文件格式比如pdf、图片、html
      window.open(config.base_host + originUrl)
      loading.close()
    }
  } else if (type === 'download') {
    // --------a链接下载--------
    /*  const anchor = document.createElement('a')
    let index = originUrl.lastIndexOf('/')
    let filename = originUrl.substring(index + 1, originUrl.length)
    anchor.href = config.base_host + originUrl
    anchor.download = filename
    anchor.style.display = 'none' // 隐藏链接，避免干扰页面布局
    document.body.appendChild(anchor) // 将链接添加到页面中
    anchor.click() // 模拟点击触发下载
    loading.close()
    document.body.removeChild(anchor) // 下载完成后，移除链接 */

    // -------xhr 下载--------
    const xhr = new XMLHttpRequest()
    xhr.open('GET', config.base_host + originUrl)
    xhr.responseType = 'blob' // 设置响应类型为二进制数据
    xhr.onload = function () {
      if (xhr.status === 200) {
        const blob = new Blob([xhr.response], {
          type: 'application/octet-stream',
        })
        const index = originUrl.lastIndexOf('/')
        const filename = originUrl.substring(index + 1, originUrl.length)
        const anchor = document.createElement('a')
        const downloadUrl = URL.createObjectURL(blob)
        anchor.href = downloadUrl
        anchor.download = filename
        anchor.click()
        URL.revokeObjectURL(downloadUrl) // 释放URL对象
      }
    }
    xhr.send()
    loading.close()
  }
}

// 获取角色列表
export function getRoleList() {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/socialUnit/getRoleList',
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

/**
 * 获取单位名称下拉列表
 * @param params
 */
export function getUnitName(params = {}) {
  deleteObjectNullProp(params)
  params = Object.assign({ superviseStatus: '1' }, params)
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/safetyMonitor/getUnitByName',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

/**
 * 获取单位名称下拉列表
 * @param params
 */
export function getModelName(params = {}) {
  deleteObjectNullProp(params)
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/selectTemplateList',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

/**
 * 获取知识类型下拉列表
 * @param params
 */
export function getKnowledgeTypeList(params = {}) {
  deleteObjectNullProp(params)
  params = Object.assign({ superviseStatus: '1' }, params)
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/basic/getKnowledgeTypeList',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}
/**
 * 查询该监管单位下所有的子集
 * @param params
 */
export function getUnitNameBySuperviseId(params = {}) {
  deleteObjectNullProp(params)
  // params = Object.assign({ superviseStatus: '1' }, params)
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/security/management/subset',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data.rows)
        } else {
          reject(data)
        }
      })
  })
}

/**
 * 获取单位机构下拉列表
 * @param params
 */
export function getServiceModelType(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/socialUnit/getServiceModelType',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}
/**
 * 获取单位机构下拉列表
 * @param params
 */
export function getOrgName(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/organize/getSuperviseUnitTree',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}
/**
 * 获取监管单位级联下拉数据
 * @param params
 */
export function getSuperviseUnitTreeByConditions(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/getSuperviseUnitTreeByConditions',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

// ;/monitor/getMakeFromEvent  //  主机号

export function getMakeFromEvent(params: any = {}) {
  // 默认为全部接口, whole 查询全部 part 查询部分,默认为查询全部数据
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/monitor/getMakeFromEvent',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}
/**
 * 获取设备类型 级联
 * @param params
 */
export function getDeviceType(params: any = {}, filter = 'whole', withoutDeviceTypes = '') {
  // 默认为全部接口, whole 查询全部 part 查询部分,默认为查询全部数据
  return new Promise<any>((resolve, reject) => {
    if (filter === 'part') params.withoutDeviceTypes = withoutDeviceTypes
    $API
      .post({
        url: '/safetyMonitor/getDeviceTypes',
        data: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

/**
 * 获取楼栋楼层 级联树桩数据 sh
 * @param params
 */
export function getBuildingNewTreeByUnitId(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/safetyMonitor/getBuildingTreeByUnitId',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

/**
 * 获取楼栋楼层 级联
 * @param params
 */
export function getBuildingTreeByUnitId(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/monitor/getTreeBuildingInfo',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

/**
 * 获取楼栋
 * @param params
 */
export function getBuildList(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/deviceEvent/getBuildingListByUnitId',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

/**
 * 获取楼层
 * @param params
 */
export function getFloorList(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/deviceEvent/getFloorListByUnitIdAndBuilding',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

interface UrgePrams {
  disposeId: string
  operatorName: string
  operatorTel: string
}

export function saveUrgeRecord(params: UrgePrams) {
  return $API
    .post({
      method: 'post',
      url: '/dispose/saveUrgeRecord',
      params,
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        ElMessage.success('催促成功')
      } else {
        ElMessage.error(res.message)
      }
    })
}

// 根据企业单位ID查询消控室列表
export function getFireControlRoomListByUnitId(unitId: string) {
  const params = { unitId }
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/getFireControlRoomListByUnitId',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 消控室值班计划列表查询
export function getControlRoomDutyPlanLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/fireControlRoomDuty/plan/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 消控室值班任务列表查询
export function getControlRoomDutyTaskLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/fireControlRoomDuty/task/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 消控室值班任务详情查询
export function getControlRoomDutyTaskDetail(taskId: string) {
  const params = { taskId }
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/fireControlRoomDuty/task/details',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 消控室人员在离岗记录查询
export function getControlRoomPersonLeaveRecordLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/fireControlRoomDuty/personLeaveRecord/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 分页查询防火巡查计划列表
export function selectPatrolPlanByListPage(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/selectPatrolPlanByListPage',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询防火巡查任务列表
export function getFireInspectionTaskLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/patrol-task-list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询防火巡查任务详情
export function getFireInspectionTaskDetail(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/patrol-task-details',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 分页查询防火检查计划列表
export function selectInspectPlanByListPage(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/selectInspectPlanByListPage',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 分页查询防火检查任务列表
export function getFireCheckingTaskLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/inspect-task-list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询防火检查任务详情
export function getFireCheckingTaskDetail(taskId: any) {
  return new Promise((resolve, reject) => {
    const params = { taskId }
    $API
      .post({
        url: '/fireInspection/inspect-task-details',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取设施巡查计划列表
export function getBuildingFirefightingFacilitiesCheckingPlanLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/plan/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取设施巡查任务列表
export function getBuildingFirefightingFacilitiesCheckingTaskLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/task/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取设施巡查任务详情（巡查信息）
export function getBuildingFirefightingFacilitiesCheckingTaskDetail(taskId: any) {
  const params = {
    taskId,
  }
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/task/get-task-details',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取设施巡查任务详情（完成情况）
export function getBuildingFirefightingFacilitiesCheckingTaskDetailOfCompletion(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/task/get-patrol-points-type',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取设施巡查任务详情（问题列表）
export function getBuildingFirefightingFacilitiesCheckingTaskDetailProblemLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/task/get-problem-record-list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询单位例行维保计划列表
export function getRoutineTrackList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/getRoutineTrackList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询单位例行维保详情
export function getMaintenancePlanCheckRelSystemList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/getMaintenancePlanCheckRelSystemList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询预约维保列表
export function getReservationList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/getReservationList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询应急维修列表
export function getEmergencyList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/fireFightFacilityMaintenance/emergency/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取消防系统下拉列表
export function getFireFightingSystemList() {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/getFireFightSystemList',
      })
      .then((res: any) => {
        if (res && res.code == 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
  })
}

// 获取计划id消防系统下拉列表
export function queryFaultSystemList(params) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/queryFaultSystemList',
        params,
      })
      .then((res: any) => {
        if (res && res.code == 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
  })
}

// 合同变更历史-获取合同列表
export function getContractLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/fireFightFacilityMaintenance/contract/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取合同变更历史列表
export function getContractHistoryLists(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/fireFightFacilityMaintenance/contractHistory/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询九小/家庭列表
export function queryUnitSuperviseList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/monitor/queryUnitSuperviseList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取设备品牌列表
export function getDeviceBrandOptions(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/eDevice/getDeviceBrandDicList',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

// 根据品牌id筛选规格型号列表
export function getDeviceModelOptions(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/eDevice/getDeviceModelDicList',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}
// 用户行为分析接口
//单位登录情况统计
export function unitLoginStatistics(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/userBehavior/analysis/getUnitLoginPageList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
//用户登录情况统计
export function getUserLoginPageList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: 'userBehavior/analysis/getUserLoginPageList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

//查询消防安全工作执行情况列表
export function getFireSafetyWorkPageList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/userBehavior/analysis/getFireSafetyWorkPageList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
//基础信息管理接口-----------start

// 智能离岗监测
//离岗记录列表
export function getControlRoomDutyRecordList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/dutyRecord/getControlRoomDutyRecordList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

//在离岗监测列表
export function getStatisticsOnLeaveList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/dutyRecord/statisticsOnLeaveList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

//安消联动列表
export function getVideoPageListByUnitId(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/safetyLink/getVideoPageListByUnitId',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询设备档案列表
export function queryDeviceArchivesPage(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/basic/queryDeviceArchivesPage',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
// 查询通讯卡列表
export function queryCommunicationCardPage(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/basic/queryCommunicationCardPage',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
// 查询消防安全报告列表
export function queryReportList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/report/reportList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
// 查询消防安全报告列表
export function queryReportConfigurationList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/report/reportConfigurationList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
// 查询消防安全报告列表
export function saveMonthlyReportDownload(data: any) {
  deleteObjectNullProp(data)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/report/monthlyReportDownload',
        data: data,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
// 知识库
export function queryKnowledgePage(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/basic/queryKnowledgePage',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 隐患标准
export function queryHzardStandards(params: any) {
  // deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/hazardStandard/queryHazardStandardPage',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
//基础信息管理接口-------------------end

// 查询设备档案列表
export function queryDeviceInfoPageList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/eDevice/queryDeviceInfoPageList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询通讯卡列表
export function queryCommunicationCardList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/eDevice/communicationCard/list',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询监管对象列表
export function querySuperviseRelPageList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/superviseRel/querySuperviseRelPageList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看设备增长趋势
export function getDeviceTrend(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/device/analysis/getDeviceTrend',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看设备数量排名前十单位
export function getDeviceNumTop10Unit(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/device/analysis/getDeviceNumTop10Unit',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 按设备分类查看设备数量
export function getDeviceNumByType(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/device/analysis/getDeviceNumByType',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 消防设施完好率
export function getDeviceIntactRate(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/device/analysis/getDeviceIntactRate',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询消防设施运行情况分析
export function getDeviceOnlineRate(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/device/analysis/getDeviceOnlineRate',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 按设备品牌查看设备数量
export function getDeviceNumByBrand(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/device/analysis/getDeviceNumByBrand',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 风险评估查询行业类别列表
export function getIndustryDicList(options: { code: string } = { code: '' }) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/getIndustryDicList',
        params: options,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 风险评估查询单位属性列表
export function getUnitAttributeDicList(options: { code: string } = { code: '' }) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/getUnitAttributeDicList',
        params: options,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 单位配置回显
export function getUnitConfigDetail(options: { tempId: string } = { tempId: '' }) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/unitConfigDetail',
        params: options,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取默认模板信息
export function getRiskTemplateInfo(options: { tempId: string } = { tempId: '' }) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/getRiskTemplateInfo',
        params: options,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取模板名称列表
export function getTemplateNameList(params = {}) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/getTemplateNameList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取模板名称列表
export function getTemplateOptions(params) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireSafety/queryTemplateList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取人员资质证书下拉列表
export function getQualificationDicList() {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/fireInspection/getQualificationDicList',
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 定制模板,保存并开始配置
export function saveRiskTemplateBase(data: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/saveRiskTemplateBase',
        data,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 定制模板,编辑保存
export function editRiskTemplateBase(data: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/editRiskTemplateBase',
        data,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 定制模板,保存并开始配置
export function addUnitToTemplate(data: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/addUnitToTemplate',
        data,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查询模板评估树
export function getTemplateIndicatorCategoryTree(params: { name?: string; tempId: string }) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/getTemplateIndicatorCategoryTree',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 根据监管id查询其监管的末端单位是否配置了其他模型
export function getIfConfiged(params: { superviseId: string; tempId: string }) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/getIfConfiged',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 定制模板评估树页面数据保存
export function saveRiskTemplateTreeForSpecial(data: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/saveRiskTemplateTreeForSpecial',
        data,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 编辑指标保存
export function editRiskTemplateIndicators(data: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/riskAssess/editRiskTemplateIndicators',
        data,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

/**
 * 工作台
 */

// 获取数据决策列表数据
export function queryDecisionWithDataList(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/decision/decisionWithDataList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取接收的消防资讯列表数据
export function queryInformationList(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/information/queryInformationList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取创建的消防资讯列表数据
export function queryInformationByCreate(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/information/queryInformationByCreate',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 撤回创建的资讯
export function withdrawInformation(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/information/withdrawInformation',
        params,
      })
      .then((res: any) => {
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 删除创建的资讯
export function delInformation(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/information/delInformation',
        params,
      })
      .then((res: any) => {
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看通知通告草稿箱列表
export function queryInformationByDrafts(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/information/queryInformationDraft',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取消防资讯来源option
export function getInformationSourceList(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/information/getInformationSourceList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取消防资讯详情
export function queryInformationById(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/information/queryInformationById',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看接收的待办事项
export function queryPendingByReceive(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/pending/queryPendingByReceive',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看创建的待办事项
export function queryPendingByCreate(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/pending/queryPendingByCreate',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 更改待办事项完成状态
export function updatePendingState(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/pending/updatePendingState',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看待办事项详情
export function queryPendingDetailsById(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/pending/queryPendingDetailsById',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看待办事项完成情况
export function queryPendingStatusById(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/pending/queryPendingStatusById',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 撤回创建的待办
export function withdrawPending(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/pending/withdrawPending',
        params,
      })
      .then((res: any) => {
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 删除创建的待办事项
export function delPending(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/pending/delPending',
        params,
      })
      .then((res: any) => {
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看通知通告草稿箱列表
export function queryPendingDraft(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/pending/queryPendingDraft',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看接收的通知通告
export function queryNoticeByReceive(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/notice/queryNoticeByReceive',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看通知通告草稿箱列表
export function queryNoticeByDrafts(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/notice/queryNoticeDraft',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看创建的通知通告
export function queryNoticeByCreate(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/notice/queryNoticeByCreate',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 撤回创建的资讯
export function withdrawNotice(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/notice/withdrawNotice',
        params,
      })
      .then((res: any) => {
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 删除创建的通知通告
export function delNotice(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/notice/delNotice',
        params,
      })
      .then((res: any) => {
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 查看通知通告详情
export function queryNoticeDetailsById(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/notice/queryNoticeDetailsById',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 获取检测值守核警结果的options
export function getOperationResultList(
  params: any = {
    resultType: '1',
  }
) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/dispose/getOperationResultList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 系统动态列表
export function querySystemDynamics(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/systemDynamics/querySuperviseSystemDynamicsList',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 删除
export function delSystemDynamics(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/systemDynamics/delSystemDynamics',
        params,
      })
      .then((res: any) => {
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 系统动态详情
export function getSystemDynamicsDetail(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/systemDynamics/systemDynamicsDetail',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

/**
 * 下载文件的处理
 * @param url 请求地址
 * @param filename 文件名称 必填 需带上格式后缀
 * @param params 请求参数
 */
// type DownloadType = {
//   url: string;
//   filename: string;
//   params: any;
//   msg?: string;
//   method?: string;
// };
// export const getDownloadTxt = async ({
//   url,
//   filename,
//   params,
//   msg = '开始导出',
//   method = 'get'
// }: DownloadType) => {
//   ElMessage.config({ maxCount: 1 });
//   ElMessage.success(`${msg}，请稍等~`);
//   const requestConfig =
//     method === 'get'
//       ? {
//           method,
//           responseType: 'blob',
//           params
//         }
//       : {
//           method,
//           responseType: 'blob',
//           data: params
//         };

//   await extendRequest(url, requestConfig).then((response) => {
//     if (response?.type === 'application/json') {
//       const fileReader = new FileReader();
//       fileReader.onloadend = () => {
//         const jsonData = JSON.parse(fileReader?.result);
//         const { code, message: errMsg } = jsonData;
//         switch (code) {
//           case ResCode.LOGINREQUIRE:
//           case ResCode.USERDOESNOTEXIST:
//           case ResCode.USERNOTASSIGNEDROLE:
//             redirectToLogin();
//             break;
//           default:
//         }
//         if (code) {
//           ElMessage.error(errMsg || '未知错误');
//         }
//       };
//       fileReader.readAsText(response);
//     } else {
//       saveAs(response, filename);
//     }
//   });
// };
