/**
 * 系统日志
 */
import $API from '~/common/api'
import { deleteObjectNullProp } from '~/common/comResponse/index'

/**
 *
 * @param params
 */
export function querySysOperLogList(params: any) {
  deleteObjectNullProp(params)
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/sysLog/querySysOperLogList',
        params
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
