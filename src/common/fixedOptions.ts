// 根据code获取name
const getNameByCode = (
  code: any,
  options: any,
  propName: any = { labelName: 'label', valueName: 'value' }
) => {
  const filterRes = options.filter(
    (item: any) => item[propName.valueName] === code
  )

  if (filterRes.length > 0) {
    return filterRes[0][propName.labelName]
  } else {
    return ''
  }
}

// 是否
const yesOrNotOptions = [
  { label: '是', value: '1' },
  { label: '否', value: '0' }
]

const serviceStatus = [
  // { label: '全部', value: '' },
  { label: '已启用', value: '1' },
  { label: '已停用', value: '0' }
]

// 消控室值班计划状态
const controlRoomDutyPlanStateOptions = [
  // { label: '全部', value: '' },
  { label: '已启用', value: '1' },
  { label: '已停用', value: '0' }
]

// 值班状态
const dutyStatusOptions = [
  { label: '待值班', value: '0' },
  { label: '已完成', value: '2' },
  { label: '超时未执行', value: '3' }
]

// 是否规范
const isStandardOptions = [
  { label: '是', value: '1' },
  { label: '否', value: '0' }
]

const standardOptions = [
  { label: '是', value: '1' },
  { label: '否', value: '4' }
]

// 离岗时长
const leaveTimeOptions = [
  {
    label: '1小时内',
    flagUnit: '0',
    flagValue: '1'
  },
  {
    label: '3小时内',
    flagUnit: '0',
    flagValue: '3'
  },
  {
    label: '8小时内',
    flagUnit: '0',
    flagValue: '8'
  },
  {
    label: '1天内',
    flagUnit: '0',
    flagValue: '24'
  },
  {
    label: '超出1天',
    flagUnit: '1',
    flagValue: '1'
  }
]

// 离返岗
const leaveStatusOptions = [
  { label: '已离岗', value: 0 },
  { label: '已返岗', value: 1 }
]

// firePatrol
// 防火巡查计划状态
const fireInspectionPlanStatusOptions = [
  {
    value: '0',
    label: '待启用'
  },
  {
    value: '1',
    label: '启用中'
  },
  {
    value: '2',
    label: '已过期'
  },
  {
    value: '3',
    label: '已停用'
  }
]
// 防火检查  1已启用, 2已停用,3已过期
const fireInspectionStatusOptions = [
  {
    value: '0',
    label: '待启用'
  },
  {
    value: '1',
    label: '启用中'
  },
  {
    value: '2',
    label: '已停用'
  },
  {
    value: '3',
    label: '已过期'
  }
]

// 巡查状态
const inspectionTaskIsValidOptions = [
  {
    value: '0',
    label: '待巡查'
  },
  {
    value: '1',
    label: '巡查中'
  },
  {
    value: '2',
    label: '已完成'
  },
  {
    value: '3',
    label: '超时未执行'
  }
]

// 检查状态
const checkingTaskIsValidOptions = [
  {
    value: '0',
    label: '待检查'
  },
  {
    value: '1',
    label: '检查中'
  },
  {
    value: '2',
    label: '已完成'
  },
  {
    value: '3',
    label: '超时未执行'
  }
]

// 完成率
const completionRateOptions = [
  {
    value: '1',
    label: '100%'
  },
  {
    value: '2',
    label: '60%~99%'
  },
  {
    value: '3',
    label: '60%以下'
  }
]

// 规范率
const normRateOptions = [
  {
    value: '1',
    label: '100%'
  },
  {
    value: '2',
    label: '60%~99%'
  },
  {
    value: '3',
    label: '60%以下'
  }
]

// 频次
const frequencyOptions = [
  { value: '0', label: '小时' },
  { value: '1', label: '日' },
  { value: '2', label: '周' },
  { value: '3', label: '月' },
  { value: '4', label: '季度' },
  { value: '5', label: '年' }
]

// 计划类型
const planTypeOptions = [
  // {
  //   value: '',
  //   label: '全部'
  // },
  // {
  //   value: '1',
  //   label: '日计划'
  // },
  // {
  //   value: '2',
  //   label: '周计划'
  // },
  {
    value: '3',
    label: '月计划'
  },
  {
    value: '4',
    label: '季计划'
  },
  {
    value: '7',
    label: '半年计划'
  },
  {
    value: '5',
    label: '年计划'
  }
  // {
  //   value: '6',
  //   label: '自定义计划'
  // },
]

// 订单类型
const orderTypeOptions = [
  { value: '-1', label: '例行维保上报' },
  { value: '0', label: '预约维保' },
  { value: '1', label: '预测应急单' },
  { value: '2', label: '自建预约维保' },
  { value: '3', label: '隐患转维保' },
  { value: '4', label: '故障自动转维保' },
  { value: '5', label: '人工故障转维保' },
  { value: '6', label: '动作转维保' }
]

// 单位类型
const unitTypeOptions = [
  {
    value: 0,
    label: '企业单位'
  },
  {
    value: 3,
    label: '九小场所/家庭'
  }
]

// 服务范围
const serviceScopeTypeOptions = [
  { value: '1', label: '运营' },
  { value: '2', label: '维保' },
  { value: '3', label: '检测' },
  { value: '4', label: '评估' },
  { value: '5', label: '设备运维' },
  { value: '6', label: '监测值守' }
]

export {
  getNameByCode,
  yesOrNotOptions,
  standardOptions,
  serviceStatus,
  controlRoomDutyPlanStateOptions,
  dutyStatusOptions,
  isStandardOptions,
  leaveTimeOptions,
  leaveStatusOptions,
  fireInspectionPlanStatusOptions,
  fireInspectionStatusOptions,
  inspectionTaskIsValidOptions,
  checkingTaskIsValidOptions,
  completionRateOptions,
  normRateOptions,
  frequencyOptions,
  planTypeOptions,
  orderTypeOptions,
  unitTypeOptions,
  serviceScopeTypeOptions
}
