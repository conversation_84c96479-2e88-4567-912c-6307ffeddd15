<template>
  <div class="nkt w-full h-full">
    <div ref="mapRef" class="w-full h-full relative"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, withDefaults, computed } from 'vue'
import Popup from './devicePopup.vue'

// 导入整个库
import { indoorGis } from '@/utils/index.es.js'
console.log('🚀 ~ gisLib:', indoorGis)
// 使用多个组件
// import Popup from './popup.vue'

const { getGisMap } = indoorGis

interface Props {
  floorInfo: {
    mapType: number
    floordId: string
    floorImage: string
  }
  isShowPopup?: boolean
  popupHtml?: any
  deviceData?: any[]
}
const props = withDefaults(defineProps<Props>(), {
  popupHtml: () => null,
  deviceData: () => [],
  isShowPopup: () => false,
  hasReq: () => false
})
const emits = defineEmits(['onDeviceSelected'])

const mapRef = ref<HTMLElement | null>(null)

const _floorInfo = computed(() => {
  return props.floorInfo
})
const _isShowPopup = computed(() => {
  return props.isShowPopup
})
const _devicePopupHtml = computed(() => {
  return props.popupHtml || Popup
})
const _deviceData = computed(() => {
  return props.deviceData
})

onMounted(async () => {
  const gisMap = getGisMap()
  gisMap.mount(mapRef.value as HTMLElement)
  await gisMap.loadFloorData(_floorInfo.value)
  // const deviceData = getDeviceById()
  // gisMap.loadDeviceData([deviceData])
  // gisMap.onDeviceSelected = (e) => {
  //   if (_isShowPopup.value && _devicePopupHtml.value) {
  //     gisMap.addPointerPopup(e, _devicePopupHtml.value)
  //   }
  //   emits('onDeviceSelected', e)
  // }

  // gisMap.addPointerPopup
})
defineOptions({
  name: 'floorMap'
})
</script>
<style lang="scss" scoped></style>
