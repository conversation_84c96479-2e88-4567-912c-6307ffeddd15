<template>
  <div class="maybe-alarm-detail">
    <el-collapse class="gs-collapse" v-model="activeNames">
      <el-collapse-item title="设备状态" name="0">
        <ul>
          <li class="line-item">
            <span class="left-content" style="color: #ff4242">设备类型：</span>
            <span class="right-content" style="color: #ff4242">{{
              val.deviceTypeName || '未知'
            }}</span>
          </li>
          <li class="line-item">
            <span class="left-content">设备状态：</span>
            <span class="right-content">{{ deviceInfo.deviceStateLabel }}</span>
          </li>
        </ul>
      </el-collapse-item>
      <el-collapse-item title="在线状态" name="1" v-if="isShowOnline">
        <ul>
          <li class="line-item">
            <span class="left-content">在线状态：</span>
            <span class="right-content">{{ isOnline ? '在线' : '离线' }}</span>
          </li>
          <li>
            <div class="line-item">
              <span class="left-content">离线时间：</span>
              <span class="right-content">{{
                deviceInfo.offlineTime || '--'
              }}</span>
            </div>
            <div class="line-item">
              <span class="left-content">离线时长：</span>
              <span class="right-content">{{
                deviceInfo.offLineDurationNew || '--'
              }}</span>
            </div>
          </li>
        </ul>
      </el-collapse-item>
      <el-collapse-item title="监测状态" name="2">
        <div class="monitor-state">
          <div class="mb-10px">
            <span class="font-hp-md text-16px mr-10px">高度疑似真警</span>
            <el-tag effect="plain" round>{{ val.floorName }}</el-tag>
          </div>
          <!-- <p>警情描述：该点位与相邻16个点位之间在短时间内发生密集报警，火警风险极高</p> -->
          <ul>
            <li class="line-item">
              <span class="left-content"> 设备位置： </span>
              <span class="right-content">
                <myTooltip :str="val._deviceAddress"></myTooltip>
              </span>
            </li>

            <li class="line-item">
              <span class="left-content">平台首次接收时间：</span>
              <span class="right-content">{{
                val.eventTime || '--'
              }}</span>
            </li>
            <li class="line-item">
              <span class="left-content">平台末次接收时间：</span>
              <span class="right-content">{{
                val.lastEventTime || '--'
              }}</span>
            </li>

            <!-- <li class="line-item">
              <span class="left-content"> 平台最新接收时间： </span>
              <span class="right-content">
                {{ val.lastEventTime || '--' }}
              </span>
            </li>
            <li class="line-item">
              <span class="left-content"> 设备最新上报时间： </span>
              <span class="right-content">
                {{ val.lastDeviceTime || '--' }}
              </span>
            </li>
            <li class="line-item">
              <span class="left-content"> 平台首次接收时间： </span>
              <span class="right-content">
                {{ val.eventTime || '--' }}
              </span>
            </li> -->
          </ul>
          <span class="step-tag">{{ stepLabel }}</span>
        </div>
      </el-collapse-item>
      <el-collapse-item title="设备信息" name="3">
        <ul>
          <li class="line-item" v-for="(item, index) in fidleList" :key="index">
            <span class="left-content">{{ item.label }}：</span>
            <span class="right-content">{{ item.value }}</span>
          </li>
        </ul>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import {
  normalizeAddress,
  normalizeLoop,
  getDeviceFieldList
} from '~/common/utils'
import { queryDeviceEventLisBySystemType } from '~/common/services'

const props = defineProps({
  val: {
    type: Object,
    default: () => ({})
  }
})

const fidleList = ref([] as any[])

const deviceInfo = ref({} as any)

const isOnline = ref(false)

const isShowOnline = ['22000000', '23000000', '24000000', '25000000'].includes(
  props.val.deviceTypePid
)

const activeNames = ref(['0', '1', '2', '3'])

const stepLabel = computed(() => {
  const val = props.val._indexLabel
  return (val === '首' ? '' : '第') + val + '起'
})

function getDeviceInfo() {
  queryDeviceEventLisBySystemType({
    deviceIds: props.val.deviceId,
    ownerId: props.val.unitId
  }).then((res: any) => {
    const item = res.rows[0]
    if (item) {
      isOnline.value = item.onlineState === 0
      if (!isOnline.value) {
        item.deviceStateLabel = '离线'
      } else if (item.deviceState == 0) {
        item.deviceStateLabel = '正常'
      } else if (item.deviceState == 1) {
        item.deviceStateLabel = '故障'
      }
      deviceInfo.value = item
    }
  })
}

onMounted(() => {
  fidleList.value = getDeviceFieldList(props.val)
  getDeviceInfo()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.maybe-alarm-detail {
  .monitor-state {
    background: #f6f7fa;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 8px;
    overflow: hidden;
    position: relative;

    .step-tag {
      position: absolute;
      right: 0;
      top: 0;
      background: #5092f3;
      color: white;
      font-size: 14px;
      padding: 2px 6px 2px 8px;
      border-bottom-left-radius: 12px;
    }
  }
}
</style>
