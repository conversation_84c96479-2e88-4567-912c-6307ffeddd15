<template>
  <div class="maybe-alarm-switch-floor">
    <div class="switch-floor--wrapper w-full h-full">
      <div class="arrow-btn" @click="handleSwitchClick(0)">
        <el-icon :size="18"><icon-caret-top /></el-icon>
      </div>
      <div class="list-wrapper" ref="wrapper">
        <ul class="alarm-list">
          <li
            class="list--item text-center"
            :class="{ active: index === activeIndex, alarm: item._isRealAlarm }"
            @click.stop="handleClick(item, index)"
            v-for="(item, index) in props.alarmList"
            :key="index"
          >
            <div class="flex justify-center">
              <div class="floor-info w-90px flex justify-center items-center relative">
                <div class="step-label">{{ item._indexLabel }}</div>
                <div class="floor-name flex-1 text-center">
                  {{ item.floorName }}
                </div>
                <div class="absolute flex items-center real-alarm" v-if="item._isRealAlarm">
                  <svg-icon name="alarm" color="#FF2525" class="inline" :size="20"></svg-icon>
                  <span style="color: #ff2525" class="mt-6px ml-4px">真警</span>
                </div>
              </div>
            </div>
            <img
              @click.stop="nextClick(index)"
              src="@/assets/image/down_arrow_2.png"
              class="relative arrow-icon h-16px inline-block my-10px"
              alt=""
            />
          </li>
        </ul>
      </div>
      <div class="arrow-btn" @click="handleSwitchClick(1)">
        <el-icon :size="18"><icon-caret-bottom /></el-icon>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'

const props: {
  alarmList: any[]
} = defineProps({
  alarmList: {
    type: Array,
    default: () => [],
  },
})

const emits = defineEmits(['change'])

const wrapper = ref()

const activeIndex = ref(0)

function handleClick(item, index) {
  activeIndex.value = index
  emits('change', item, index)
}

function nextClick(index) {
  let nextIndex = index + 1 >= props.alarmList ? 0 : index + 1
  const nextItem = props.alarmList[nextIndex] || {}
  activeIndex.value = nextIndex
  emits('change', nextItem, nextIndex)
}

function handleSwitchClick(flag: number) {
  const el = wrapper.value as HTMLElement,
    wrapperHeight = el.clientHeight,
    scrollTop = el.scrollTop,
    childHeight = el.children[0].clientHeight
  let top = 0
  if (flag === 0) {
    top = scrollTop - wrapperHeight
  } else {
    top = scrollTop + wrapperHeight
  }
}
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.maybe-alarm-switch-floor {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 300px;
  right: -80px;
  z-index: 10;
  .switch-floor--wrapper {
    position: relative;
    // background: white;
    // border-radius: 10px;
    // padding: 0 10px;
    // width: 140px;
    &::before {
      content: '';
      background: white;
      border-radius: 10px;
      width: 110px;
      height: 100%;
      position: absolute;
      right: 95px;
    }
  }
  .arrow-btn {
    text-align: center;
    height: 40px;
    line-height: 40px;
    .el-icon svg {
      width: inherit;
      height: inherit;
    }
  }
  .list-wrapper {
    height: 500px;
    overflow-y: hidden;
    .alarm-list {
      width: 100%;
      .list--item {
        cursor: pointer;
        .floor-info {
          border: 1px solid #b2b2b2;
          border-radius: 18px 18px 18px 18px;
          padding: 1px;
          .step-label {
            background: #999999;
            color: white;
            width: 34px;
            height: 34px;
            border-radius: 50%;
            text-align: center;
            line-height: 34px;
          }
          .real-alarm {
            left: -80px;
          }
        }
        .arrow-icon {
          width: 18px;
          height: 12px;
        }
      }
      .list--item.alarm {
        .floor-info {
          color: #ff2e2e;
          border-color: #ffa0a0;
          background: #fff0f0;
          .step-label {
            background: #ff2e2e;
          }
        }
      }
      .list--item.active {
        .floor-info {
          color: #3363ff;
          border-color: #7393ff;
          background: #f0f3ff;
          .step-label {
            background: #3363ff;
          }
        }
      }
      .list--item:last-child {
        img {
          display: none;
        }
      }
    }
  }
}
</style>
