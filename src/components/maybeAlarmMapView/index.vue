<template>
  <div class="maybe-alarm-map-view w-full h-full" v-loading="isLosading">
    <switch-floor :alarm-list="floorList" @change="handleChange"></switch-floor>
    <div class="w-full h-full" :id="gisID"></div>
    <div class="alarm-unitName-box" v-if="detailInfo && detailInfo.unitName">
      {{ detailInfo.unitName }}
    </div>
    <popup-side v-model="isShowDetail" popup-title="详情">
      <template #subTitle>
        <span class="alarm-tag">高度疑似真警</span>
      </template>
      <device-detail :val="deviceInfo"></device-detail>
    </popup-side>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import switchFloor from './switchFloor.vue'
import deviceDetail from './deviceDetail.vue'

window.IndoorMap.initAlarmPlugin()
window.IndoorScene.initAlarmPlugin()

// const props = defineProps({
//     detailInfo: {
//         type: Object,
//         default: () => _data
//     }
// })

const detailInfo = ref({
  children: [],
} as any)

const floorInfoRef = ref()

const deviceInfo = ref<any>({})

const isShowDetail = ref(false)

const currentFloorId = computed(() => deviceInfo.value.floorId)
const isLosading: any = ref(false)

const alarmList = computed(() =>
  [].concat(detailInfo.value.children.filter((item) => item.floorId === currentFloorId.value))
)

const floorList = computed(() => [].concat(detailInfo.value.children))

const gisID = 'gsmap_floor_' + Date.now()

let pIndoor: null | {
  showFloorData: (...args) => void
  onDeviceSelected: (...args) => void
  zoomToExtent: (...args) => void
  addPopup: (...args) => void
  show: (...args) => void
  getGeoDeviceByDeviceId: (...args) => any
} = null

const temp: any = ref({})
function handleChange(item, index) {
  temp.value = deviceInfo.value
  deviceInfo.value = item
  renderFloor()
}

function renderFloor() {
  // return new Promise<void>((resolve) => {
  var mapType = deviceInfo.value.floorMapType
  pIndoor!.showFloorData(
    typeof mapType === 'number' ? mapType : window.IndoorMap.ViewType.IndoorAreaVector,
    undefined,
    undefined,
    currentFloorId.value,
    '',
    function (mapType, success, objArgs, indoor) {
      isLosading.value = false
      // window.indoor = indoor
      objArgs.cancelZoomToExtent = true
      const fieldNameXYZ = indoor.getDeviceFieldNameXYZ(mapType)
      const fieldNameX = fieldNameXYZ[0]
      const fieldNameY = fieldNameXYZ[1]
      const fieldNameZ = fieldNameXYZ[2]

      const xyArray = [deviceInfo.value[fieldNameX], deviceInfo.value[fieldNameY]]
      // if (pIndoor.Type === 'Scene') {
      window.IndoorScene.projectXY(
        xyArray,
        GISShare.SMap.SpatialReference.ProjectedCoordinateSystemStyle.eWGS_1984_Web_Mercator_Auxiliary_Sphere_BD09MC
      )
      // } else {
      //   window.IndoorMap.projectXY(
      //     xyArray,
      //     GISShare.SMap.SpatialReference.ProjectedCoordinateSystemStyle
      //       .eWGS_1984_Web_Mercator_Auxiliary_Sphere_BD09MC
      //   )
      // }
      // pIndoor?.zoomToExtent(xyArray)

      indoor.showFloorDataDevice(alarmList.value, fieldNameX, fieldNameY, fieldNameZ, undefined)
      const indoorDataState = indoor.getIndoorDataState()
      // alert(indoorDataState.floorId)
      indoor.AppendAlarmArrayToDeviceLayer(detailInfo.value.children, indoorDataState.floorId)
      if (deviceInfo.value.floorId !== temp.value.floorId) {
        updateFloorInfo()
      }
      addDevicePopup(deviceInfo.value)
      pIndoor?.zoomToExtent()
      // resolve()
    }
  )
  // })
}
const addDevicePopup = (data, e?: any, obj?: any) => {
  if (!data && !e) return
  let coordinate = getCoordinateByDeviceId(data.deviceId)
  if (!coordinate) return

  let _container = document.createElement('div')
  _container.classList.add('floor-popup')
  _container.innerHTML = `<div id="floorPopup" style='min-height:60px; z-index:18;width:30px'>
    <img class='my-img' style='width:30px' src="data:image/png;base64,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" alt="">
    </div>`
  let p: any = pIndoor?.addPopup({
    element: _container,
    single: true, // 控制popup只存在一个还是多个
    custom: true, // 自定义content开关
    autoPan: true,
    // 'dx' : e.objPoint['x'],
    dy: -10,
  })
  p?.show({
    x: e?.objPoint['x'] || coordinate[0],
    y: e?.objPoint['y'] || coordinate[1],
    z: e?.objPoint['z'] || 3,
  })
  // let _docu = p.getElement();
  // setPopup({
  //   elment: _docu.firstChild,
  //   options: {
  //     pointData: data,
  //   }
  // })
  return false
}
// 根据设备id，获取点位坐标信息
function getCoordinateByDeviceId(_deviceId) {
  // 设置设备点位坐标
  let coordinate: any = null
  // 根据设备id，获取设备geo对象
  let geoObj = pIndoor?.getGeoDeviceByDeviceId(_deviceId)
  if (geoObj) {
    coordinate = geoObj.getGeometry().getCoordinates()
    // 判断pPoint是否继承自GISShare.SMap.Geometry.IPoint  (pPoint是否是对象的形式)
    if (GISShare.EX.Interface.ImplementOf(coordinate, GISShare.SMap.Geometry.IPoint))
      coordinate = [coordinate.getX(), coordinate.getY()]
    // 判断pPoint设置Z值
    if (geoObj.options) coordinate[2] = geoObj.options.altitude + geoObj.options.height

    return coordinate
  } else {
    return null
  }
}

function updateFloorInfo() {
  const val = deviceInfo.value

  floorInfoRef.value.init({
    floorId: val.floorId,
    buildId: val.buildingId,
    subCenterCode: val.subCenterCode,
    unitId: val.unitId,
    unitType: val.unitType,
  })
}

function onDeviceSelected(data) {
  deviceInfo.value = data
  isShowDetail.value = true
}

function initMap() {
  const ui = deviceInfo.value

  const is2D = ui.bitmap === '1' || ui.aerialMapType >= 0

  const options = window.IndoorMap.Merge([
    window.CONST_GSOptions,
    {
      tileURL: window.CONST_GSParams.tileURL_Satellite,
      tileURL_Traffic: window.CONST_GSParams.tileURL_Traffic,
      tileURL_Annotation: window.CONST_GSParams.tileURL_Annotation,
      adminCodeDicCache: window.CONST_GSCache.adminCodeDicCache,
      target: gisID, //'gsmap_floor',
      sky: true,
      animation: true,
      dragRotate: true,
      dragPitch: true,
      is3DView: true,
      uiScale: true,
      uiScaleOptions: { hide: true },
      uiZoom: false,
      uiCompass: true,
      uiCompassOptions: { hide: true, position: 'bottom-right' },
      tile: false,
      maxZoom: 24,
      minZoom: 3,
      zoom: 18,
      center: [0, 0],
      extent_expand_x: 1,
      extent_expand_y: 1,
      isVector: true,
      videoBufferQuery: false,
      deviceIconAlarmGifler: true,
      ovUnitModelActivatable: true,
      onLoad: (sender) => {
        pIndoor = sender
        renderFloor()
      },
      gsTag_UnitInfo: {
        unitId: ui.unitId,
        unitType: ui.unitType,
        modelType: ui.floorMapType,
      },
    },
  ])
  // if (!is2D) {
  pIndoor = new window.IndoorScene(
    window.IndoorMap.Merge(
      {
        styleInfo: window.CONST_GSParams.styleInfo_3D,
        styleInfoGrid: window.CONST_GSParams.styleInfoGrid_3D,
        indoorAreaDicCache: window.CONST_GSCache.indoorAreaDicCache_Scene,
        indoorAreaExtentDicCache: window.CONST_GSCache.indoorAreaExtentDicCache_Scene,
        gridAreaDicCache: window.CONST_GSCache.gridAreaDicCache_Scene,
        ovUnitModelInfoDicCache: window.CONST_GSCache.ovUnitModelInfoDicCache_Scene,
      },
      options
    )
  )
  // } else {
  //   pIndoor = new window.IndoorMap(
  //     window.IndoorMap.Merge(
  //       {
  //         styleInfo: window.CONST_GSParams.styleInfo_2D,
  //         styleInfoGrid: window.CONST_GSParams.styleInfoGrid_2D,
  //         indoorAreaDicCache: window.CONST_GSCache.indoorAreaDicCache_Map,
  //         indoorAreaExtentDicCache:
  //           window.CONST_GSCache.indoorAreaExtentDicCache_Map,
  //         gridAreaDicCache: window.CONST_GSCache.gridAreaDicCache_Map
  //       },
  //       options
  //     )
  //   )
  // }

  pIndoor!.onDeviceSelected = onDeviceSelected
}

function showFloor(data) {
  detailInfo.value = data
  deviceInfo.value = data.children[0]
  isLosading.value = true

  initMap()
}

defineExpose({
  showFloor,
})

// onMounted(() => {

//     deviceInfo.value = props.detailInfo!.children[0];
//     // isShowDetail.value = true;
//     initMap()
// })
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.maybe-alarm-map-view {
  position: relative;
  // background: black;
  background-color: #ddd;

  .alarm-tag {
    border-radius: 2px 2px 2px 2px;
    background: rgba(255, 239, 239, 1);
    border: 1px solid #ff9696;
    color: #ff4545;
    margin-left: 10px;
    padding: 6px;
  }
}

// div {
//   width: 100px;
//   height: 100px;
//   background-color: red;
//   animation-name: example;
//   animation-duration: 4s;
// }
.alarm-unitName-box {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  background: #26a0fb;
  border-radius: 0 0 8px 8px;
  padding: 10px;
  color: #fff;
  z-index: 99;
}

@keyframes example {
  0% {
    transform: translateY(0);
  }

  60% {
    transform: translateY(20px);
  }

  100% {
    transform: translateY(0px);
  }
}

.my-img {
  animation-name: example;
  animation-duration: 2s;
  animation-iteration-count: infinite;
}
</style>
