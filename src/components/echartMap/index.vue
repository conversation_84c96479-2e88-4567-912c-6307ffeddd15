<template>
  <div class="map-box">
    <div class="chinaMap" id="chinaMap" @click="mapClick"></div>
    <div class="menu-box">
      <chartMenu></chartMenu>
    </div>

    <!--点位重叠弹框遮罩  -->
    <div class="detail-zz" @click="popupVisible = false" v-if="popupVisible"></div>
    <div class="detail-zz" @click="counterStore.isShowMapDetail = false" v-if="isShowMapDetail">
      <!--单位详情遮罩  -->
    </div>

    <Transition name="slide-fade">
      <div class="detail-box" v-if="isShowMapDetail" ref="detailBoxRef">
        <unitDetail :type="type" :key="counterStore.currentUnit.unitId"></unitDetail>
      </div>
    </Transition>

    <customPopup v-model="popupVisible" ref="popupRef" :currentList="currentList" @toDetails="toDetail"></customPopup>
  </div>
</template>

<script lang="ts" setup>
import $API from '~/common/api'
import config from '~/config'
import chartMenu from './mapMenu.vue'
import unitDetail from './unitDetail.vue'
import customPopup from './customPopup.vue'
import { computed, watch, ref, nextTick, onUnmounted } from 'vue'
import { useCounterStore, useUserInfo } from '~/store'
import { onMounted } from 'vue'
const userInfo = useUserInfo()
const props = defineProps({
  type: {
    type: String,
    default: 'safe', // safe: 安全一张图 device: 设备一张图(不传也是安全一张图)
  },
  modelValue: {
    type: Object,
    default: () => {},
  },
  jumpUrl: {
    type: String,
    default: '',
  },
})
const mapRange = userInfo.value.mapRange ? JSON.parse(userInfo.value.mapRange) : []
const counterStore = useCounterStore()
const isCollapse = computed(() => counterStore.isCollapse)

const emits = defineEmits(['update:modelValue'])

const tempCurrentUnit: any = ref({})
const isShowMapDetail = computed(() => {
  emits('update:modelValue', tempCurrentUnit.value)
  return counterStore.isShowMapDetail
})

watch(
  () => props.modelValue,
  (n) => {
    // 下级单位
    if (n.superviseType == '1') tempCurrentUnit.value = n
  },
  { deep: true }
)

let myChart: any = null
let mapName = 'china' //   全国地图 china    省份地图 map
// let mapName = 'Thailand'  // 泰国地图
if (mapRange.length > 0) {
  if (mapRange.includes('000000')) {
    // mapName = 'Thailand'
    mapName = 'china'
  } else {
    mapName = 'map'
  }
}

watch(
  isCollapse,
  (newVal) => {
    if (newVal) {
      setTimeout(() => {
        myChart && myChart.resize()
      }, 250)
    } else {
      setTimeout(() => {
        myChart && myChart.resize()
      }, 250)
    }
  },
  { immediate: false }
)

onMounted(() => {
  drawChart([])
})

async function getJSON(cb) {
  if (mapName == 'china' || mapName == 'Thailand') {
    const res: any = await $API.get({
      url: config.root_dir + '/json/' + mapName + '.json',
    })
    const mapjson = {
      type: 'FeatureCollection',
      features: res.features.filter((i) => i && i.properties.name),
    }
    cb(mapjson)
  } else {
    let arr: any[] = []
    mapRange.forEach((i) => {
      arr.push(
        $API.get({
          url: config.root_dir + '/json/' + i + '.json',
        })
      )
    })
    const json = await Promise.all(arr)
    const features: any[] = []
    json.forEach((item) => {
      features.push(...item.features)
    })
    const mapjson2 = {
      type: 'FeatureCollection',
      features,
    }
    cb(mapjson2)
  }
}

const bool = ref(false)
const zoom = ref(1.1)

const popupVisible = ref(false)
const popupRef = ref()
const currentList = ref([])

const mapClick = () => {
  popupVisible.value = false
  counterStore.isShowMapDetail = false
}

function drawChart(data) {
  if (!myChart) {
    if (!document.getElementById('chinaMap')) return
    myChart = echarts.init(document.getElementById('chinaMap') as any)
    myChart &&
      myChart.on('click', function (params) {
        // 获取点击的位置坐标
        const pointX = params.event.offsetX
        const pointY = params.event.offsetY

        // GetDataByClientXY第一个参数是查找的序列,最后一个参数与点击point的尺寸有关系,容差
        currentList.value = myChart.GetDataByClientXY(undefined, pointX, pointY, 5)
        if (currentList.value.length == 1) {
          if (params.seriesName == 'unit') {
            popupVisible.value = false
            counterStore.currentUnit = {
              unitId: params.data.unitId,
              unitName: params.data.unitName,
            }

            setTimeout(() => {
              counterStore.isShowMapDetail = true
            }, 100)
          }
        } else if (currentList.value.length > 0) {
          setTimeout(() => {
            popupVisible.value = true
            // 计算弹框的位置，使其显示在点位的正上方
            const el = popupRef.value.$refs.customPopupRef
            nextTick(() => {
              const popupX = pointX - el.clientWidth / 2
              const popupY = pointY - el.clientHeight - 20
              el.style.left = popupX + 'px'
              el.style.top = popupY + 'px'
              el.style.zIndex = 9
            })
          }, 100)
        } else if (currentList.value.length == 0) {
          popupVisible.value = false
        }
      })

    // 当地图类型为省份map时才开启放大缩小拖放功能
    // if (mapName === 'map') {
    //   bool.value = false
    //   zoom.value = 0.9
    //   //捕捉geo roam事件，使下层的geo随着上层的geo一起实现缩放拖曳
    //   myChart &&
    //     myChart.on('georoam', (params: any) => {
    //       const option = myChart.getOption() //获得option对象
    //       if (params.zoom != null) {
    //         //捕捉到缩放时
    //         option.geo[0].zoom = option.series[0].zoom //下层geo的缩放等级跟着上层的geo一起改变
    //         option.geo[0].center = option.series[0].center //下层的geo的中心位置随着上层geo一起改变
    //         option.geo[1].zoom = option.series[0].zoom //下层geo的缩放等级跟着上层的geo一起改变
    //         option.geo[1].center = option.series[0].center //下层的geo的中心位置随着上层geo一起改变
    //       } else {
    //         //捕捉到拖曳时
    //         option.geo[0].center = option.series[0].center //下层的geo的中心位置随着上层geo一起改变
    //         option.geo[1].center = option.series[0].center //下层的geo的中心位置随着上层geo一起改变
    //       }
    //       myChart.setOption(option, false) //设置option
    //     })
    // }

    window.onresize = () => {
      myChart.resize()
    }
  }
  /**
    此版本通过设置geoindex && seriesIndex: [1] 属性来实现geo和map共存，来达到hover散点和区域显示tooltip的效果

    默认情况下，map series 会自己生成内部专用的 geo 组件。但是也可以用这个 geoIndex 指定一个 geo 组件。这样的话，map 和 其他 series（例如散点图）就可以共享一个 geo 组件了。并且，geo 组件的颜色也可以被这个 map series 控制，从而用 visualMap 来更改。
    当设定了 geoIndex 后，series-map.map 属性，以及 series-map.itemStyle 等样式配置不再起作用，而是采用 geo 中的相应属性。

    http://echarts.baidu.com/option.html#series-map.geoIndex

    并且加了pin气泡图标以示数值大小
    */
  const points: any = []
  const colors = [
    {
      className: 'excellent', // 优秀
      color: '#0ab76d',
    },
    {
      className: 'good', // 良好
      color: '#4fc6f8',
    },
    {
      className: 'pass', //及格
      color: '#ff852e',
    },
    {
      className: 'danger', // 危险
      color: '#ff5039',
    },
  ]
  data.map((i) => {
    points.push({
      value: [i.lnglat.lng, i.lnglat.lat],
      unitName: i.unitName,
      unitId: i.unitId,
      socre: i.socre,
    })
  })
  // myChart.showLoading()

  getJSON(function (geoJson) {
    echarts.registerMap(mapName, geoJson)
    // myChart.hideLoading()

    const option = {
      // backgroundColor: '#0c1836',
      backgroundColor: 'rgba(0,0,0,0)',
      geo: [
        {
          map: mapName,
          // center: [100.97, 36.15],
          aspectScale: 0.75, //长宽比
          zoom: zoom.value,
          roam: bool.value,
          animationDurationUpdate: 0,
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0,
                    color: '#001e65', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#001e65', // 100% 处的颜色
                  },
                ],
                globalCoord: true, // 缺省为 false
              },
              // borderColor:'red',
              // borderWidth:4,
              shadowColor: '#027CEE',
              shadowOffsetX: 2,
              shadowOffsetY: 10,
              // shadowBlur: 1.5
            },
          },
          regions: [
            {
              name: '南海诸岛',
              itemStyle: {
                areaColor: 'rgba(0, 10, 52, 1)',

                borderColor: 'rgba(0, 10, 52, 1)',
                normal: {
                  opacity: 0,
                  label: {
                    show: false,
                    color: '#001e65',
                  },
                },
              },
            },
          ],
          silent: true,
        },
        {
          map: mapName,
          // center: [100.97, 36.15],
          aspectScale: 0.75, //长宽比
          zoom: zoom.value,
          roam: bool.value,
          animationDurationUpdate: 0,
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0,
                    color: '#017cea', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#017cea', // 100% 处的颜色
                  },
                ],
                globalCoord: true, // 缺省为 false
              },
              shadowColor: '#017cea',
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              shadowBlur: 50, //修改地图发光大小
            },
          },
          regions: [
            {
              name: '南海诸岛',
              itemStyle: {
                areaColor: '#017cea',

                borderColor: '#017cea',
                normal: {
                  opacity: 0,
                  label: {
                    show: false,
                    color: '#009cc9',
                  },
                },
              },
            },
          ],
          silent: true,
        },
      ],
      series: [
        {
          map: mapName, //使用
          type: 'map',
          roam: bool.value,
          animationDurationUpdate: 0,
          selectedMode: false,
          // center: [100.97, 36.15],
          label: {
            normal: {
              show: false,
              textStyle: {
                color: '#1DE9B6',
              },
              formatter: (p) => {
                switch (p.name) {
                  case '内蒙古自治区':
                    p.name = '内蒙古'
                    break
                  case '西藏自治区':
                    p.name = '西藏'
                    break
                  case '新疆维吾尔自治区':
                    p.name = '新疆'
                    break
                  case '宁夏回族自治区':
                    p.name = '宁夏'
                    break
                  case '广西壮族自治区':
                    p.name = '广西'
                    break
                  case '香港特别行政区':
                    p.name = '香港'
                    break
                  case '澳门特别行政区':
                    p.name = '澳门'
                    break
                }
                return p.name
              },
            },
            emphasis: {
              show: false,
              // textStyle: {
              //   show: false,
              //   color: 'rgb(183,185,14)'
              // }
            },
          },
          itemStyle: {
            normal: {
              // borderColor: 'rgb(147, 235, 248)',
              borderColor: '#1875ED',
              borderWidth: 1,
              areaColor: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0,
                    // color: '#001e65' // 0% 处的颜色
                    color: 'rgba(0, 30, 101, 1)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    // color: '#001e65' // 100% 处的颜色
                    color: 'rgba(0, 30, 101, 1)',
                  },
                ],
                // globalCoord: true // 缺省为 false
              },
            },
            emphasis: {
              areaColor: '#001e65',
              // areaColor: 'red',
              //    shadowColor: 'rgb(12,25,50)',
              borderWidth: 0.1,
            },
          },
          zoom: zoom.value,
          // roam: false,
          // data: this.difficultData //热力图数据   不同区域 不同的底色
        },
        {
          name: 'unit',
          type: 'scatter',
          coordinateSystem: 'geo',
          showEffectOn: 'render',
          zlevel: 1,
          rippleEffect: {
            period: 15,
            scale: 4,
            brushType: 'fill',
          },
          hoverAnimation: true,
          label: {
            normal: {
              formatter: '{b}',
              position: 'right',
              offset: [15, 0],
              color: '#1DE9B6',
              show: true,
            },
          },
          itemStyle: {
            normal: {
              color: function (params) {
                //随机颜色
                let className = ''
                if (params.data.socre >= 90) className = 'excellent'
                else if (params.data.socre >= 80 && params.data.socre < 90) className = 'good'
                else if (params.data.socre >= 60 && params.data.socre < 80) className = 'pass'
                else if (params.data.socre < 60) className = 'danger'
                return colors.find((item) => item.className === className)?.color
              },
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
          symbolSize: 10,
          data: points || [],
        },
      ],
    }
    myChart.setOption(option, true)
  })
}
const detailBoxRef = ref()

const toDetail = (item) => {
  counterStore.currentUnit = {
    unitId: item.unitId,
    unitName: item.unitName,
  }
  setTimeout(() => {
    counterStore.isShowMapDetail = true
    nextTick(() => {
      detailBoxRef.value.style.zIndex = 10
    })
  }, 100)
}

const mktlng = (poi) => {
  // 墨卡托转经纬度
  let lnglat: any = {}
  if (!poi.unitPointX || !poi.unitPointY) return lnglat
  const pPoint = new window.GISShare.SMap.Geometry.Point(poi.unitPointX, poi.unitPointY)
  window.GISShare.SMap.Fitting.FittingHelper.Fit(
    pPoint,
    window.GISShare.SMap.SpatialReference.ProjectedCoordinateSystemStyle.eWGS_1984_Web_Mercator_Auxiliary_Sphere_BD09MC,
    window.GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_GCJ02
  )
  lnglat.lng = pPoint.getX()
  lnglat.lat = pPoint.getY()
  return lnglat
}

const setData = (info) => {
  if (info.superviseType === '2') {
    // 企业单位直接打开详情
    counterStore.currentUnit = {
      unitId: info.unitId,
      unitName: info.unitName,
      superviseType: info.superviseType,
    }
    setTimeout(() => {
      counterStore.isShowMapDetail = true
    }, 100)
  }
  let jsonData = JSON.parse(JSON.stringify(info.data))
  jsonData.forEach((i) => {
    i.lnglat = mktlng(i)
  })

  drawChart(jsonData)
}
onUnmounted(() => {
  counterStore.isShowMapDetail = false
})
defineExpose({ setData, isShowMapDetail })
</script>

<style scoped lang="scss">
.map-box {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;

  .menu-box {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    height: 30px;
    // width: calc(100% - 860px);
    width: 100%;
    padding: 0 20px;
    z-index: 5;
  }

  .detail-box {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }

  .detail-zz {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 3;
  }
}

.chinaMap {
  width: 100%;
  height: 100%;
  backgroundcolor: '#0c1836';
  // background-image: url(../../assets/bj001.png.png);
  background-size: 100% 100%;
  z-index: 4;
  // max-width: 1600px;
  // background-color: red;
}

.red1 {
  color: red;
}
</style>
