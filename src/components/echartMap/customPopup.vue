<!-- customPopup -->
<template>
  <div id="custom-popup" ref="customPopupRef" v-show="modelValue">
    <div class="header">
      <div>单位列表</div>
      <div class="close-btn">
        <el-icon :size="24" @click="close">
          <Close />
        </el-icon>
      </div>
    </div>
    <div class="content">
      <div
        class="item"
        v-for="(item, index) in currentList"
        :key="index"
        @click="toDetails(item, $event)"
      >
        <myTooltip :str="item.unitName"></myTooltip>
      </div>
    </div>
    <span class="pointTo"></span>
  </div>
</template>

<script lang="ts" setup>
import { Close } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  currentList: {
    type: Array,
    default: () => []
  }
})
const customPopupRef = ref()

const emits = defineEmits(['toDetails', 'update:modelValue'])

const toDetails = (item, e) => {
  emits('toDetails', item, e)
}

const close = () => {
  emits('update:modelValue', false)
}

onMounted(() => {
  customPopupRef.value = document.querySelector('#custom-popup')
})
</script>

<style lang="scss" scoped>
#custom-popup {
  position: absolute;
  width: 250px;
  background: rgba(0, 16, 50, 0.9);
  border: 1px solid #079afd;
  box-shadow: 0px 0px 16px 0px rgba(10, 212, 255, 0.54);
  border-radius: 5px;
  z-index: 4;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 40px;
    color: #ffffff;
    background: linear-gradient(90deg, #0262cf 20%, #001238 60%);
    padding: 0 15px;

    .close-btn {
      cursor: pointer;
    }
  }

  .content {
    width: 230px;
    height: 135px;
    overflow: hidden;
    margin: 10px 15px;

    .item {
      width: 218px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      background: #0c1f4f;
      border: 1px solid #0057b0;
      color: #dae1ef;
      margin: 8px 0;
      padding: 0 10px;
      cursor: pointer;

      &:hover {
        // text-decoration: underline;
        background-image: url(@/assets/image/yzt/cur_item.png);
        background-size: cover;
      }
    }

    &:hover {
      overflow-y: overlay;
      scrollbar-gutter: stable;
    }

    &::-webkit-scrollbar-thumb {
      background: #386bbb;
    }

    &::-webkit-scrollbar {
      background: #36425e;
      border-radius: 2px;
    }
  }

  .pointTo {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: rgba(0, 16, 50, 0.9);
    border: 1px solid #001032;
    transform: translate(-50%, 50%) rotate(45deg);
    position: absolute;
    bottom: 0;
    left: 50%;
  }
}
</style>
