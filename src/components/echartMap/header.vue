<template>
  <div>
    <div class="header-bg">
      <span class="time">{{ dayTime }}</span>
      <svg-icon name="signOut" class="sign-out" :size="18" track @click="exit"></svg-icon>
      <!-- ui.systemMidName|| -->
      <div class="ttile header-fzst">
        {{ ui.systemMidName || '监督管理信息综合展示系统' }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import $API from '~/common/api'
import { ref, onMounted, h } from 'vue'
import { useUserInfo } from '@/store'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { loginPath } from '@/router/loginPath'

const ui = useUserInfo()
const router = useRouter()
const dayTime = ref(dayjs(`${new Date()}`).format('YYYY-MM-DD HH:mm:ss'))

function exit() {
  const val = ui.value
  ElMessageBox({
    title: '提示',
    message: h('span', null, '确认退出登录吗?'),
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        $API
          .post({
            method: 'post',
            url: '/login/logOut',
            params: {
              client: 'WEB',
              orgCode: val.unitId,
              userId: val.userId,
              userToken: val.userToken,
            },
          })
          .then((res: any) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (res && res.code === 'success') {
              ui.value = undefined as any
              sessionStorage.clear()

              // ElMessage.success('退出成功')
              done()
              router.push(loginPath.value)
            }
          })
      } else {
        done()
      }
    },
  })
}

onMounted(() => {
  var timer
  clearInterval(timer)
  timer = setInterval(() => {
    dayTime.value = $API.getDayTime()
  }, 1000)
})
</script>

<style scoped lang="scss">
.header-bg {
  height: 118px;
  // background-color: #0c1836;
  background-image: url(../../assets/image/yzt/yzt.png);
  background-size: 100% 104px;
  background-repeat: no-repeat;
  // background-position: center;
  position: relative;

  .time {
    position: absolute;
    right: 50px;
    top: 12px;
    color: rgba(62, 180, 255, 1);
    font-size: 16px;
  }

  .sign-out {
    position: absolute;
    right: 20px;
    top: 10px;
    color: #0ba0ff;
    cursor: pointer;

    svg {
      width: inherit;
      height: inherit;
    }
  }

  .sign-out:hover {
    color: #5bc0ff;
  }

  .sign-out:active,
  .sign-out:focus {
    color: #0070b7;
  }

  .ttile {
    text-align: center;
    font-size: 36px;
    // font-family: FZCuHeiSongS-B-GB;
    font-weight: 400;
    color: #ffffff;
    padding-top: 14px;
    letter-spacing: 8px;
    text-shadow: 0px 2px 8px rgba(10, 35, 81, 0.9);
  }
}

video {
  position: absolute;
  top: 0;
  right: 0;
  mix-blend-mode: screen;
}
</style>
