<template>
  <div class="unitDetail" :style="{
    width: type != 'device' ? '350px' : ''
  }">
    <div class="title">
      <div>
        <myTooltip :str="info.unitName" :size="18"></myTooltip>
      </div>
      <div class="close-btn">
        <el-icon :size="20" @click="close">
          <Close />
        </el-icon>
      </div>
    </div>

    <div class="main">
      <div class="details" :class="type !== 'device' ? 'w-[calc(100%-95px)]' : 'w-full'">
        <div class="unitAddress">
          地址：
          <el-tooltip effect="dark" :content="info.unitAddress" placement="top">
            <template #content> {{ info.unitAddress }} </template>
            {{ info.unitAddress || '--' }}
          </el-tooltip>
        </div>
        <div class="">
          建筑面积：{{
    Math.floor(info?.baseInfo?.buildingArea * 100) / 100 || 0
  }}㎡
        </div>
        <div class="btn-box" v-if="type !== 'device'" @click="toDetails">
          <div class="btn">详情</div>
        </div>
        <template v-else-if="type === 'device'">
          <div class="">物联网设备在线率：{{ info.onlineRate || '0%' }}</div>
          <div class="flex justify-between items-center">
            <div>消防设施完好率：{{ info.rate || '0%' }}</div>
          </div>
          <div class="btn-box deviceBtn-box">
            <div class="btn" @click="toDetails" track>详情</div>
          </div>
        </template>
      </div>
      <div class="aqzs-box" :class="{
    pass: info.scoreLevel == '及格',
    good: info.scoreLevel == '良好',
    danger: info.scoreLevel == '危险'
  }" v-if="type !== 'device'">
        <div class="" style="font-size: 24px">
          {{ info.socre || 0
          }}<span class="pl-3px" style="color: #92a1bb">分</span>
        </div>
        <!-- <div>安全指数</div> -->
        <div class="tip" :class="{
    'tip-pass': info.scoreLevel == '及格',
    'tip-good': info.scoreLevel == '良好',
    'tip-danger': info.scoreLevel == '危险'
  }">
          {{ info.scoreLevel || '--' }}
        </div>
      </div>
      <div class="footer">
        <template v-if="type !== 'device'">
          <!-- <div class="left"> -->
          <!-- <div class="item " style="cursor: pointer;" v-for=" (n, index ) in statisticsInfo" @click="goSituation(n)"
              :key='index'>
              <div v-if='n.oneLevelName != "消防安全管理"'>{{ n.oneLevelName }}</div>
              <div v-else>{{ n.oneLevelName.replace('消防', '') }}</div>
              <div class="num">{{ n.riskCount || 0 }} <span>项异常</span></div>
            </div> -->
          <!-- <div class="item ">
              <div>消防设施</div>
              <div class="num">{{ statisticsInfo.warnNum || 0 }} <span>项异常</span></div>
            </div>
            <div class="item ">
              <div>安全管理</div>
              <div class="num">{{ statisticsInfo.hazardNum || 0 }} <span>项异常</span></div>
            </div>
            <div class="item ">
              <div>火灾风险源</div>
              <div class="num">{{ statisticsInfo.faultNum || 0 }}<span>项异常</span></div>
            </div> -->
          <!-- </div> -->
          <div class="w-full min-h-160px">
            <el-image class="w-full h-full" :src="setImgUrl(info)">
              <template #error>
                <div class="el-image__error">
                  <custom-empty class="w-full w-full relative"></custom-empty>
                </div>
              </template>
            </el-image>
          </div>
        </template>
        <template v-else>
          <div class="deviceDetails-content">
            <div class="item">
              <div class="name">接入设备总数</div>
              <div class="num">{{ info?.dataOver?.total || 0 }}</div>
            </div>
            <div class="item">
              <div class="name">火灾报警设备</div>
              <div class="num">{{ info?.dataOver?.alarmNum || 0 }}</div>
            </div>
            <div class="item">
              <div class="name">电气火灾设备</div>
              <div class="num">{{ info?.dataOver?.electricalNum || 0 }}</div>
            </div>
            <div class="item">
              <div class="name">消防水系统设备</div>
              <div class="num">{{ info?.dataOver?.fireControlNum || 0 }}</div>
            </div>
            <div class="item">
              <div class="name">可燃气体设备</div>
              <div class="num">{{ info?.dataOver?.combustibleNum || 0 }}</div>
            </div>
            <div class="item">
              <div class="name">其他设备</div>
              <div class="num">{{ info?.dataOver?.otherNum || 0 }}</div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch, reactive } from 'vue'
import { useRouter, useRoute, RouteRecordRaw } from 'vue-router'
import PubSub from 'pubsub-js'
import $API from '~/common/api'
import { useUserInfo, useCounterStore } from '~/store'
import { Close } from '@element-plus/icons-vue'
import config from '~/config'
import { ElMessage } from 'element-plus'
const userInfo = useUserInfo()
const router = useRouter()
const route = useRoute()

const props = defineProps({
  type: {
    type: String,
    default: 'safe' // safe: 安全一张图 device: 设备一张图(不传也是安全一张图)
  }
})

const isHasJump = !route.path.includes('/safety-management/device_map')
const counterStore = useCounterStore()
const currentUnit = computed(() => counterStore.currentUnit)
const info: any = ref({})
const statisticsInfo: any = ref([])
const list = reactive([
  {
    name: '设备接入总数',
    value: 0
  },
  {
    name: '火灾报警设备',
    value: 0
  },
  {
    name: '电气火灾设备',
    value: 0
  },
  {
    name: '消防水系统设备',
    value: 0
  },
  {
    name: '可燃气体设备',
    value: 0
  },
  {
    name: '其他设备',
    value: 0
  }
])
const scoreInfo: any = ref({})
function setImgUrl(data) {
  if (data && Object.keys(data).length == 0) return ''
  return config.image_url_new + data.baseInfo.aerialviewImg
}
onMounted(() => {
  getdetails()
  getNewstScore()
  getNum()
})
const close = () => {
  counterStore.isShowMapDetail = false
}
const goSituation = (val) => {
  router.push({
    path: '/fireSafetySituation/exponentialTrend/riskFactors',
    query: {
      unitId: info.value.unitId,
      unitName: info.value.unitName,
      oneLevelName: val.oneLevelName
    }
  })
}
const getdetails = async () => {
  const params = {
    unitId: currentUnit.value.unitId
    // unitType: 2
  }
  let res: any = null
  try {
    res = await $API.post({
      url: `/security/management/${props.type === 'device' ? 'device' : 'unit'
        }/details`,
      params
    })
    if (res.code != 'success') return
    info.value = {
      ...res.data,
      unitId: currentUnit.value.unitId
    }
  } catch (e) {
    //TODO handle the exception
  }
}
const getNewstScore = async () => {
  const params = {
    unitId: currentUnit.value.unitId
  }
  let res: any = null
  try {
    res = await $API.post({
      url: `/monitor/getNewstScore`,
      params
    })
    if (res.code != 'success') return
    scoreInfo.value = res.data
  } catch (e) {
    //TODO handle the exception
  }
}
const getNum = async () => {
  return
  const params = {
    unitId: currentUnit.value.unitId
  }
  try {
    const res = await $API.post({
      url: '/fireSafety/getRiskNumByUnitId',
      params
    })
    if (res.code !== 'success') return
    statisticsInfo.value = res.data
  } catch (e) {
    //TODO handle the exception
  }
}
const toDetails = () => {
  // v-if="type !== 'device'"
  // type==device   设备一张图

  const { unitId, unitName } = currentUnit.value as any

  sessionStorage.setItem('gobackFlg', '-1')
  // counterStore.isShowMapDetail = false
  const { recordId } = scoreInfo.value
  if (!recordId)
    return ElMessage.error({
      message: '当前单位无安全指数评估记录'
    })
  if (props.type == 'safe') {
    router.push(
      `/fireRemoteManage/fireremotemanage-unitmonitor/evaluationRecord?isShowBack=1&showName=true&unitName=${unitName}&unitId=${unitId}&id=${recordId}&subCenterCode=&viewType=shdw_qydw&unitType=0`
    )
    // ?id=1125591899776745472mzzllrhcgpqw19&showName=true&unitName=清华大学合肥公共安全研究院
  } else if (props.type == 'device') {

    if (!isHasJump) {
      router.push(
        `/fireRemoteManage/fireremoteManageUnitmap?showName=true&unitId=` +
        unitId +
        '&unitName=' +
        unitName
      )
      return
    }

    router.push(
      `/fireRemoteManage/fireremoteManage-unitmap?showName=true&unitId=` +
      unitId +
      '&unitName=' +
      unitName
    )
  }

  // if( props.type ==  'safe'){
  //   counterStore.isShowMapDetail = false
  //   router.push('/fireRemoteManage/fireremoteManage-unitmap?unitId='+unitId + '&unitName='+unitName)
  // }else{
  //     // const {unitId,unitName} = currentUnit.value
  //     // counterStore.isShowMapDetail = false
  //     // router.push('/fireRemoteManage/fireremoteManage-unitmap?unitId='+unitId + '&unitName='+unitName)
  // }
}
</script>

<style lang="scss" scoped>
:deep(.custom-empty .custom-empty-page .gray) {
  color: #000000 !important;
}

.unitDetail {
  background: rgba(0, 16, 50, 0.9);
  color: #fff;
  border: 1px solid #edf6fc;
  min-height: 322px;
  width: 532px;

  .main {
    padding: 15px;
    position: relative;

    .details {
      position: relative;

      >div {
        color: #dae1ef;
        margin-bottom: 12px;
      }

      .deviceBtn-box {
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }

    .unitAddress {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .aqzs-box {
      position: absolute;
      right: 10px;
      top: 0;
      height: 95px;
      width: 95px;
      background-image: url(../../assets/image/yzt/excellent.png);
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .tip {
        position: absolute;
        left: 50%;
        bottom: -6px;
        transform: translate(-50%, 30%);
        width: 42px;
        height: 18px;
        line-height: 15px;
        font-size: 12px;
        text-align: center;
        border: 1px solid #12e7ad;
        border: 1px solid #12e7ad;
        background: linear-gradient(0deg, #005655 0%, #00e6a2 100%);
        border-radius: 9px;
      }

      .tip-pass {
        border: 1px solid #dcc600;
        background: linear-gradient(0deg, #823700 0%, #dcc600 100%);
      }

      .tip-good {
        border: 1px solid #63dde0;
        background: linear-gradient(0deg, #004188 0%, #00d6d8 98%);
      }

      .tip-danger {
        border: 1px solid #f56857;
        background: linear-gradient(0deg, #790001 0%, #f56857 98%);
      }
    }

    .pass {
      background-image: url(../../assets/image/yzt/pass.png);
    }

    .good {
      background-image: url(../../assets/image/yzt/good.png);
    }

    .danger {
      background-image: url(../../assets/image/yzt/danger.png);
    }
  }

  .title {
    // background-image: linear-gradient(
    //   to right,
    //   rgba(2, 98, 207, 1),
    //   rgba(2, 98, 207, 0)
    // );
    background: linear-gradient(90deg, #0262cf 20%, #001238 60%);
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;

    .close-btn {
      cursor: pointer;
    }

    .el-icon svg {
      width: initial;
      height: initial;
    }
  }

  .btn-box {
    display: flex;

    .btn {
      padding: 8px 14px;
      background-image: url(../../assets/image/yzt/detail-btn.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;

    .left {
      display: grid;
      grid-template-columns: repeat(2, 108px);
      justify-content: space-between;
      flex: 1;

      .item {
        width: 108px;
        height: 66px;
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        flex-wrap: wrap;
        background: #0b389b;
        border: 1px solid #079afd;
        box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
        margin-bottom: 8px;
        padding: 10px;

        >div:first-child {
          width: 100%;
          font-size: 14px;
        }

        .num {
          font-size: 18px;

          span {
            font-size: 12px;
          }
        }
      }

      .fire {
        background: #650000;
        border: 1px solid #ff4545;
        box-shadow: inset 0px 0px 16px 0px #ff4343;
      }

      .warning {
        background: #700760;
        border: 1px solid #f13ad5;
        box-shadow: inset 0px 0px 16px 0px #e320c5;
      }

      .hidden-danger {
        background: #7d7200;
        border: 1px solid #ffed2f;
        box-shadow: inset 0px 0px 16px 0px #ffee36;
      }

      .fault {
        background: #774700;
        border: 1px solid #ffb74d;
        box-shadow: inset 0px 0px 16px 0px #ffb649;
      }
    }

    .deviceDetails-content {
      display: grid;
      grid-template-columns: repeat(3, 160px);
      justify-content: space-between;
      flex: 1;

      .item {
        width: 160px;
        height: 66px;
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        flex-wrap: wrap;
        background: #0b389b;
        border: 1px solid #079afd;
        box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
        margin-bottom: 8px;
        padding: 10px;

        .name {
          width: 100%;
          color: #e5f1ff;
          font-weight: 400;
          font-size: 14px;
        }

        .num {
          color: #ffffff;
          font-size: 18px;
        }
      }
    }

    .img-box {
      width: 258px;
      height: 160px;
      // background-color: #eee;
      margin-left: 17px;

      .el-image {
        background-color: rgba(0, 0, 0, 0) !important;
      }

      :deep(.el-image__error) {
        background-color: rgba(0, 0, 0, 0);

        .custom-empty-page {
          .gray {
            color: #fff !important;
          }
        }
      }
    }
  }
}
</style>
