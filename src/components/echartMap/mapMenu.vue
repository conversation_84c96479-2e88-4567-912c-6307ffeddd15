<template>
  <div class="mapMenu">
    <div
      class="item"
      v-for="(item, index) in menuList"
      @click="setActiveMenu(item, index)"
      :class="{ activeMenu: activeMenu == index }"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import cardTitle from './cardTitle.vue'
// import * as echarts from 'echarts'
import { ref, onMounted, computed, watch, reactive } from 'vue'
import { useRouter, useRoute, RouteRecordRaw } from 'vue-router'
import { useUserInfo } from '@/store'
const userInfo = useUserInfo()
const router = useRouter()
const route = useRoute()
const menuList = ref([
  // {
  //   name: '安全一张图',
  //   path: '/safety-management/safeMap'
  // },
  // {
  //   name: '设备一张图',
  //   path: '/safety-management/deviceMap'
  // },
  // {
  //   name: '远程联网管理',
  //   path: '/fireRemoteManage/fireremoteManage-unitmap'
  // },
  // {
  //   name: '安全态势分析',
  //   path: '/fireSafetySituation/fireSafetySituationMap'
  // },
  // {
  //   name: '消防数据中心',
  //   path: ''
  // },
  // {
  //   name: '动火一张图',
  //   path: ''
  // }
])

const activeMenu = computed(() => {
  const pagePaths = menuList.value.map((i) => i.path)
  return pagePaths.indexOf(route.path)
})
onMounted(() => {})
// const emits = defineEmits(['showAll'])
if (userInfo.value.pictureMenuName) {
  try {
    menuList.value = JSON.parse(userInfo.value.pictureMenuName)
  } catch (e) {
    //TODO handle the exception
  }
}
const setActiveMenu = (item, index) => {
  if (item.path) {
    router.push(item.path + `?time=${new Date().getTime()}`)
  }
}

const initData = (data) => {}

defineExpose({ initData })
</script>

<style scoped lang="scss">
.mapMenu {
  display: flex;
  align-items: center;
  color: #fff;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  margin-left: -24px;
  // gap: 24px;
  // background-color: red;
  .item {
    --gutter: 24px;
    font-size: 18px;
    cursor: pointer;
    padding: 6px 15px;
    text-align: center;
    flex: 1;
    margin: 0 0px 0px var(--gutter);
    background-image: url(../../assets/image/yzt/chart-menu.png);
    background-size: 100% 100%;
    // background: #15357C;
    // border: 2px solid;
    // border-image:  linear-gradient(12deg, #4D9DFF, #0084FF) 5 5;
    // border-radius: 6px;
    // transform: skewX(-30deg);

    // transform: translateX();
  }
  .activeMenu {
    background-image: url(../../assets/image/yzt/chart-menu-active.png);
    background-size: 100% 100%;
    padding: 6px 15px;
    //    background-image: url(../../assets/image/yzt/menu-active-bg.png);
    // background-size: 100% 100%;
    // background: linear-gradient(180deg, #0083C0, #0D2A67);
    // border: 2px solid;
    // border-image: linear-gradient(12deg, #0079FF, #4FE0FF) 10 10;
    // box-shadow:inset 0px 3px 7px 0px rgba(52,177,255,0.76);
    // border-radius: 6px;
  }
}
</style>
