<template>
  <div class="topSearch">
    <div class="search-zz" v-if="isShowMore" @click="isShowMore = false"></div>
    <div class="select-box">
      <el-select
        v-model="value"
        remote
        :filterable="true"
        :remote-show-suffix="true"
        reserve-keyword
        clearable
        placeholder="请输入单位名称"
        :teleported="false"
        :remote-method="remoteMethod"
        suffix-icon=""
        :loading="loading"
        popper-class="mapUnitSelect"
        @change="handleChange"
      >
        <el-option
          v-for="item in options"
          :key="item.superviseUnitId"
          :label="item.superviseUnitName"
          :value="item.superviseUnitId"
        >
          <div class="select-item">
            <div class="unitname">
              <myTooltip :str="item.superviseUnitName"></myTooltip>
            </div>
            <div class="item-type">
              {{ item.superviseType == 1 ? '下级单位' : item.superviseType == 2 ? '企业单位' : '--' }}
            </div>
          </div>
        </el-option>
      </el-select>
      <div class="selce-icon" v-if="!value">
        <el-icon class="el-selceinput-icon">
          <Search />
        </el-icon>
      </div>
    </div>
    <div class="mb-15px selected-range flex" v-if="filterName && isShow">
      <span class="w-100px"> 已选范围：</span>
      <myTooltip :str="filterName"></myTooltip>
      <el-icon color="#11E6FF" class="cursor-pointer" @click="clearSelect">
        <CloseBold />
      </el-icon>
    </div>
    <div class="tabs-box">
      <template v-for="(item, index) in tabsList">
        <div
          :key="index"
          class="tabs-item"
          v-if="isShowItem(index)"
          @click="handleTabChange(item)"
          :class="{ 'tab-active': item.superviseUnitId == activeTabs }"
        >
          <myTooltip :str="item.superviseUnitAlias"></myTooltip>
        </div>
      </template>

      <div class="tabs-item" v-if="tabsList.length > 10 && !isShowMore" @click="isShowMore = true">更多</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import * as echarts from 'echarts'
import { ref, onMounted, computed } from 'vue'
import { Search, CloseBold } from '@element-plus/icons-vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()
// interface ListItem {
//   value: string
//   label: string
// }

const options = ref<any>([])
const value = ref<string>('')
const tabsList = ref<any>([])
const loading = ref(false)
const activeTabs = ref('')
const isShowMore = ref(false)

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {},
  },
})
const isShowItem = (index) => {
  if (isShowMore.value) return true
  if (tabsList.value.length > 10) {
    return index < 9
  } else if (tabsList.value.length <= 10) {
    return true
  }
}

onMounted(() => {
  // echartInit()
  getSubset()
})
const emits = defineEmits(['update:modelValue', 'updateMapData'])

const remoteMethod = async (query: string) => {
  if (query) {
    loading.value = true
    let res: any = null
    try {
      res = await getOption(query)
      loading.value = false
      if (res.code != 'success') {
        options.value = []
        return
      }
      options.value = res.data
    } catch (e) {
      //TODO handle the exception
    }
  } else {
    options.value = []
  }
}

const isShow = ref(false)

const handleChange = (val) => {
  isShow.value = true

  let data = options.value.filter((i) => i.superviseUnitId == val)

  activeTabs.value = ''
  if (data.length > 0) {
    emits('update:modelValue', data[0])
  } else {
    emits('update:modelValue', {})
  }
  emits('updateMapData', data[0])
}
const handleTabChange = (item) => {
  isShow.value = true
  isShowMore.value = false
  value.value = ''
  // const arr = options.value.filter(i=>i.superviseUnitId== item.superviseUnitId)

  if (activeTabs.value == item.superviseUnitId) {
    activeTabs.value = ''
    // if(arr.length>0){
    //   value.value = item.superviseUnitId
    // }else{
    //   value.value = ''
    // }
    emits('update:modelValue', {})
    emits('updateMapData')
    return
  }
  activeTabs.value = item.superviseUnitId

  emits('update:modelValue', {
    superviseUnitAlias: item.superviseUnitAlias,
    superviseUnitId: item.superviseUnitId,
    superviseType: 1,
  })
  emits('updateMapData', {
    superviseUnitAlias: item.superviseUnitAlias,
    superviseUnitId: item.superviseUnitId,
    superviseType: 1,
  })
}

const filterName = computed(() => {
  let name = ''
  const data = options.value.filter((i) => i.superviseUnitId == value.value)
  if (data && data.length > 0)
    if (data[0].superviseType == '1') name = data[0].superviseUnitName || props.modelValue.superviseUnitAlias
    else name = data[0].unitName

  if (props.modelValue.superviseUnitAlias) name = props.modelValue.superviseUnitAlias

  return name
})

const clearSelect = () => {
  isShow.value = false
  activeTabs.value = ''
  options.value = []
  value.value = ''
  emits('update:modelValue', {})
  emits('updateMapData')
}

const getOption = async (key) => {
  const params = {
    superviseId: userInfo.value.orgCode,
    superviseName: key,
  }
  return $API.post({
    url: '/security/management/subsetUnit',
    params,
  })
}

const getSubset = async () => {
  const params = {
    superviseId: userInfo.value.orgCode,
    pageSize: -1,
  }
  let res: any = null
  try {
    res = await $API.post({
      url: '/security/management/subset',
      params,
    })
    tabsList.value = [...res.data.rows]
  } catch (e) {
    //TODO handle the exception
  }
}

const initData = () => {}

defineExpose({ initData })
</script>

<style scoped lang="scss">
.search-zz {
  position: fixed;
  background-color: transparent;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
}

// :deep(.el-input__suffix) {
//   background-size: 100% 100%;
//   margin-top: 12px;
//   width: 14px;
//   height: 14px;
//   .el-input__suffix-inner{
//     width: 14px;
//     height: 14px;
//     background-image: url(../../assets/image/yzt/search-icon.png);
//     background-size: 100%;
//   }
// }
.topSearch {
  width: 432px;

  .select-box {
    width: 100%;
    margin-bottom: 15px;
    position: relative;

    .selce-icon {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);

      .el-selceinput-icon {
        color: #fff;
      }
    }

    :deep(.el-select) {
      width: 100%;

      .el-input .el-select__caret.is-reverse {
        transform: rotateZ(0deg) !important;
      }

      .el-input__inner {
        color: rgba(211, 230, 249, 1);
        height: 35px !important;
      }

      .select-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .unitname {
          flex: 1;
          overflow: hidden;
          padding-left: 5px;
        }

        .item-type {
          width: 62px;
          height: 20px;
          line-height: 18px;
          font-size: 12px;
          text-align: center;
          background: rgba(39, 140, 255, 0.16);
          border: 1px solid #5eaeff;
          border-radius: 4px;
          color: rgba(94, 174, 255, 1);
          flex-shrink: 0;
        }
      }

      // el-input__wrapper
      // .el-input__wrapper {
      .el-input__wrapper {
        // &input:-moz-placeholder, {
        //     color: #0f0;
        // }
        background: rgba(18, 89, 252, 0.5);
        color: rgba(211, 230, 249, 1);
        // border: 1px solid #079AFD;
        box-shadow: inset 0px 0px 16px 0px rgba(10, 212, 255, 0.54);
      }

      .el-input.is-focus {
        .el-input__inner {
          // background: rgba(18, 89, 252, 0.5);
          color: rgba(211, 230, 249, 1) !important;
          // box-shadow: inset 0px 0px 16px 0px rgba(10, 212, 255, 0.54) !important;
        }
      }

      input::-webkit-input-placeholder {
        color: rgba(211, 230, 249, 1);
      }

      input::-moz-input-placeholder {
        color: rgba(211, 230, 249, 1);
      }

      input::-ms-input-placeholder {
        color: rgba(211, 230, 249, 1);
      }

      .mapUnitSelect {
        border: 1px solid #067bca;

        .el-select-dropdown {
          background-color: rgba(0, 0, 0, 0);
        }

        .el-select-dropdown__empty {
          background-color: #09204f;
          color: #eee;
        }

        .el-scrollbar {
          max-width: 432px;
          overflow: hidden;

          .el-select-dropdown__item {
            text-overflow: ellipsis;
            white-space: nowrap;
            background-color: #02133a;
            color: #fff;

            &:hover {
              background-color: #153571;
            }
          }

          .el-select-dropdown__item:nth-of-type(2n) {
            background-color: #09204f;

            &:hover {
              background-color: #153571;
            }
          }

          .el-select-dropdown__list {
            margin: 0 !important;
          }
        }
      }
    }
  }

  .selected-range {
    color: #fff;
  }

  .tabs-box {
    width: 432px;
    display: grid;
    grid-template-columns: repeat(5, 80px);
    justify-content: space-between;
    max-height: 240px;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar:horizontal {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: #36425e;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(56, 107, 187, 1);
      border-radius: 10px;
      transition: all 0.2s ease-in-out;
    }

    .tabs-item {
      height: 32px;
      width: 80px;
      color: #fff;
      line-height: 32px;
      background: rgba(12, 31, 79, 0.5);
      border: 1px solid #079afd;
      text-align: center;
      box-shadow: inset 0px 0px 16px 0px rgba(10, 212, 255, 0.54);
      margin-bottom: 8px;
      overflow: hidden;
      padding: 0 4px;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }

    .tab-active {
      background-image: url(../../assets/image/yzt/search-active.png);
      background-size: 100% 100%;
    }
  }
}
</style>
