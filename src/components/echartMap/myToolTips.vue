<template>
  <el-tooltip effect="dark" :disabled="isShowToopTip" :content="str" placement="top-start">
    <div class="str-content" @mouseover="onMouseOver()">
      <div class="str-span" :style="fomartStyle">
        <span style="font-size: inherit"> {{ str }}</span>
      </div>
      <div class="elStr" ref="elStr" style="opacity: 0.6">
        {{ str }}
      </div>
    </div>
  </el-tooltip>
</template>
<script lang="ts" setup name="myTooltips">
import { ref, computed } from 'vue'
import { ElTooltip } from 'element-plus'
const isShowToopTip = ref(true)
const elStr: any = ref(null)
const props = defineProps({
  str: {
    type: String,
    default: '', // safe: 安全一张图 device: 设备一张图
  },
  size: {
    type: [Number, String],
    default: '', // safe: 安全一张图 device: 设备一张图
  },
  weight: {
    type: Number,
    default: 400, // safe: 安全一张图 device: 设备一张图
  },
  clamp: {
    type: Number,
    default: 1,
  },
})
const fomartStyle = computed(() => {
  return `-webkit-line-clamp:${props.clamp};font-size:${props.size}px;font-weight:${props.weight};`
})
const onMouseOver = () => {
  // let pw = elStr.value?.parentNode.offsetWidth
  // let sw = elStr.value?.offsetWidth
  let ph = elStr.value?.parentNode.offsetHeight
  let sh = elStr.value?.offsetHeight
  isShowToopTip.value = sh <= ph
  // myRef.value?.clientHeight
}
</script>

<style lang="scss" scoped>
/* width: ; */
.str-content {
  width: 100%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;

  .elStr {
    position: absolute;
    width: 100%;
    right: 100%;
    top: 0;
  }
}

.str-span {
  // padding-bottom: 5px;
  width: 100%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  overflow: hidden;
  line-height: 1.2;
  -webkit-box-orient: vertical;
}
</style>
