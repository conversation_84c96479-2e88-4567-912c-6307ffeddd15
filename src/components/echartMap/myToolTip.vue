<template>
  <el-tooltip effect="dark" :disabled="isShowToopTip" :content="str" :popper-class="popperClass" placement="top-start">
    <div class="str-content" @mouseover="onMouseOver()">
      <div class="str-span" :style="fomartStyle">
        <span style="font-size: inherit"> {{ str }}</span>
      </div>
      <span class="elStr" :style="fomartStyle" ref="elStr" style="visibility: hidden">
        {{ str }}
      </span>
    </div>
  </el-tooltip>
</template>

<script lang="ts" setup name="myTooltip">
import { ref, computed } from 'vue'
import { px2rem } from '@/common/utils'
import { ElTooltip } from 'element-plus'
const isShowToopTip = ref(true)
const elStr: any = ref(null)
const props = defineProps({
  str: {
    type: String,
    default: '', // safe: 安全一张图 device: 设备一张图
  },
  size: {
    type: Number,
    default: 0, // safe: 安全一张图 device: 设备一张图
  },
  weight: {
    type: Number,
    default: 400, // safe: 安全一张图 device: 设备一张图
  },
  popperClass: {
    type: String,
    default: '',
  },
})
const fomartStyle = computed(() => {
  if (props.size) return `font-size:${px2rem(props.size)};font-weight:${props.weight}`
  return ''
})
const onMouseOver = () => {
  let pw = elStr.value?.parentNode.offsetWidth
  let sw = elStr.value?.offsetWidth
  isShowToopTip.value = sw <= pw
}
</script>

<style lang="scss" scoped>
/* width: ; */
.str-content {
  width: 100%;
  display: grid;
  grid-template-columns: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;

  .elStr {
    position: absolute;
    right: 100%;
    top: 0;
  }
}

.str-span {
  // padding-bottom: 5px;
  // width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
