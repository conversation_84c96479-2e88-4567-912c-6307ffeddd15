<template>
  <div class="real-alarm-dialog" v-loading="loading">
    <p class="alarm-title">真警通知</p>
    <el-icon class="close-btn" :size="25" color="white" @click="close">
      <Close />
    </el-icon>
    <div class="w-2/3 pl-20px box-border">
      <div class="gis-view w-full h-full">
        <!-- <floor-view ref="floorViewRef"  :is-preview="true" :view-type="'messageDialog'" :user-info="messageData"></floor-view> -->
        <div id="gisMap2" class="indoorContent"></div>
      </div>
    </div>
    <div class="w-1/3 text-center">
      <ul class="alarm-info">
        <li>
          <span class="item-label">单位名称：</span>
          <!-- <span class="item-content">清华大学合肥公共安全研究院</span> -->
          <span class="item-content">{{ messageData.unitName }}</span>
        </li>
        <li>
          <span class="item-label">单位地址：</span>
          <span class="item-content">{{ messageData.unitAddress || '--' }}</span>
        </li>
        <li>
          <span class="item-label">火警接收时间：</span>
          <!-- <span class="item-content">2022-08-16 09:28:50</span> -->
          <span class="item-content">{{ messageData.monitorReceiveTime || '--' }}</span>
        </li>
        <li>
          <span class="item-label">设备类型：</span>
          <!-- <span class="item-content">点型感烟探测器</span> -->
          <span class="item-content">{{ messageData.deviceTypeName || '未知设备' }}</span>
        </li>
        <li>
          <span class="item-label">报警点位/IMEI号：</span>
          <span class="item-content">{{ messageData.deviceNum }}</span>
        </li>
        <li>
          <span class="item-label">核警人：</span>
          <span class="item-content">{{ messageData.disposeUserName }}</span>
        </li>
        <li>
          <span class="item-label">核警人联系方式：</span>
          <span class="item-content">{{ messageData.disposeUserPhone }}</span>
        </li>
        <li>
          <el-button class="redBtn" plain @click="goDetail(messageData)">详情</el-button>
          <el-button @click="close">关闭</el-button>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref, toRaw } from 'vue'
import { ElIcon } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { setTempData } from '@/common/utils'
import router from '~/router'
import { EVENT_TYPE } from '~/common/eventType'
import { setPageJsonQueryParamsAdapter } from '@/commonTypes/jumpDataStatisticsQuery'

// ---------  地图参数   -----------
let map: any = {}
let unitLayer = {
  // 正常
  '0': {
    icon: 'data:image/png;base64,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',
  },
  // 报警 火警
  '1': {
    icon: 'data:image/png;base64,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',
  },
  // 预警
  '2': {
    icon: 'data:image/png;base64,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',
  },
  // 故障
  '3': {
    icon: 'data:image/png;base64,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',
  },
  // 隐患
  '4': {
    icon: 'data:image/png;base64,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',
  },
  // 动作
  '5': {
    icon: 'data:image/png;base64,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',
  },
  // 离线
  '7': {
    icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAwCAYAAAB0WahSAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjIxQ0M1RkYyQ0Y2QTExRUM4ODlFRTlGMTJDOTZCQjRGIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjIxQ0M1RkYzQ0Y2QTExRUM4ODlFRTlGMTJDOTZCQjRGIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MjFDQzVGRjBDRjZBMTFFQzg4OUVFOUYxMkM5NkJCNEYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MjFDQzVGRjFDRjZBMTFFQzg4OUVFOUYxMkM5NkJCNEYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz49J+YeAAAETklEQVR42sxYSUsjURCuxLijxgUXXMgoihc1cUM9SEY8CsZf4HjxOs4v0PyC0auX0T8gzk1FTEQQ0YMtCIKiSUAE97jjPq9qXoeepJfXIXEsKHp5r7u//qrqq9dteX9/h89gtngvnJyctLONg7lTcTrMXeJbQxseHjYPhD98kLmHudtgup/5FPNl5sGEMMIBjDD/ztwuiNutAIuAvHqArAIg8GabzEdNgIi2b8x9/GXMA2EgxvgNHAnIR7zHT+7iQDiI0SQUCLLySyhHREFkZWVBTU0NNDY2wtHREfh8PjOhwqr6oQmE54QuiKKiImhoaIDa2trIucrKSrBarfD29maGmRDzcTywKAWNV8emWk5YLBaorq6mh1dVVdG5i4sLyMjIIGaurq5gZmYG6uvrCdDW1pYIGGTlC9OSsE0FZQwIfNuOjg7Iz8+PnAuFQjA/Pw/Nzc3Q2toKj4+PkJeXB52dnTReXl4OkiRRyHTMziXBa1URqxhraWn5BwRaYWEhtLe3g8PxFzcyi0y8vr7ScUVFBfT19YHb7Ybc3FzdEOGzlVXj0SpTOe7hcBhWV1cpDGlpafRweQxDh8dyqPf392F9fR3q6urA4/FQWHVYGVQCGdSaKT/s+voatre34ebmhvY3Njbg8PBQ9Zq9vT0KDYLGPGpqatJjxWOLkmRdKysrg/7+fqoaDAO+KeaFmnV3d8P9/X0kLHLItNqBleeHUyTFz8/Pwe/3w+npKVxeXpJuYAjUDNlYXFwk9pSsGjU9IQlPT0+H4uJiohpzpKSkBHJycjS15vn5GWw2sQZvUySMoSEALGUZCFaGFpCCggIKX2pqqhAQq5kmcXx8DEtLS3ByckJihvu7u7uqc9fW1mj89vY28Su00tJS0gUMD75pT08P7atZV1cXgdViTIsRSWTy3d0dBAIBqoaHhwc4ODigpFUzVFQcf3p6MgUkKDIZSxCrALdYBbiv9SAEiloj2gStfAEbFmHFbrdTX0HtQH1oa2sjbVEzbH7YAjCxBUxSJuus0Wyke2FhgZL27OwM5ubmYGdnJ9JrlJ18ZWWFxgWT1a8EMm00Gxufy+WibXZ2NjidTipnGYhSPZERHM/MzBQBMhG9HvFFS31vb69ew4oY3gerBLuylmHyotpG2RRLjaHo8h1iHlCeQNF6eXkx6hUkXggCk1ft6zElJYXupWLemBUaZ2VEa6WdBBtjbHhVlZUNjPOvtGTbrMyGnsQPiIpcnCbxNNDvNVxXviaJGYnfOyzU9BAMc7xgLIEgMOwutT8Fht2XJ9OA6G8GvcRUflDFtQxgYGbV6DQJwpuQ9QgDE5NgiQJhemHEmRk3cYlfBIRpIAolFFk2vJth0DQQXtoibzktus6JlxFZFcMCzEFSgXBWpgyABpMOhNtvAyDwUUD0etHyhwHh4Qlq/HwJfiQjWqv/uLp2MoDA/wACnwXIpwnNRBSYID9n2v4IMAAiUrl9lUdbuAAAAABJRU5ErkJggg==',
  },
}

const defaultPositionPixel = [13055414.38723415, 3710265.**********] // 地图中心点
const loading = ref<boolean>(false)

const props = defineProps({
  messageData: {
    type: Object,
    default: () => ({}),
  },
  default: () => {},
})

const emits = defineEmits(['close'])

function close() {
  emits('close')
}

function goDetail(data: any) {
  if (data !== null) {
    const obj = Object.assign({}, toRaw(data))
    obj.tempId = data.disposeId
    setTempData(obj)
  }
  router.push({
    path: '/fireRemoteManage/eventHanding',
    query: {
      isShowBack: 'true',

      ...setPageJsonQueryParamsAdapter({
        disposeId: data.disposeId || data.businessId,
        eventType: data.eventType || data.messageType,
      }),
    },
  })
  close()
}

onMounted(async () => {
  await nextTick()
  const item = toRaw(props.messageData)
  item.evetType = EVENT_TYPE.ALARM
  item.priorityEventType = EVENT_TYPE.ALARM
  await initMap()
  await nextTick(() => {
    getSuperviseAddrList(item)
  })
})
// ------------  gis地图组件相关事件  ---------------
// 初始化地图
function initMap() {
  loading.value = true
  // (window as any).IndoorMap.init()
  map = new (window as any).IndoorMap({
    target: 'gisMap2',
    tile: false,
    maxZoom: 22,
    minZoom: 4,
    zoom: 15,
    center: defaultPositionPixel,
    //center: [13055425.12247062, 3710371.**********],
    isVector: true,
    deviceIconAlarmGifUrl: '',
    onLoad: mapOnLoad,
  })
  const pMap = (window as any).GSMap.Plugin.new_PMap_ex({
    mapMain: map,
    styleJson: [],
  })

  map.BindSynchronousDo(pMap)
}
// map加载完毕事件
async function mapOnLoad(map: any) {
  loading.value = false

  // map.onMouseClick = mapClick
  // map.onNewMarkSelected = onNewMarkSelected
}
// 获取地图撒点信息
async function getSuperviseAddrList(val: any) {
  // loading.value = true
  let iconIcon = unitLayer[val.eventType].icon
  let styles = window.IndoorMap.createStyleByImage(iconIcon, [0.5, 1])
  map.addNewMark(
    { name: val.unitName, x: val.unitPointX, y: val.unitPointY }, //绑定数据
    'x', //X轴对应的字段名称,
    'y', //Y轴对应的字段名称
    undefined, //Z轴对应的字段名称（可去省）
    styles //样式
  )
  map.zoomTo({
    center: [val.unitPointX, val.unitPointY],
    zoom: 16,
  })
  // loading.value = false
}
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.real-alarm-dialog {
  background: #ee202d;
  width: 1120px;
  height: 440px;
  border-radius: 10px;
  padding: 50px 30px;
  // padding: 30px;
  box-sizing: border-box;
  position: relative;
  display: flex;

  .close-btn {
    position: absolute;
    right: 15px;
    top: 15px;
    cursor: pointer;

    svg {
      width: inherit;
      height: inherit;
    }
  }

  .gis-view {
    background: white;
    overflow: hidden;
    border-radius: 10px;
  }

  .indoorContent {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .alarm-title {
    position: absolute;
    top: 20px;
    color: #ffeb97;
    display: inline-block;
    // font-size: 28px;
    // margin-bottom: 30px;
  }

  .alarm-info {
    margin: 30px 0 0 20px;

    .redBtn {
      color: #ee202d;
      background-color: #fff;
      border-color: #ee202d;
    }

    li:last-child {
      margin: 30px 0 0 50px;
    }

    li {
      display: flex;
      align-items: flex-start;
      margin-bottom: 10px;
      color: white;

      .item-label {
        text-align: right;
        display: inline-block;
        width: 45%;
        margin-right: 15px;
        opacity: 0.7;
      }

      .item-content {
        text-align: left;
        display: inline-block;
        width: 60%;
      }
    }
  }
}
</style>
