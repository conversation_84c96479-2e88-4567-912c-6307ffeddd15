<template>
  <div class="w-full">
    <header class="h-40px w-full notification-header-message flex justify-between items-center">
      <span>{{ title }}（共{{ list.length }}条）</span>
      <el-icon @click="close" class="cursor-pointer"><icon-close /></el-icon>
    </header>
    <div class="notification-list-message">
      <el-scrollbar max-height="160px">
        <div class="notification-list_item items-center flex box-border" v-for="(item, index) in list" :key="index">
          <img :src="item.icon" class="w-40px h-40px mr-10px" alt="" />
          <div class="flex items-center justify-between w-[60%]">
            <div class="mr-10px w-full">
              <p class="text-16px font-hp-md overflow-hidden w-full">
                <my-tooltip size="16" :weight="700" :str="item.unitName || item.messageTitle || '--'"></my-tooltip>
              </p>
              <p
                class="text-16px notification-mb-10px font-hp-md"
                v-if="item.messageType != '106' && item.messageType != '111'"
              >
                {{ item.deviceTypeName || '未知设备' }}
              </p>
              <p class="text-16px notification-mb-10px font-hp-md" v-else>
                <my-tooltips :clamp="2" :str="item.messageContent || '--'"></my-tooltips>
              </p>

              <p class="text-14px notification-mb-10px" v-if="isWaring()">
                处置等级：
                <span style="color: #9f1d8b">
                  {{ item.warningRankName || '--' }}
                </span>
              </p>
              <p class="text-14px notification-mb-10px" v-if="isWaring()">预警描述：{{ item.eventDesc || '--' }}</p>
              <p class="text-14px">
                {{ item.monitorReceiveTime || '--' }}
              </p>
            </div>
          </div>
          <p
            class="text-12px automaticAlarm"
            v-if="item.reachWay == '2'"
            :class="item.isSuccess && item.isSuccess == 'true' ? 'success' : 'fail'"
          >
            {{ item.isSuccess && item.isSuccess == 'true' ? '自动告警成功' : '自动告警失败' }}
          </p>
          <div class="notification-list_item_end info-btn">
            <span class="text-14px cursor-pointer btn" track @click="openDetail(item)">{{ item.subTitle }}</span>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed } from 'vue'
import myTooltips from '@/components/echartMap/myToolTips.vue'
import { Message } from '@/types'
import { EVENT_TYPE, EVENT_MESSAGE_TITLE, EVENT_ICON } from '~/common/eventType'
import dayjs from 'dayjs'
import { openMessageDetails } from '@/common/utils'
import router from '~/router'
import { useUserInfo } from '~/store'
import _ from 'lodash'

type NOtification = Message & { subTitle: string }

const list = ref([] as unknown as NOtification[])

const rankObj: any = ref({
  '1': '一级',
  '2': '二级',
  '3': '三级',
  '4': '四级',
})

const props = defineProps({
  messageType: {
    type: [String, Number],
    default: '',
  },
  onClose: {
    type: Function,
    default: () => {},
  },
  onClear: {
    type: Function,
    default: () => {},
  },
  notificationRecord: {
    type: Object,
    default: () => ({}),
  },
})

const userInfo = useUserInfo()

const title = computed(() => {
  const t = props.messageType
  return EVENT_MESSAGE_TITLE[t]
})

function close() {
  props.onClose()
}

function add(item: NOtification) {
  item.icon = EVENT_ICON[item.messageType]
  item.createTime = dayjs(item.createTime).format('YYYY/MM/DD hh:mm:hh')
  item.subTitle = getSubTitle(item)
  item.warningRankName = item.warningRank ? rankObj.value[item.warningRank] : '--'
  list.value.push(item)
}

const handleClear = _.debounce(() => {
  props.onClose()
}, 10 * 1000)

function openDetail(item: NOtification) {
  openMessageDetails(item, router)
  list.value.splice(list.value.indexOf(item), 1)
  handleClear()
  if (list.value.length === 0) {
    props.onClose()
  }
}

function isWaring(): boolean {
  const t = props.messageType
  return t == EVENT_TYPE.WARING
}

function getSubTitle(item: Message) {
  const t = props.messageType
  const model = userInfo.value.serviceModelCode
  return '详情'
}

onMounted(() => {
  // eslint-disable-next-line vue/no-mutating-props
  props.notificationRecord[props.messageType].add = add
  handleClear()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.info-btn {
  position: absolute;
  right: 10px;
  top: 45px;
}

.automaticAlarm {
  position: absolute;
  top: 15px;
  right: 10px;
  padding: 5px 8px;
  border-radius: 4px;
  width: 9em;
  text-align: center;
}

.gs-notification {
  width: 400px !important;
  padding: 0 !important;
  border: unset !important;
  z-index: 999 !important;

  .el-notification__group {
    margin: 0;
    width: 100%;
  }

  .notification-header-message {
    background: rgba(0, 128, 255, 1);
    color: white;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .notification-list-message {
    position: relative;

    .notification-list_item {
      position: relative;
      padding: 15px 20px 20px;
      box-sizing: border-box;
      font-size: 12px;
      border-bottom: 1px solid var(--el-border-color);

      .notification-mb-10px {
        margin-bottom: 10px;
      }

      .notification-list_item_unitName {
        position: relative;
      }

      .success {
        color: #3ebb00;
        background-color: rgba(62, 187, 0, 0.15);
      }

      .fail {
        color: #ff2626;
        background-color: rgba(255, 38, 38, 0.15);
      }

      .btn {
        display: inline-block;
        padding: 8px 20px;
        font-size: 14px;
        background: #ffffff;
        border: 1px solid #0080ff;
        border-radius: 4px;
        color: #1890ff;
        white-space: nowrap;
        justify-self: flex-end;
      }
    }
  }
}
</style>
