<template>
  <div class="w-full">
    <header class="h-40px w-full notification-header-message flex justify-between items-center">
      <span> 通知通告 </span>
      <el-icon @click="close" class="cursor-pointer"><icon-close /></el-icon>
    </header>
    <div class="notification-list-message">
      <el-scrollbar max-height="160px">
        <div class="notification-list_item items-center flex box-border" v-for="(item, index) in list" :key="index">
          <div class="flex items-center justify-between w-full">
            <div class="mr-10px flex flex-col w-full">
              <div class="notification-list_item_unitName notification-mb-10px">
                <p class="text-16px font-hp-md">
                  {{ item.noticeName || '--' }}
                </p>
              </div>
              <p class="text-12px font-hp-md" style="color: #a8abb2">
                {{ $frm.formatDate(item.publishTime, 'YYYY-MM-DD HH:mm:ss') || '--' }}
              </p>
            </div>
            <div class="notification-list_item_end">
              <span class="text-14px cursor-pointer btn" track @click="openDetail(item)">{{ item.subTitle }}</span>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { Message } from '@/types'
import { EVENT_ICON } from '~/common/eventType'
import dayjs from 'dayjs'
import router from '~/router'
import _ from 'lodash'
import $frm from '@/common/format'

type NOtification = Message & {
  subTitle: string
  noticeName: string
  publishTime: string
  noticeId: string
}

const list = ref([] as unknown as NOtification[])

const props = defineProps({
  messageType: {
    type: [String, Number],
    default: '',
  },
  onClose: {
    type: Function,
    default: () => {},
  },
  onClear: {
    type: Function,
    default: () => {},
  },
  notificationRecord: {
    type: Object,
    default: () => ({}),
  },
})

function close() {
  props.onClose()
}

function add(item: NOtification) {
  const messageContent = item.messageContent ? JSON.parse(item.messageContent) : {}
  item.noticeName = messageContent.noticeName
  item.publishTime = messageContent.publishTime
  item.noticeId = messageContent.noticeId
  item.icon = EVENT_ICON[item.messageType]
  item.createTime = dayjs(item.createTime).format('YYYY/MM/DD hh:mm:hh')
  item.subTitle = getSubTitle(item)
  list.value.push(item)
}

const handleClear = _.debounce(() => {
  props.onClose()
}, 10 * 1000)

function openDetail(item: NOtification) {
  router.push(`/staging/notice/noticeDetails?source=notice&noticeId=${item.noticeId}`)
  list.value.splice(list.value.indexOf(item), 1)
  handleClear()
  if (list.value.length === 0) {
    props.onClose()
  }
}

function getSubTitle(item: NOtification) {
  let str = '详情'
  if (item.subTitle) str = item.subTitle
  return str
}

onMounted(() => {
  // eslint-disable-next-line vue/no-mutating-props
  props.notificationRecord[props.messageType].add = add
  handleClear()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.notification-notice {
  width: 400px !important;
  padding: 0 !important;
  border: unset !important;
  z-index: 999 !important;

  .el-notification__group {
    margin: 0;
    width: 100%;
  }

  .notification-header-message {
    background: rgba(0, 128, 255, 1);
    color: white;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .notification-list-message {
    .notification-list_item {
      padding: 25px 20px;
      box-sizing: border-box;
      font-size: 12px;
      border-bottom: 1px solid var(--el-border-color);

      .notification-mb-10px {
        margin-bottom: 15px;
      }

      .notification-list_item_unitName {
        position: relative;

        .automaticAlarm {
          position: absolute;
          top: -4px;
          right: -7em;
          padding: 3px 6px;
          border-radius: 4px;
        }
      }

      .success {
        color: #3ebb00;
        background-color: rgba(62, 187, 0, 0.15);
      }

      .fail {
        color: #ff2626;
        background-color: rgba(255, 38, 38, 0.15);
      }

      .btn {
        display: inline-block;
        padding: 8px 20px;
        font-size: 14px;
        background: #ffffff;
        border: 1px solid #0080ff;
        border-radius: 4px;
        color: #1890ff;
        white-space: nowrap;
        justify-self: flex-end;
      }
    }
  }
}
</style>
