<template>
  <div class="nkt w-full h-full relative">
    <div ref="mapRef" class="w-full h-full relative"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, withDefaults, computed } from 'vue'

// 导入整个库
import { indoorGis } from '@/utils/index.es.js'
// 使用多个组件
// import Popup from './popup.vue'

const { getGisMap } = indoorGis

interface Props {
  deviceId?: string
  floorImage?: string
  mapType?: number
}
const props = withDefaults(defineProps<Props>(), {
  deviceId: '',
  floorImage: '',
  mapType: 1
})
const emits = defineEmits(['onDeviceSelected'])

const mapRef = ref<HTMLElement | null>(null)

const getDeviceById = () => {
  return {
    deviceTypePid: '02000000',
    deviceTypeId: '02030000',
    eventTypeNames: '',
    laMake: '1',
    twoCode: '',
    latitude: '',
    deviceId: '20230313093229454177',
    deviceName: '点型感烟火灾探测器',
    mapZ: 0,
    measurement: '{}',
    mapY: 3597770.6229317035,
    buildingShockRank: '',
    mapX: 13555616.734606337,
    deviceNum: '862',
    floorId: '310100DW1630462420088520704_001_U001',
    produceInfo:
      '{"brand": "上海松江", "model": "JB9108G", "brand_id": "13bf940c175f53c1246c8223554d912e", "model_id": "de3305b55a1fd96cd86422a7215864ef"}',
    deviceAddress: '就餐厅中北',
    signalCode: '',
    deviceTypePname: '火灾自动报警系统',
    deviceOnlyId: '2441be749cf5d7c7222d25916ad78aea',
    unitId: '310100DW1630462420088520704',
    laPoint: '40',
    floorName: '1层',
    subordinateUnits: 0,
    longitude: '',
    subCenterCode: '340100YYZX201805230001',
    deviceTypeName: '点型感烟火灾探测器',
    eventType: '',
    manufacturerCode: 'bwnanjrz',
    buildingId: '310100DW1630462420088520704_001',
    buildingName: '综合办公楼',
    monitorReceiveTime: '2023-07-24 08:28:59',
    iotReceiveTime: '2023-07-24 08:28:59',
    deviceClassification: 1,
    deviceState: 0,
    priorityEventType: '',
    useInfo: '',
    buildingDeviceStatus: '',
    onlineState: 0,
    laLoop: '1',
    showDevicePoint: '1-1-40',
    floorImage: ''
  }
}

onMounted(async () => {
  const gisMap = getGisMap('floorPopup')
  gisMap.mount(mapRef.value as HTMLElement)
  const device = getDeviceById()
  const floorInfo = {
    mapType: props.mapType,
    floordId: device.floorId,
    floorImage: props.floorImage || device?.floorImage || ''
  }
  await gisMap.loadFloorData(floorInfo)
  gisMap.loadDeviceData([device])
})
defineOptions({
  name: 'floorMap'
})
</script>
<style lang="scss" scoped></style>
