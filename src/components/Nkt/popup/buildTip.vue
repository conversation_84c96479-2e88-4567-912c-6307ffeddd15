<template>
  <div class="buildPopup bg-[#f0f]">
    <div class="title">{{ data.buildingName }}</div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
const props = defineProps<{
  data: any
}>()
console.log('🚀 ~ props:', props)
const emits = defineEmits(['close'])
const buildInfo = computed(() => {
  return props.data
})
const close = () => {
  emits('close')
}
defineOptions({
  name: 'buildPopup'
})
</script>
<style lang="scss" scoped></style>
