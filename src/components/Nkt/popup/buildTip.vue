<template>
  <div class="buildPopup bg-[#fff]">
    <div class="flex title justify-between">
      <div class="">{{ data.buildingName }}</div>
      <div class="icon" :class="{ 'icon-flip': !showList }" @click="setShow">
        <el-icon><DArrowRight /></el-icon>
      </div>
    </div>
    <div v-show="showList">
      <div class="border-b border-light-[#000]">
        <div class="flex justify-between px-[10px] py-[5px]">
          <div>设备</div>
          <div>11</div>
        </div>
        <div class="flex justify-between px-[10px] py-[5px]">
          <div>视频</div>
          <div>11</div>
        </div>
      </div>
      <div class="">
        <div class="px-[10px] py-[5px]">消防设备</div>
        <div
          class="flex justify-between px-[10px] py-[5px]"
          v-for="item in firefighting"
          :key="item.key"
        >
          <div>{{ item.lable }}</div>
          <div>{{ data[item.key] || 0 }}</div>
        </div>
      </div>
      <div class="">
        <div class="px-[10px] py-[5px]">储能设备</div>
        <div class="flex justify-between px-[10px] py-[5px]">
          <div>设备</div>
          <div>11</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, ref } from 'vue'
import { DArrowRight } from '@element-plus/icons-vue'
import { ElIcon } from 'element-plus'

const props = defineProps<{
  data: any
}>()
const emits = defineEmits(['close', 'upDataSize'])
const buildInfo = computed(() => {
  return props.data
})
const showList = ref(true)

const firefighting = ref([
  { key: 'alarmNum', value: 0, lable: '火警' },
  { key: 'warningNum', value: 0, lable: '预警' },
  { key: 'faultNum', value: 0, lable: '故障' },
  { key: 'troubleNum', value: 0, lable: '隐患' },
  { key: 'movingNum', value: 0, lable: '动作' },
  { key: 'offlineNum', value: 0, lable: '离线' }
])
const setShow = (e) => {
  let el = e.target.closest('.Build-Tip').parentNode
  const allel = Array.from(el.parentNode.childNodes)
  allel.forEach((item: any) => {
    item.style.zIndex = 1
  })
  el.style.zIndex = 2
  showList.value = !showList.value
  nextTick(() => {
    emits('upDataSize')
  })
}
const close = () => {
  emits('close')
}
defineOptions({
  name: 'buildPopup'
})
</script>
<style lang="scss" scoped>
.buildPopup {
  border-radius: 6px;
  color: #000;
  min-width: 160px;
}
.title {
  width: 100%;
  height: 35px;
  font-size: 14px;
  box-sizing: border-box;
  padding: 0 8px;
  line-height: 35px;
  color: #161616;
  background: linear-gradient(180deg, #dee7ff 0%, #feffff 80%, #fff 100%);
  border-radius: 6px;
  // border: 1px solid #161616;
  box-shadow: 0px 1px #161616;
  .icon {
    transform: rotate(90deg);
  }
  .icon-flip {
    transform: rotate(-90deg);
  }
}
</style>
