// http://112.27.198.15:9862?fields=buildingName%2CbuildingId&unitId=310100DW1630462420088520704

import $API from '~/common/api'

// 
// /safetyMonitor/queryDeviceNumsByUnitId

export const queryDeviceNumsByUnitId = (params:{unitId:string,deviceClassification?:string})=>{
  return $API.post({
    url:'/safetyMonitor/queryDeviceNumsByUnitId',
    params:{
      // 设备分类 3 物联网设备 4 用户信息传输装置 6 视频设备，默认（3，4，6）
      // deviceClassification:'buildingName,buildingId'
      ...params
    }
  })
}


// 根据单位ID查询楼栋列表
export const getBuildingListByUnitId  =(unitId)=>{
  return  $API.post({
    baseURL:'/api/v3/bw-svc-enterprise-gis-service',
    url:'/deviceEvent/getBuildingListByUnitId',
    params:{
      unitId:unitId,
      fields:'buildingName,buildingId'
    }
  })
}


// 获取单位下所楼栋所有楼层信息及设备数、视频以及事件统计

export const queryFloorInfoAndDeviceNums= (params:{unitId:string,buildId?:string})=>{
  return  $API.post({
    url:'/safetyMonitor/queryFloorInfoAndDeviceNums',
    params:{
      ...params
    }
  })
}