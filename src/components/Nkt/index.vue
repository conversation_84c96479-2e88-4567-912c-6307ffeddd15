<template>
  <div
    class="absolute left-[50%] top-[50%] z-10 w-[70vw] h-[85vh] flex flex-col"
    style="transform: translate(-50%, -50%)"
  >
    <div
      class="bg-[#132547] w-full flex justify-between items-center pl-[15px]"
    >
      <div>唐能光伏运维中心光储电站</div>
      <div class="p-15px" @click="close">x</div>
    </div>
    <div
      class="flex items-center bg-[#fff] p-[15px] text-[#000]"
      v-if="isShowFloorView"
    >
      <div class="pr-[15px] cursor-pointer" @click="backToUnit">
        <el-icon><Back /></el-icon>
      </div>
      <div class="flex items-center mr-[15px]">
        <div class="flex-shrink-0 mr-[10px]">楼栋</div>
        <el-select
          v-model="buildInfo.buildId"
          @change="buildChange"
          filterable
          placeholder="楼栋"
        >
          <el-option
            v-for="(item, index) in buildList"
            :key="item.buildingId"
            :label="item.buildingName"
            :value="item.buildingId"
          />
        </el-select>
      </div>
      <div class="flex items-center mr-[15px]">
        <div class="flex-shrink-0 mr-[10px]">设备类型</div>
        <el-select
          v-model="buildInfo.buildId"
          @change="buildChange"
          filterable
          placeholder="楼栋"
        >
          <el-option
            v-for="(item, index) in buildList"
            :key="item.buildingId"
            :label="item.buildingName"
            :value="item.buildingId"
          />
        </el-select>
      </div>
      <div class="flex items-center mr-[15px]">
        <div class="flex-shrink-0 mr-[10px]">监测状态</div>
        <el-select
          v-model="buildInfo.buildId"
          @change="buildChange"
          filterable
          placeholder="楼栋"
        >
          <el-option
            v-for="(item, index) in buildList"
            :key="item.buildingId"
            :label="item.buildingName"
            :value="item.buildingId"
          />
        </el-select>
      </div>
      <div></div>
      <div></div>
    </div>
    <div class="w-full h-full flex-1 relative overflow-hidden">
      <div ref="mapRef" class="w-full h-full relative"></div>
      <div class="absolute left-[30px] top-[30px]" v-if="isShowFloorView">
        <deviceCard></deviceCard>
      </div>
      <div
        v-if="isShowFloorView"
        class="absolute right-[30px] top-[50%] h-[80%] overflow-hidden"
        style="transform: translateY(-50%)"
      >
        <floorTab :floorList="floorList"></floorTab>
        <!-- @floorChange="floorChange" -->
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, ref, watch } from 'vue'
import { onMounted } from 'vue'
import buildTip from './popup/buildTip.vue'
import { Back } from '@element-plus/icons-vue'
import $API from '~/common/api'
import {
  getBuildingListByUnitId,
  queryDeviceNumsByUnitId,
  queryFloorInfoAndDeviceNums
} from './serve'
import deviceCard from './component/card.vue'
import floorTab from './component/floorTab.vue'

// 导入整个库
import { indoorGis } from '@/utils/index.es.js'
// 使用多个组件
// import Popup from './popup.vue'

const { getGisMap } = indoorGis
const gisMap = getGisMap()
const mapRef = ref()

interface Props {
  pointer?: { x: number; y: number; [key: string]: any }[]
}
const props = defineProps<Props>()
const emits = defineEmits(['close'])
const close = () => {
  emits('close')
}
const backToUnit = async () => {
  isShowFloorView.value = false

  gisMap.clearPopup()
  await gisMap.loadUnit(unitInfo.value)
  gisMap.loadBuild(buildData.value, { html: buildTip })
}

const isShowFloorView = ref(false)

// NKT star

const buildData = ref([
  {
    buildingUseNature: '',
    fireRisk: 6,
    notes: '',
    latitude: 460000.0,
    downArea: 0.0,
    buildingArea: 5624.0,
    putUseDate: '2015-12-01',
    remark: '',
    areaCovered: 0.0,
    exitNum: 0,
    upFloor: 0,
    allElevatorCount: 0,
    aerialViewNo: '0',
    buildingHeight: 24.0,
    unitId: 'AHHF_QHHFY_20180408',
    fireRating: 2,
    longitude: 710000.0,
    ifExistPump: 0,
    updateUserId: '',
    updateUserName: '',
    buildingClass: '',
    buildingImg: '',
    updateTime: '2025-05-22 10:30:21',
    buildingFlag: 0,
    standardArea: 0.0,
    buildingId: 'AHHF_QHHFY_20180408_002',
    downFloor: 0,
    elevatorCount: 0,
    buildingName: '标准检验大楼（2号楼）',
    buildingStructure: '5',
    createTime: '2025-05-22 10:30:21',
    stairsNum: 0,
    upArea: 0.0,
    completionDate: '',
    constructionDate: '',
    status: '0',
    '25020000': 1,
    '25010000': 11,
    '25030000': 12,
    deviceCount: 609,
    alarmNum: 2,
    faultNum: 3,
    troubleNum: 5,
    movingNum: 0,
    warningNum: 0,
    offlineNum: 27,
    bluetoothCount: 12,
    videoCount: 12
  }
])

const unitInfo = ref({
  unitId: 'AHHF_QHHFY_20180408', //单位ID
  aerialViewImg:
    '/api/v3/v3-img1-online/img1/floorImage/image/floorImage/AHHF_QHHFY_20180408/ASC.jpg', //鸟瞰图地址
  subCenterCode: '340100YYZX201805230001', //运营中心ID
  viewType: 0
})

const initNkt = async () => {
  gisMap.mount(mapRef.value as HTMLElement)
  await gisMap.loadUnit(unitInfo.value)
  const res = await queryDeviceNumsByUnitId({ unitId: unitInfo.value.unitId })
  console.log('🚀 ~ initNkt ~ res:', res)
  res.data
  buildData.value.push(...res.data, ...res.data)
  gisMap.loadBuild(buildData.value, {
    html: buildTip,
    onUpDataSize: () => {
      getGisMap().popupRefresh()
    }
  })
  gisMap.onOVBuildSelected = async (data, e, obj) => {
    floorInfo.value.floordId = '310100DW1630462420088520704_001_U001'
    showFloor()
  }
}

// NKT end

// floor star
const buildInfo = ref({
  buildId: '',
  buildingName: '',
  unitId: ''
})

const floorInfo = ref({
  mapType: 1,
  floordId: '',
  floorImage: ''
})
const buildList = ref([])
const buildChange = () => {}
const showFloor = async () => {
  isShowFloorView.value = true
  const res = await queryFloorInfoAndDeviceNums({
    unitId: unitInfo.value.unitId,
    buildId: '310100DW1630462420088520704_001'
  })
  console.log('🚀 ~ showFloor ~ res:', res)
  gisMap.loadFloorData(floorInfo.value)
  // loadDeviceData
  // gisMap
}
const floorList = ref<any>([])
for (let index = 0; index < 30; index++) {
  floorList.value.push({
    floorName: index + '层',
    floorId: index.toString()
  })
}

// floor end

onMounted(async () => {
  initNkt()
  const res = await getBuildingListByUnitId(unitInfo.value.unitId)
  buildList.value = res.data
})
</script>
<style>
.floorPopup {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 1200px;
  height: 800px;
  z-index: 99999999999;
  transform: translate(-50%, -50%);
}
</style>
