<template>
  <div class="w-full h-full relative">
    <div ref="mapRef" class="w-full h-full relative bg-[#f0f]"></div>
    <div class="floorPopup">
      <floorMapPopup device-id="20230313093229454177"></floorMapPopup>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, ref, watch } from 'vue'
import { onMounted } from 'vue'
import buildTip from './popup/buildTip.vue'
import $API from '~/common/api'
import floorMapPopup from '@/components/floorMapPopup/index.vue'

// 导入整个库
import { indoorGis } from '@/utils/index.es.js'
// 使用多个组件
// import Popup from './popup.vue'

const { getGisMap } = indoorGis

interface Props {
  pointer?: { x: number; y: number; [key: string]: any }[]
}
const props = defineProps<Props>()
const _pointer = computed(() => {
  return props.pointer || []
})
const unitInfo = ref({
  unitId: 'AHHF_QHHFY_20180408', //单位ID
  aerialViewImg: 'image/floorImage/AHHF_QHHFY_20180408/ASC.jpg', //鸟瞰图地址
  subCenterCode: '340100YYZX201805230001', //运营中心ID
  viewType: -2
})

const iconList = ref([
  {
    name: '0',
    src: new URL('@/assets/pointerIcon/0.png', import.meta.url).href
  },
  {
    name: '1',
    src: new URL('@/assets/pointerIcon/1.png', import.meta.url).href
  },
  {
    name: '2',
    src: new URL('@/assets/pointerIcon/2.png', import.meta.url).href
  },
  {
    name: '3',
    src: new URL('@/assets/pointerIcon/3.png', import.meta.url).href
  },
  {
    name: '4',
    src: new URL('@/assets/pointerIcon/4.png', import.meta.url).href
  },
  {
    name: '5',
    src: new URL('@/assets/pointerIcon/5.png', import.meta.url).href
  },
  {
    name: '7',
    src: new URL('@/assets/pointerIcon/7.png', import.meta.url).href
  }
])

const buildData = ref([
  {
    buildingUseNature: '',
    fireRisk: 6,
    notes: '',
    latitude: 460000.0,
    downArea: 0.0,
    buildingArea: 5624.0,
    putUseDate: '2015-12-01',
    remark: '',
    areaCovered: 0.0,
    exitNum: 0,
    upFloor: 0,
    allElevatorCount: 0,
    aerialViewNo: '0',
    buildingHeight: 24.0,
    unitId: 'AHHF_QHHFY_20180408',
    fireRating: 2,
    longitude: 710000.0,
    ifExistPump: 0,
    updateUserId: '',
    updateUserName: '',
    buildingClass: '',
    buildingImg: '',
    updateTime: '2025-05-22 10:30:21',
    buildingFlag: 0,
    standardArea: 0.0,
    buildingId: 'AHHF_QHHFY_20180408_002',
    downFloor: 0,
    elevatorCount: 0,
    buildingName: '标准检验大楼（2号楼）',
    buildingStructure: '5',
    createTime: '2025-05-22 10:30:21',
    stairsNum: 0,
    upArea: 0.0,
    completionDate: '',
    constructionDate: '',
    status: '0',
    '25020000': 1,
    '25010000': 11,
    '25030000': 12,
    deviceCount: 609,
    alarmNum: 2,
    faultNum: 3,
    troubleNum: 5,
    movingNum: 0,
    warningNum: 0,
    offlineNum: 27,
    bluetoothCount: 12,
    videoCount: 12
  }
])

const buildInfo = ref({
  buildId: '',
  buildingName: '',
  unitId: ''
})

const mapRef = ref()
onMounted(async () => {
  // const gisMap = getGisMap()
  // gisMap.addIcon(iconList.value)
  // gisMap.mount(mapRef.value as HTMLElement)
  // await gisMap.loadUnit(unitInfo.value)
  // gisMap.onGeoDataLoad(buildData.value, { html: buildTip })
  // gisMap.onOVBuildSelected = (data, e, obj) => {
  //   if (_buildPopupHtml.value && _showBuildPopup.value) {
  //     gisMap.addPointerPopup(data, _buildPopupHtml.value)
  //   }
  //   emits('onOVBuildSelected', data, e, obj)
  // }
})
</script>
<style>
.floorPopup {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 1200px;
  height: 800px;
  z-index: 99999999999;
  transform: translate(-50%, -50%);
}
</style>
