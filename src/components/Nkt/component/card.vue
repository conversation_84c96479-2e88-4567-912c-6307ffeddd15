<template>
  <div class="bg-[#fff] rounded-[8px] text-[#000] px-[10px] py-[10px]">
    <div class="">
      <div class="px-[10px] py-[5px] font-bold">设备总数</div>
      <div class="flex">
        <div class="flex justify-between px-[10px]">
          <div>储能设备</div>
          <div>11</div>
        </div>
        <div class="flex justify-between px-[10px]">
          <div>储能设备</div>
          <div>11</div>
        </div>
      </div>
      <div class="px-[10px] py-[5px] font-bold mt-[10px]">消防设备</div>
      <div class="flex">
        <div
          class="flex flex-col items-center justify-center px-[10px]"
          v-for="item in firefighting"
          :key="item.key"
        >
          <div>{{ data[item.key] || 0 }}</div>
          <div>{{ item.lable }}</div>
        </div>
      </div>
      <div class="px-[10px] py-[5px] mt-[10px] font-bold">储能设备</div>
      <div class="flex">
        <div class="flex flex-col items-center justify-center px-[10px]">
          <div>11</div>
          <div>报警</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, withDefaults } from 'vue'

const props = withDefaults(
  defineProps<{
    data: {
      [key: string]: any
    }
  }>(),
  {
    data: () => {
      return {}
    }
  }
)
const firefighting = ref([
  { key: 'alarmNum', value: 0, lable: '火警' },
  { key: 'warningNum', value: 0, lable: '预警' },
  { key: 'faultNum', value: 0, lable: '故障' },
  { key: 'troubleNum', value: 0, lable: '隐患' },
  { key: 'movingNum', value: 0, lable: '动作' },
  { key: 'offlineNum', value: 0, lable: '离线' }
])
</script>
