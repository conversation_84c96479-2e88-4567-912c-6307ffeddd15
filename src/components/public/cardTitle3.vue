<template>
  <div class="card-title">
    <p class="title">{{ title }}</p>
    <div class="sub-title">
      <slot name="subTitle"></slot>
    </div>
    <div class="mb-16px mr-30px">
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
})
</script>
<style lang="scss" scoped>
.card-title {
  margin-top: 10px;
  height: 42px;
  width: 100%;
  background-image: url('@/assets/image/card-title-bg.png');
  background-size: auto 100%;
  background-position: 10px 0;
  background-repeat: no-repeat;
  display: flex;
  align-items: flex-end;
}
.title {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 24px;
  color: #ffffff;
  line-height: 42px;
  padding-left: 50px;
}
.sub-title {
  display: flex;
  margin-left: 17px;
  font-size: 14px;
  color: #9fbcd0;
  line-height: 42px;
  padding-bottom: -15px;
}
</style>
