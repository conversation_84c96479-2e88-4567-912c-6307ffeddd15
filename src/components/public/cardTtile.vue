<template>
  <div class="card-title">
    <p class="title">{{ title }}</p>
    <div>
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
})
</script>
<style lang="scss" scoped>
.card-title {
  width: auto;
  height: 50px;
  background-image: url('@/assets/image/safeMap/title-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 24px;
  color: #ffffff;
  line-height: 50px;
  text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
  padding-left: 50px;
}
</style>
