<template>
  <el-cascader
    ref="casRef"
    :props="casProps"
    v-model="modelValue"
    @change="handleChange"
    placeholder="全部"
    clearable
  />
</template>

<script lang="ts" setup>
import { isLeaf } from 'element-plus/lib/utils'
import { emit } from 'process'
import { ref, onMounted, handleError } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '~/common/api'
const route = useRoute()
const router = useRouter()

const props = defineProps({
  modelValue: {}
})

const emits = defineEmits(['update:modelValue', 'change', 'getCheckedNodes'])

const casRef = ref()

const casProps = {
  lazy: true,
  // checkStrictly: true,
  lazyLoad
}

onMounted(() => {
  getOptions()
})

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', val)
  emits('getCheckedNodes', casRef.value.getCheckedNodes())
}

async function lazyLoad(node: any, resolve: any) {
  let parentCode = node.data?.distCode
  const data = await getOptions(parentCode)
  resolve(data)
}

function getOptions(parentCode: any = '000000') {
  return new Promise<void>((resolve, reject) => {
    $API
      .post({
        url: '/socialUnit/getDistrictListByCondition',
        params: {
          distParentCode: parentCode
        }
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          if (res.data.length > 0) {
            res.data = res.data.map((item: any) => {
              return {
                ...item,
                label: item.distName,
                value: item.distCode,
                leaf: item.isLeaf === 1
              }
            })
          }
          resolve(res.data)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
</script>
<style lang="scss" scoped></style>
