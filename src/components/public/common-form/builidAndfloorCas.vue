<template>
  <el-cascader
    ref="floorsRef"
    fit-input-width
    v-model="innerValue"
    :options="options"
    placeholder="请选择"
    :props="{
      children: 'children',
      checkStrictly: props.checkStrictly,
      expandTrigger: 'hover',
    }"
    clearable
    @change="handleChange"
  >
    <template #default="{ node }">
      <div class="opacity-0">{{ node.label }}</div>
      <div class="custom-node" track @click="elCascaderOnClick">
        {{ node.label }}
      </div>
    </template>
  </el-cascader>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { getBuildingNewTreeByUnitId } from '@/common/comResponse/index'
import { useRoute } from 'vue-router'
import type { PropType } from 'vue'

const route = useRoute()

type Model = string | number | Array<string | number> | null
const props = defineProps({
  modelValue: {
    type: [String, Number, Array] as PropType<Model>,
    default: null,
  },
  checkStrictly: {
    type: Boolean,
    default: true,
  },
})

const emits = defineEmits<{
  (e: 'update:modelValue', v: Model): void
  (e: 'change', v: Model): void
}>()

const options = ref<any>([])
const floorsRef: any = ref(null)
const innerValue = computed<Model>({
  get: () => props.modelValue as Model,
  set: (val) => emits('update:modelValue', val),
})

function handleChange(val: any) {
  emits('change', val)
  setTimeout(() => {
    floorsRef.value && floorsRef.value.togglePopperVisible(false)
  }, 160)
}

async function getOptions() {
  const params = {
    unitId: route.query.unitId || '',
  }
  const tempOptions = await getBuildingNewTreeByUnitId(params)
  options.value = tempOptions.options
}
const elCascaderOnClick = (e: MouseEvent) => {
  const target = e?.target as HTMLElement | null
  const parent = target?.parentElement
  const prev = parent?.previousElementSibling as HTMLElement | null
  prev?.click?.()
}

onMounted(() => {
  getOptions()
})
</script>
<style lang="scss" scoped>
.custom-node {
  width: 100%;
  position: absolute;
  top: 0;
  right: 0;
  padding-left: 40px;
  z-index: 1;
}
</style>
