<template>
  <el-tree-select
    v-model="valueStrictly"
    ref="treeRef"
    default-expand-all
    :render-after-expand="false"
    :teleported="true"
    :data="options"
    multiple
    show-checkbox
    value-key="id"
    node-key="id"
    :check-strictly="checkStrictly"
    check-on-click-node
    :collapse-tags="collapseTags"
    :collapse-tags-tooltip="collapseTagsTooltip"
    @check="checkNode"
    @remove-tag="removeTag"
  >
  </el-tree-select>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { getSuperviseUnitTreeByConditions } from '@/common/comResponse/index'
import { useRoute, useRouter } from 'vue-router'
import { useUserInfo } from '@/store'

const route = useRoute()
const router = useRouter()

const ui = useUserInfo()

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  screen: {
    type: String,
    default: 'whole' // whole: 勾选全部,part: 部分
  },
  disableFlg: {
    type: Boolean,
    default: false
  },
  collapseTags: {
    type: Boolean,
    default: false
  },
  collapseTagsTooltip: {
    type: Boolean,
    default: false
  },
  treeProps: {
    type: Object,
    default: () => ({
      label: 'unitName',
      value: 'unitId',
      children: 'child'
    })
  }
})

const checkStrictly = ref(true)

const valueStrictly: any = ref([])

const treeRef: any = ref(null)

const emits = defineEmits(['getCheckedNodes', 'removeCheckedNodes'])

const options = ref<any>([])

const removeTag = (val) => {
  emits('removeCheckedNodes', val)
}

async function checkNode(
  data,
  { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }
) {
  // console.log('data', data)
  // console.log('checkedNodes', checkedNodes)
  // console.log('checkedKeys', checkedKeys)
  // console.log('halfCheckedNodes', halfCheckedNodes)
  // console.log('halfCheckedKeys', halfCheckedKeys)
  // const currtNode = treeRef.value.getNode(data.unitId)
  // console.log('currtNode', currtNode)
  // const checkedTreeData = treeRef.value.getCheckedNodes(false, false)
  // console.error(checkedTreeData, 'checkedTreeData')

  let isChecked = checkedNodes.findIndex((i) => i.id === data.id) === -1
  if (!isChecked) {
    if (data.leafType == 'unit') {
      // 若用户选择企业单位任一值则所属分管单位选中
      // 获取当前点击元素的父级id列表
      let list = options.value
      if (props.screen === 'part') list = options.value[0].children
      const parentNodes = getParentsById(list, data.id)
      checkedNodes.push(...parentNodes)
    }
  }

  const uniqueArray = unique(checkedNodes)
  const checkedIds = uniqueArray.map((k) => k.id)
  const checkedAllNodes = uniqueArray.map((k) => ({
    unitId: k.value,
    unitName: k.label,
    unitType: k.leafType == 'unit' ? '0' : '1',
    unitOnlyId: k.id
  }))
  nextTick(() => {
    valueStrictly.value = checkedIds
    emits('getCheckedNodes', checkedAllNodes)
  })
}

function unique(arr) {
  if (!Array.isArray(arr)) {
    throw console.log('error: ' + JSON.stringify(arr) + ' is not Array')
  }
  let obj = {}
  return arr.filter((item) => {
    return obj.hasOwnProperty(typeof item + JSON.stringify(item))
      ? false
      : (obj[typeof item + JSON.stringify(item)] = true)
  })
}

function getParentsById(list, id) {
  for (let i in list) {
    if (list[i].id === id) {
      //查询到就返回该数组对象
      return [list[i]]
    }
    if (list[i].children) {
      let node = getParentsById(list[i].children, id)

      if (node !== undefined) {
        //查询到把父节点连起来
        return node.concat(list[i])
      }
    }
  }
}

function clearData() {
  options.value = []
}

async function getOptions() {
  const orgParams = {
    superviseUnitId: route.path.includes('dataIntelligence-knowledge')
      ? ui.value.topSuperviseId
      : ui.value.orgCode,
    serverStatus: '0'
  }
  let res = await getSuperviseUnitTreeByConditions(orgParams)

  options.value = modifyTreeField([res], {
    unitName: 'label',
    unitId: 'value',
    child: 'children'
  })

  echo()
  console.log(options.value)
}

function echo() {
  nextTick(() => {
    valueStrictly.value = props.modelValue.map((i: any) => i.unitOnlyId)
    emits('getCheckedNodes', props.modelValue)
  })
}

function modifyTreeField(tree, fieldNameMap) {
  if (!Array.isArray(tree)) {
    return tree
  }
  return tree.map((node, index) => {
    const newNode = { ...node }
    // 修改字段名称
    Object.keys(fieldNameMap).forEach((oldFieldName) => {
      const newFieldName = fieldNameMap[oldFieldName]
      newNode[newFieldName] = newNode[oldFieldName]
      // 单独加个防止重复的id
      if (oldFieldName == 'unitId')
        newNode['id'] = newNode[oldFieldName] + '_' + index
      delete newNode[oldFieldName]
    })
    // 递归修改子节点的字段名称
    if (newNode.children) {
      newNode.children = modifyTreeField(newNode.children, fieldNameMap)
    }

    return newNode
  })
}

onMounted(() => {
  getOptions()
})

defineExpose({
  getOptions,
  clearData
})
</script>
<style lang="scss" scoped></style>
