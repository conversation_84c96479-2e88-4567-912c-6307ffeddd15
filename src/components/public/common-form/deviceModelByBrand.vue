<!-- 根据设备品牌筛选到的规格型号列表组件 -->
<template>
  <el-select
    v-model="modelValue"
    @change="handleChange"
    placeholder="全部"
    :disabled="!props.brandId"
    clearable
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.name"
      :value="item.code"
    />
  </el-select>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getDeviceModelOptions } from '@/common/comResponse/index'

const route = useRoute()
const router = useRouter()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  brandId: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

watch(
  () => props.brandId,
  (val: any) => {
    getOptions(val)
  },
  { deep: true }
)

const selectName = computed(() => {
  const filterRes = options.value.filter(
    (controlRoom: any) => controlRoom.code === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].name
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {
  getOptions(props.brandId)
}

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', {
    code: val,
    name: selectName.value
  })
}

// 获取企业单位列表
async function getOptions(brandId: string) {
  options.value = []
  try {
    emits('update:modelValue', '')
    if (brandId) {
      options.value = await getDeviceModelOptions({
        brandId
      })
    }
  } catch (error) {}
}
</script>
<style lang="scss" scoped></style>
