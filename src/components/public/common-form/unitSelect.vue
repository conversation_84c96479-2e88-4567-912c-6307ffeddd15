<template>
  <el-select v-model="innerValue" placeholder="请选择" clearable filterable fit-input-width @change="handleChange">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.unitName"
      :value="item.unitId"
      :title="item.unitName"
    />
  </el-select>
</template>

<script lang="ts">
export default {
  name: 'unitSelect',
}
</script>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { getUnitName } from '@/common/comResponse/index'
import { useUserInfo } from '@/store'

const ui = useUserInfo()
const innerValue = computed({
  get: () => props.modelValue,
  set: (val: any) => emits('update:modelValue', val),
})
const props: any = defineProps({
  modelValue: {},
  // 0：企业单位 3：九小场所/家庭
  unitType: {},
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

const selectName = computed(() => {
  const filterRes = options.value.filter((item: any) => item.unitId === props.modelValue)
  if (filterRes.length > 0) {
    return filterRes[0].unitName
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {
  getOptions()
}

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', { unitId: val, unitName: selectName.value })
}

// 获取企业单位列表
async function getOptions() {
  const unitParams = {
    superviseId: ui.value.unitId,
    unitType: props.unitType,
  }

  options.value = await getUnitName(unitParams)
}
</script>
<style lang="scss" scoped></style>
