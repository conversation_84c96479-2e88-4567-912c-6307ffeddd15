<template>
  <el-select
    v-model="modelValue"
    @change="handleChange"
    placeholder="全部"
    clearable
    filterable
    fit-input-width
    :disabled="disabled"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.unitName"
      :value="item.unitId"
      :title="item.unitName"
    />
  </el-select>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getUnitName } from '@/common/comResponse/index'
import { useUserInfo } from '@/store'

const route = useRoute()
const router = useRouter()
const ui = useUserInfo()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  unitType: {
    type: [String, Number], // 只接收string，当传入number时控制台报警告
    default: ''
  },
  disabled: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
  isLoad: {
    type: Boolean,
    default: () => {
      return true;
    },
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

watch(
  () => props.unitType,
  (val: any) => {
    getOptions(val)
  },
  { deep: true }
)

const selectName = computed(() => {
  const filterRes = options.value.filter(
    (item: any) => item.unitId === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].unitName
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {
  getOptions(props.unitType)
}

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', {
    unitId: val,
    unitName: selectName.value
  })
}

async function getOptions(unitType: any) {
  options.value = []
  emits('update:modelValue', '')
  emits('change', {
    unitId: "",
    unitName: ""
  })
  try {
    // emits('update:modelValue', '')
    if (unitType!==''||props.isLoad) {
      options.value = await getUnitName({
        superviseId: ui.value.orgCode,
        unitType:unitType
      })
      // emits('update:modelValue', '')
    }
  } catch (error) {}
}
</script>
<style lang="scss" scoped></style>
