<template>
  <el-cascader
    v-model="modelValue"
    placeholder="全部"
    :show-all-levels="false"
    :props="{
      checkStrictly: true,
      value: 'superviseId',
      label: 'superviseName',
      children: 'child'
    }"
    clearable
    :disabled="(type === 'edit' ? true : false) || props.disableFlg"
    :options="options"
    @change="handleChange"
  ></el-cascader>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getOrgName } from '@/common/comResponse/index'
import { useRoute, useRouter } from 'vue-router'
import { useUserInfo } from '@/store'

const route = useRoute()
const router = useRouter()

const ui = useUserInfo()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  disableFlg: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

onMounted(() => {
  getOptions()
})
defineExpose({
  getOptions,
  clearData
})
function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', val)
}
function clearData() {
  options.value = []
}
async function getOptions() {
  const orgParams = {
    superviseUnitId: route.path.includes('dataIntelligence-knowledge')
      ? ui.value.topSuperviseId
      : ui.value.orgCode,
    serverStatus: '0'
  }
  let res = await getOrgName(orgParams)
  options.value.push(res)
  // if (options.value.length != 0 && props.modelValue != '') emits('update:modelValue', options.value[0].superviseId)
}
</script>
<style lang="scss" scoped></style>
