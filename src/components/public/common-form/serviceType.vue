<template>
  <el-select v-model="modelValue" placeholder="全部" filterable clearable @change="handleChange">
    <el-option v-for="item in options" :key="item.code" :label="item.value" :value="item.code" />
  </el-select>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getServiceModelType } from '@/common/comResponse/index'
const route = useRoute()
const router = useRouter()

const props = defineProps({
  modelValue: {}
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

const selectName = computed(() => {
  const filterRes = options.value.filter(
    (item: any) => item.code === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].name || filterRes[0].value
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {
  getOptions()
}

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', { code: val, name: selectName.value })
}

async function getOptions() {
  const params = {}
  options.value = await getServiceModelType(params)
}
</script>
<style lang="scss" scoped></style>
