<template>
  <!-- 单位类别筛选框 -->
  <el-select v-model="modelValue" fit-input-width @change="handleChange" placeholder="全部" clearable filterable
    :popper-class="type == 'yzt' ? 'complex' : ''">
    <el-option v-for="item in options" :key="item[optionsProps.value]" :label="item[optionsProps.label]"
      :value="item[optionsProps.value]"></el-option>
  </el-select>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import $API from '@/common/api'
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  options: {
    type: Object || Array,
    default: () => []
  },
  optionsProps: {
    type: Object,
    default: {
      value: 'code',
      label: 'value'
    }
  },
  type: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', val)
}

const options = ref(props.options)

async function getOptions() {
  return new Promise<void>((resolve, reject) => {
    $API
      .post({
        url: '/fireSafety/getUnitCategoryList'
      })
      .then((res: any) => {
        console.log(res, '单位类别列表')
        if (res && res.code == 'success') {
          options.value = res.data
        } else {
          options.value = []
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
console.log(props.options)
if (props.options.length == 0) getOptions()
</script>
<style lang="scss" scoped></style>
