<template>
  <el-select
    v-model="modelValue"
    placeholder="全部"
    clearable
    @change="handleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.roleName"
      :value="item.id"
    />
  </el-select>
</template>

<script lang="ts">
export default {
  name: 'unitSelect'
}
</script>
<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getRoleList } from '@/common/comResponse/index'
const route = useRoute()
const router = useRouter()

const props = defineProps({
  modelValue: {}
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

const selectName = computed(() => {
  const filterRes = options.value.filter(
    (item: any) => item.id === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].roleName
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {
  getOptions()
}

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', { id: val, roleName: selectName.value })
}

// 获取企业单位列表
async function getOptions() {
  options.value = await getRoleList()
}
</script>
<style lang="scss" scoped></style>
