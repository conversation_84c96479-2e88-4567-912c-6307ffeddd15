<!-- 根据企业id筛选到的消控室列表组件 -->
<template>
  <el-select
    v-model="modelValue"
    @change="controlRoomChange"
    placeholder="全部"
    :disabled="!props.unitId"
    clearable
  >
    <el-option
      v-for="item in controlRoomOptions"
      :key="item.value"
      :label="item.keyPartName"
      :value="item.keyPartId"
    />
  </el-select>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getFireControlRoomListByUnitId } from '@/common/comResponse/index'

const route = useRoute()
const router = useRouter()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  unitId: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

const controlRoomOptions = ref<any>([])

watch(
  () => props.unitId,
  (val: any) => {
    getControlRoomNameData(val)
  },
  { deep: true }
)

const selectControlRoomName = computed(() => {
  const filterRes = controlRoomOptions.value.filter(
    (controlRoom: any) => controlRoom.keyPartId === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].keyPartName
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {
  getControlRoomNameData(props.unitId)
}

function controlRoomChange(val: any) {
  emits('update:modelValue', val)
  emits('change', {
    controlRoomId: val,
    controlRoomName: selectControlRoomName.value
  })
}

async function getControlRoomNameData(unitId: string) {
  controlRoomOptions.value = []
  try {
    emits('update:modelValue', '')
    if (unitId) {
      controlRoomOptions.value = await getFireControlRoomListByUnitId(unitId)
    }
  } catch (error) {}
}
</script>
<style lang="scss" scoped></style>
