<template>
  <el-date-picker
    v-model="times"
    type="date"
    :clearable="false"
    placeholder="评估时间"
    value-format="YYYY-MM-DD"
    @change="handleChange"
    :disabled-date="disabledDate"
    :popper-class="type == 'yzt' ? 'complex' : ''"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import $frm from '@/common/format'
import dayjs from 'dayjs'
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  limitTime: {
    type: Number,
    default: 60
  },
  early: {
    type: Number,
    default: 0
  },
  hasTimes: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    default: ''
  },
  todayForDate: {
    type: String,
    default: ''
  }
})
const times = ref('')
times.value = $frm.getDay(0, 1)

const emits = defineEmits(['update:modelValue', 'change'])
// 时间限制
const disabledDate = (time) => {

  let date = $frm.yesterdayStamp()
  if (props.todayForDate) date = dayjs(props.todayForDate).valueOf()
  if (time.getTime() > date) {
    return time.getTime() >= date
  }
}

const handleChange = (val: any) => {
  emits('update:modelValue', val)
  emits('change', val)
}
handleChange(times.value)
</script>
<style lang="scss" scoped></style>
