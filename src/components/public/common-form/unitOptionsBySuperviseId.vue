<template>
  <el-cascader
    ref="DeviceTypeCasRef"
    fit-input-width
    v-model="modelValue"
    :options="options"
    placeholder="全部"
    :show-all-levels="false"
    :placement="placement"
    :props="{
      expandTrigger: 'hover',
      checkStrictly: props.checkStrictly,
      multiple: multiple,
      label: 'superviseName',
      value: 'superviseId',
      children: 'child',
      emitPath: false
    }"
    :collapse-tags="collapseTags"
    :collapse-tags-tooltip="collapseTagsTooltip"
    clearable
    :popper-class="type == 'yzt' ? 'complex' : ''"
    @change="handleChange"
  >
    <template #default="{ node, data }">
      <div class="opacity-0">{{ node.label }}</div>
      <div class="custom-node" track @click="elCascaderOnClick">
        {{ node.label }}
      </div>
    </template>
  </el-cascader>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  getUnitNameBySuperviseId,
  getOrgName
} from '@/common/comResponse/index'
import { useUserInfo } from '@/store'

const route = useRoute()
const router = useRouter()
const ui = useUserInfo()

// const props = defineProps({
//   modelValue: {
//     type: String,
//     default: ''
//   },
//   superviseId: {
//     type: String || Number,
//     default: ''
//   },
//   returnType: {
//     type: String,
//     default: 'id' // id 返回所选的id,name返回字段名称
//   },
//   type: {
//     type: String,
//     default: ''
//   }
// })

const props = defineProps({
  modelValue: {},
  checkStrictly: {
    type: Boolean,
    default: true
  },
  superviseId: {
    type: String || Number,
    default: ''
  },
  multiple: {
    type: Boolean,
    default: false
  },
  collapseTags: {
    type: Boolean,
    default: false
  },
  collapseTagsTooltip: {
    type: Boolean,
    default: false
  },
  filter: {
    type: String,
    default: 'whole' // whole 查询全部 part 查询部分,默认为查询全部数据
  },
  placement: {
    type: String,
    default: 'bottom-start'
  },
  filterType: {
    type: String,
    default: '4'
  },
  deviceClassification: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  whole: {
    type: Boolean,
    default: false
  }
})

const chargeId = computed({
  // 重新定义
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value)
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

const elCascaderOnClick = (e) => {
  e.target.parentNode.previousElementSibling.click()
}

// watch(
//   () => props.superviseId,
//   (val: any) => {
//     getOptions(val)
//   },
//   { deep: true }
// )

const selectName = computed(() => {
  const filterRes = options.value.filter(
    (item: any) => item.superviseUnitId === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].unitName
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {
  getOptions()
  // getOptions(props.unitType)
}

const DeviceTypeCasRef: any = ref(null)
function handleChange(val: any) {
  console.log(val)
  if (!val) val = ''
  emits('update:modelValue', val)
  setTimeout(() => {
    emits('change', {
      unitId: val,
      unitName: selectName.value
    })
    DeviceTypeCasRef.value && DeviceTypeCasRef.value.togglePopperVisible(false)
  }, 160)
}

async function getOptions() {
  options.value = []
  try {
    emits('update:modelValue', '')
    let res = await getOrgName({
      superviseUnitId: props.superviseId || ui.value.orgCode
    })
    // let res2 = await getUnitNameBySuperviseId({
    //   superviseId: props.superviseId || ui.value.orgCode,
    //   pageSize: -1
    //   // unitType
    // })
    if (props.whole) {
      options.value.push(res)
    } else {
      options.value = [...res.child]
    }
    //   getUnitNameBySuperviseId({
    //   superviseId: props.superviseId || ui.value.orgCode,
    //   pageSize: -1
    //   // unitType
    // })
    // if (unitType != 'undefined') {

    // }
  } catch (error) {}
}
</script>
<style lang="scss" scoped>
.custom-node {
  width: 100%;
  position: absolute;
  top: 0;
  right: 0;
  padding-left: 40px;
  z-index: 1;
}
</style>
