<template>
  <el-cascader
    class="w-full"
    v-model="modelValue"
    :options="options"
    filterable
    placeholder="请选择分管单位"
    :show-all-levels="false"
    :props="{
      checkStrictly: props.checkStrictly,
      children: 'child',
      label: 'unitName',
      value: 'unitId'
    }"
    clearable
    @change="handleChange"
  ></el-cascader>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getSuperviseUnitTreeByConditions } from '@/common/comResponse/index'
import { useRoute, useRouter } from 'vue-router'
import { useUserInfo } from '@/store'

const route = useRoute()
const router = useRouter()

const ui = useUserInfo()

const props = defineProps({
  modelValue: {
    type: String,
    default: () => ''
  },
  checkStrictly: {
    type: Boolean,
    default: true
  },
  filter: {
    type: String,
    default: 'part' // whole 查询全部 part 查询部分,默认为查询分管单位数据
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

onMounted(() => {
  getOptions()
})

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', val)
}

function filterTreeData(treeData) {
  // 遍历树形结构，对每个节点进行判断
  return treeData.filter((node) => {
    if (node.child) {
      // 递归处理子节点
      node.child = filterTreeData(node.child)
    }
    // 返回所有leafType为"supervise"的节点
    return node.leafType === 'supervise'
  })
}

async function getOptions() {
  const params = {
    superviseUnitId: ui.value.orgCode,
    unitIdFlag: props.filter == 'part' ? 1 : 0
  }
  let res = await getSuperviseUnitTreeByConditions(params)
  options.value.push(res)
  // 过滤leafType为"supervise"的节点
  // if (props.filter == 'part') filterTreeData(options.value)
}
</script>
<style lang="scss" scoped></style>
