<template>
  <el-select
    fit-input-width
    v-model="model_value"
    remote
    reserve-keyword
    suffix-icon=""
    remote-show-suffix
    :remote-method="remoteMethod"
    @change="handleChange"
    @clear="clearHandle"
    :placeholder="!route.query.id ? '请输入企业单位名称' : '请输入服务单位名称'"
    clearable
    filterable
  >
    <el-option
      v-for="item in options"
      :key="returnType === 'id' ? item.value : item.unitName"
      :label="item.unitName"
      :title="item.unitName"
      :value="returnType === 'id' ? item.unitId : item.unitName"
    />
  </el-select>
</template>

<script lang="ts" setup>
import { onMounted, watch, computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { getUnitName } from '@/common/comResponse/index'
import { useUserInfo } from '@/store'
// import { options } from '@/components/sjfxCommon/server/server'

const route = useRoute()
const ui = useUserInfo()
const options = ref<any>([])

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  unitType: {
    type: String || Number,
    default: '',
  },
  returnType: {
    type: String,
    default: 'id', // id 返回所选的id,name返回字段名称
  },
  chargeId: {
    type: String || Number,
    default: '', // 分管单位id
  },
})

const emits = defineEmits(['update:modelValue', 'change'])
watch(
  () => props.chargeId,
  (newVal: any, oldVal) => {
    if (newVal) {
      getOptions(0)
      if (props.modelValue && oldVal && newVal !== oldVal) {
        emits('update:modelValue', '')
      }
    } else {
      options.value = []
      emits('update:modelValue', '')
    }
  },
  { deep: true }
)
watch(
  () => props.modelValue,
  (newVal: any) => {
    if (!props.chargeId && newVal && options.value.length == 0) {
      getOptions(0)
    }
  },
  { immediate: true }
)

const model_value = computed(() => {
  return props.modelValue
})

onMounted(() => {
  init()
})

function init() {
  options.value = []
}

const selectName = computed(() => {
  const filterRes = options.value.filter((item: any) => item.unitId === props.modelValue)
  if (filterRes.length > 0) {
    return filterRes[0].unitName
  } else {
    return ''
  }
})

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', {
    unitId: val,
    unitName: selectName.value,
  })
}

const clearHandle = () => {
  options.value = []
  if (props.chargeId) getOptions(0)
}

async function getOptions(unitType: any, query = '') {
  if (unitType != 'undefined') {
    options.value = await getUnitName({
      superviseId: props.chargeId ? props.chargeId : ui.value.orgCode,
      unitName: query,
      unitType: unitType,
    })
    if (options.value.length <= 0) {
      emits('update:modelValue', '')
    } else if (!options.value.find((i) => i.unitId == props.modelValue)) {
      emits('update:modelValue', '')
    }
  }
}

const remoteMethod = (query: string) => {
  if (query) getOptions(props.unitType, query)
}
</script>
<style lang="scss" scoped></style>
