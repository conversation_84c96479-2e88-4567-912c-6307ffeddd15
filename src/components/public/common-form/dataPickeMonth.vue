<template>
  <!-- <el-date-picker v-model="times"  @calendar-change = 'calendarChange' :disabled-date="disabledDate" type="monthrange" :clearable="false"
		value-format="YYYY-MM" range-separator="~" :default-value = "['2022-05-08','2022-06-07']" start-placeholder="开始时间" @panel-change ='panelChange' end-placeholder="结束时间"
		@change="handleChange" @visible-change="visibleChange"
    /> -->
  <el-date-picker v-model="times" type="monthrange" @calendar-change="calendarChange" :clearable="clearable"
    value-format="YYYY-MM" range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间" @change="handleChange" />
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'

import { ElMessageBox, ElMessage } from 'element-plus'
import dayjs from 'dayjs'

import $frm from '@/common/format'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  limitMounth: {
    type: Number,
    default: 6
  },
  clearable: {
    type: Boolean,
    default: false
  }
})
const times: any = ref('')
// times.value = $frm.getDays(30, props.early)
const old = dayjs().subtract(5, 'month').format('YYYY-MM')
const today = dayjs().format('YYYY-MM')
times.value = [old, today]
const emits = defineEmits(['update:modelValue', 'change'])

// const disabledDate = (time) => {
//   // 只能选择当前时间的前一个月的和当天之前的
//   let threeDate = new Date()
//   threeDate.setMonth(threeDate.getMonth() - 12)
//   return (
//     time.getTime() < threeDate.getTime() ||
//     time.getTime() > new Date().getTime()
//   )
// }

const calendarChange = (time) => {
  // 只能选择当前时间的前一个月的和当天之前的
  if (!time || !time.length) return
  starTime = time[0]
}
const panelChange = (time) => {
  // 只能选择当前时间的前一个月的和当天之前的
}
const visibleChange = (time) => {
  // 只能选择当前时间的前一个月的和当天之前的
}

const checkMouth = (val) => {
  if (props.clearable) return true
  if (!val || val.length < 2) return false
  if (val && val.length > 0) {
    let star = new Date(val[0])
    let end = new Date(val[1])
    if (dayjs(end).diff(star, 'month') > props.limitMounth - 1) {
      let str = `日期范围不超过${props.limitMounth}个月`
      ElMessage.error(str)
      return false
    } else {
      return true
    }
  }
}
const handleChange = (val: any) => {
  let nerVal = []
  // if(val&&val.length==2){
  //   let starTime = val[0]
  //   let endTime = val[1]

  //   if(props.hasTimes){
  //     starTime += ' 00:00:00'
  //     endTime += ' 23:59:59'
  //   }
  //   nerVal.push(starTime )
  //   nerVal.push(endTime)
  // }
  // emits('update:modelValue', nerVal)
  // emits('change', nerVal)
  if (checkMouth(val)) {
    emits('update:modelValue', val)
    emits('change', val)
  }
}
if (!props.clearable) handleChange(times.value)

</script>
<style lang="scss" scoped></style>
