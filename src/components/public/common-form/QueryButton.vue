<template>
  <div class="query-btn" @click="onClick">
    <slot>{{ text }}</slot>
  </div>
</template>
<script setup lang="ts">
import { defineEmits, defineProps } from 'vue'

const { text } = defineProps<{ text?: string }>()
const emit = defineEmits<{ (e: 'click'): void }>()

const onClick = () => emit('click')
</script>
<style lang="scss" scoped>
.query-btn {
  height: 32px;
  line-height: 22px;
  background: url('@/assets/image/energyStorageSafety/btn-bg.png') no-repeat center;
  background-size: 100% 100%;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  padding: 6px 22px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}
</style>
