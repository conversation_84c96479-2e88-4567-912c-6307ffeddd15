<!-- 隐患问题分类 -->
<template>
  <el-select v-model="_value" @change="unitChange" clearable :disabled="disabled">
    <el-option v-for="item in options" :key="item.problemNo" :label="item.problemDesc" :value="item.problemNo" />
  </el-select>
</template>
<script lang="ts">
export default {
  name: 'unitTypeSelect',
}
</script>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import $API from '@/common/api'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: () => {
      return false
    },
  },
  options: {
    type: Object || Array,
    default: () => [],
  },
})

const _value = computed(() => props.modelValue)

const emits = defineEmits(['update:modelValue', 'change'])
const options = ref(props.options)

async function getOptions() {
  return new Promise<void>((resolve, reject) => {
    $API
      .post({
        url: '/hazardRecord/queryHazardProblem',
      })
      .then((res: any) => {
        if (res && res.code == 'success') {
          options.value = res.data
        } else {
          options.value = []
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

onMounted(() => {
  if (props.options.length == 0) getOptions()
})

function unitChange(val: any) {
  emits('update:modelValue', val)
  emits('change', val)
}
</script>
<style lang="scss" scoped></style>
