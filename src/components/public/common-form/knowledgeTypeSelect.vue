<template>
  <el-select
    v-model="modelValue"
    placeholder="请选择"
    clearable
    filterable
    fit-input-width
    @change="handleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      :title="item.value"
    />
  </el-select>
</template>

<script lang="ts">
export default {
  name: 'unitSelect'
}
</script>
<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getKnowledgeTypeList } from '@/common/comResponse/index'
import { useUserInfo } from '@/store'

const route = useRoute()
const router = useRouter()
const ui = useUserInfo()

const props: any = defineProps({
  modelValue: {},
  // 0：企业单位 3：九小场所/家庭
  unitType: {}
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

const selectName = computed(() => {
  const filterRes = options.value.filter(
    (item: any) => item.unitId === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].unitName
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {
  getOptions()
}

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', { unitId: val, unitName: selectName.value })
}

// 获取企业单位列表
async function getOptions() {
  const unitParams = {
    // superviseId: ui.value.orgCode
  }

  options.value = await getKnowledgeTypeList(unitParams)
}
</script>
<style lang="scss" scoped></style>
