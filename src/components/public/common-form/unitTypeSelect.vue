<!-- 单位类型下拉框组件 -->
<template>
  <el-select v-model="modelValue" @change="unitChange" :disabled="disabled">
    <el-option
      v-for="item in unitTypeOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>
<script lang="ts">
export default {
  name: 'unitTypeSelect'
}
</script>
<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { unitTypeOptions } from '@/common/fixedOptions'

const props = defineProps({
  modelValue: {},
  disabled: {
    type: Boolean,
    default: () => {
      return false
    }
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

onMounted(() => {})

function unitChange(val: any) {
  emits('update:modelValue', val)
  emits('change', val)
}
</script>
<style lang="scss" scoped></style>
