<template>
  <el-date-picker v-model="times" @calendar-change='calendarChange' :disabled-date="disabledDate" type="daterange"
    :clearable="false" value-format="YYYY-MM-DD" range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间"
    @change="handleChange" />
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import $frm from '@/common/format'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  limitTime: {
    type: Number,
    default: 60
  },
  early: {
    type: Number,
    default: 0
  },
  hasTimes: {
    type: Boolean,
    default: true
  }



})
const times = ref([])
times.value = $frm.getDays(30, props.early)

const emits = defineEmits(['update:modelValue', 'change'])
// 时间限制
let starTime = ''
const disabledDate = (time) => {

  if (!starTime) return false
  const oneDay = 3600 * 1000 * 24 // 一天
  let current = new Date(starTime).getTime()
  let end = current + oneDay * props.limitTime
  let star = current - oneDay * props.limitTime
  return (
    time.getTime() > end ||
    time.getTime() < star
  )
}
const calendarChange = (time) => {
  // 只能选择当前时间的前一个月的和当天之前的
  if (!time || !time.length) return
  starTime = time[0]

}
const handleChange = (val: any) => {
  let nerVal = []
  if (val && val.length == 2) {
    let starTime = val[0]
    let endTime = val[1]

    if (props.hasTimes) {
      starTime += ' 00:00:00'
      endTime += ' 23:59:59'
    }
    nerVal.push(starTime)
    nerVal.push(endTime)
  }
  emits('update:modelValue', nerVal)
  emits('change', nerVal)
  starTime = ''
}
handleChange(times.value)

</script>
<style lang="scss" scoped></style>