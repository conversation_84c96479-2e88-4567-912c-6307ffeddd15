<template>
  <el-select
    fit-input-width
    v-model="modelValue"
    remote
    suffix-icon=""
    reserve-keyword
    remote-show-suffix
    :remote-method="remoteMethod"
    @change="handleChange"
    placeholder="请输入模型名称"
    clearable
    filterable
  >
    <el-option
      v-for="item in options"
      :key="returnType === 'id' ? item.id : item.name"
      :label="item.name"
      :title="item.name"
      :value="returnType === 'id' ? item.id : item.name"
    />
  </el-select>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getModelName } from '@/common/comResponse/index'
import { useUserInfo } from '@/store'

const route = useRoute()
const router = useRouter()
const ui = useUserInfo()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  returnType: {
    type: String,
    default: 'id' // id 返回所选的id,name返回字段名称
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

const selectName = computed(() => {
  const filterRes = options.value.filter(
    (item: any) => item.id === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].name
  } else {
    return ''
  }
})

onMounted(() => {
  init()
})

function init() {}

function handleChange(val: any) {
  console.log(val)
  if (!val) options.value = []
  emits('update:modelValue', val)
  emits('change', {
    id: val,
    name: selectName.value
  })
}

async function getOptions(query: string = '') {
  options.value = []
  try {
    emits('update:modelValue', '')
    options.value = await getModelName({
      name: query
    })
  } catch (error) {}
}

const optionLoading = ref(false)
const remoteMethod = (query: string) => {
  if (query) {
    optionLoading.value = true
    getOptions(query)
  } else {
    options.value = []
  }
}
</script>
<style lang="scss" scoped></style>
