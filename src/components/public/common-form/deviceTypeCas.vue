<template>
  <el-cascader
    ref="DeviceTypeCasRef"
    fit-input-width
    v-model="innerValue"
    :options="options"
    placeholder="全部"
    :show-all-levels="false"
    :props="cascaderProps"
    class="deviceTypeCascader"
    :placement="placement"
    :collapse-tags="collapseTags"
    :collapse-tags-tooltip="collapseTagsTooltip"
    :disabled="loading"
    clearable
    @change="handleChange"
  >
    <template #default="{ node }">
      <div class="opacity-0">{{ node.label }}</div>
      <div class="custom-node" track @click="elCascaderOnClick">
        {{ node.label }}
      </div>
    </template>
  </el-cascader>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { getDeviceType } from '@/common/comResponse/index'
import { useRoute } from 'vue-router'
import { useUserInfo } from '@/store'
import type { PropType } from 'vue'
const DeviceTypeCasRef: any = ref(null)
const route = useRoute()

const ui = useUserInfo()

type Model = string | number | Array<string | number> | null
const props = defineProps({
  modelValue: {
    type: [String, Number, Array] as PropType<Model>,
    default: null,
  },
  checkStrictly: {
    type: Boolean,
    default: true,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  collapseTags: {
    type: Boolean,
    default: false,
  },
  collapseTagsTooltip: {
    type: Boolean,
    default: false,
  },
  filter: {
    type: String as PropType<'whole' | 'part'>,
    default: 'whole', // whole 查询全部 part 查询部分,默认为查询全部数据
  },
  filterType: {
    type: String,
    default: '4',
  },
  deviceClassification: {
    type: String,
    default: '',
  },
  withoutDeviceTypes: {
    type: String,
    default: '',
  },
  placement: {
    type: String as PropType<'bottom' | 'top' | 'left' | 'right'>,
    default: 'bottom',
  },
})

const emits = defineEmits<{
  (e: 'update:modelValue', v: Model): void
  (e: 'change', v: Model): void
  (e: 'getCheckedNodes', v: any[]): void
}>()

const options = ref<any>([])
const loading = ref(false)
const errorMsg = ref('')

// v-model 本地代理，防止子组件直接改动 prop
const innerValue = computed<Model>({
  get: () => props.modelValue as Model,
  set: (val) => emits('update:modelValue', val),
})

// 计算属性，避免每次渲染创建新对象
const cascaderProps = computed(() => ({
  expandTrigger: 'hover' as const,
  checkStrictly: props.checkStrictly,
  multiple: props.multiple,
  label: props.filter === 'whole' ? 'label' : 'deviceTypeName',
  value: props.filter === 'whole' ? 'value' : 'deviceTypeId',
}))

onMounted(() => {
  getOptions()
})

function handleChange(val: any) {
  emits('change', val)
  emits('getCheckedNodes', DeviceTypeCasRef.value.getCheckedNodes())
  setTimeout(() => {
    DeviceTypeCasRef.value && DeviceTypeCasRef.value.togglePopperVisible(false)
  }, 160)
}

const elCascaderOnClick = (e: MouseEvent) => {
  const target = e?.target as HTMLElement | null
  const parent = target?.parentElement
  const prev = parent?.previousElementSibling as HTMLElement | null
  prev?.click?.()
}

// 简单缓存，减少重复请求
const optionsCache = new Map<string, any[]>()

async function getOptions() {
  const deviceParams = {
    superviseId: ui.value.orgCode,
    unitId: (route.query.unitId as string) || '',
    fireSystemType: props.filter === 'part' ? props.filterType : '',
    deviceClassification: props.deviceClassification,
  }
  const cacheKey = JSON.stringify({ deviceParams, filter: props.filter, without: props.withoutDeviceTypes })
  if (optionsCache.has(cacheKey)) {
    options.value = optionsCache.get(cacheKey) as any[]
    return
  }
  loading.value = true
  errorMsg.value = ''
  try {
    const data = await getDeviceType(deviceParams, props.filter, props.withoutDeviceTypes)
    options.value = Array.isArray(data) ? data : []
    optionsCache.set(cacheKey, options.value)
  } catch (err) {
    console.error('获取设备类型失败:', err)
    errorMsg.value = '设备类型加载失败'
    options.value = []
  } finally {
    loading.value = false
  }
}

// 依赖变更时自动刷新
watch(
  () => [
    props.filter,
    props.filterType,
    props.deviceClassification,
    props.withoutDeviceTypes,
    route.query.unitId,
    ui.value.orgCode,
  ],
  () => {
    getOptions()
  }
)

// 暴露方法
defineExpose({
  getCheckedNodes: () => DeviceTypeCasRef.value?.getCheckedNodes?.() ?? [],
})
</script>
<style lang="scss" scoped>
.custom-node {
  width: 100%;
  position: absolute;
  top: 0;
  right: 0;
  padding-left: 40px;
  z-index: 1;
}
</style>
