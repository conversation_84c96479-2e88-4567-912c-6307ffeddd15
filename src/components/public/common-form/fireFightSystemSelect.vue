<template>
  <el-select v-model="modelValue" placeholder="全部" clearable @change="handleChange">
    <el-option v-for="item in options" :key="item.systemId" :label="item.systemName" :value="item.systemId" />
  </el-select>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { queryFaultSystemList } from '@/common/comResponse/index'
const route = useRoute()
const router = useRouter()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  fireFightingSystemOptions: {
    type: Array,
    default: []
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

const options = ref<any>([])

const selectName = computed(() => {
  const filterRes = options.value.filter(
    (item: any) => item.id === props.modelValue
  )
  if (filterRes.length > 0) {
    return filterRes[0].name
  } else {
    return ''
  }
})

onMounted(() => {
  if (props.fireFightingSystemOptions.length > 0) {
    options.value = props.fireFightingSystemOptions
  } else {
    init()
  }
})

function init() {
  getOptions()
}

function handleChange(val: any) {
  emits('update:modelValue', val)
  emits('change', { id: val, name: selectName.value })
}

async function getOptions() {
  const params = {
    maintenancePlanId: route.query.planId ? route.query.planId : ''
  }
  options.value = await queryFaultSystemList(params)
}
</script>
<style lang="scss" scoped>

</style>
