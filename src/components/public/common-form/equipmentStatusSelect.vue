<template>
  <el-select
    fit-input-width
    v-model="inputVal"
    @change="handleChange"
    placeholder="全部"
    :disabled="disabled"
    :clearable="clear"
    filterable
    :multiple="multiple ? true : false"
    :collapse-tags="multiple ? true : false"
    :collapse-tags-tooltip="multiple ? true : false"
  >
    <el-option
      v-for="item in options"
      :key="item[optionsProps.value]"
      :label="item[optionsProps.label]"
      :value="item[optionsProps.value]"
    ></el-option>
  </el-select>
  <!-- collapse-tags
      collapse-tags-tooltip -->
</template>

<script lang="ts" setup>
import { onMounted, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  options: {
    type: Object,
    default: () => {},
  },
  multiple: {
    type: String,
    default: '',
  },
  optionsProps: {
    type: Object,
    default: function () {
      return {
        value: 'value',
        label: 'label',
      }
    },
  },
  disabled: {
    type: <PERSON><PERSON>an,
    default: false,
  },
  clear: {
    type: <PERSON>olean,
    default: true,
  },
})

// const inputVal = ref(null)
const inputVal = computed({
  get: () => {
    if (props.multiple) {
      if (props.modelValue && !Array.isArray(props.modelValue)) return String(props.modelValue).split(',')
      return []
    } else {
      return props.modelValue
    }
  },
  set: (val) => {
    return val
  },
})
onMounted(() => {
  // if (props.multiple) {
  //   if (props.modelValue && !Array.isArray(props.modelValue)) inputVal.value = props.modelValue.split(',')
  // }
})
const emits = defineEmits(['update:modelValue', 'change'])

function handleChange(val: any) {
  if (props.multiple) {
    emits('update:modelValue', val.join(','))
    emits('change', val.join(','))
    return
  }
  emits('update:modelValue', val)
  emits('change', val)
}
</script>
<style lang="scss" scoped></style>
