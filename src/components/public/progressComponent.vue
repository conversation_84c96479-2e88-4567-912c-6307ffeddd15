<template>
    <div class="progress-component">
        <div class="percentage-bar" :style="{width: percentage + '%'}"></div>
        <div class="percentage-round" :style="{background: backgroundColor, left: roundPercentage + '%'}"></div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'

const props = defineProps({
    progressColors: {
        type: Array,
        default: () => ['#17B5FE', '#9BE1FF']
    },
    percentage: {
        type: [Number, String],
        default: 0
    }
})

const backgroundColor = computed(() => props.progressColors[0] as string);

const percentage = computed(() => {
    let p = props.percentage;
    return typeof p === 'string' ? parseFloat(p) : p;
})
const roundPercentage = computed(() => {
    return Math.max(8, percentage.value);
})

</script>

<script lang="ts">
export default {
    name: 'progressComponent'
}
</script>

<style lang="scss">
.progress-component {
    width: 100%;
    // height: 22px;
    height: 16PX;
    background: #F1F1F1;
    border-radius: 10px;
    overflow-x: hidden;
    position: relative;
    .percentage-bar {
        width: 0%;
        height: 100%;
        border-radius: 10px;
        background-image: linear-gradient(to right, v-bind(progressColors));
        transition: all .5s;
        box-shadow:rgba(31, 192, 185, 0.3) 1px 10px 10px ;
    }
    .percentage-round {
        position: absolute;
        top: 0;
        border-radius: 50%;
        transform: translateX(-20px);
        width: 16PX;
        height: 16PX;
        transition: all .5s;
        display: flex;
        justify-content: center;
        align-items: center;
        &::after {
            content: '';
            width: 6PX;
            height: 6PX;
            background: white;
            display: block;
            border-radius: 50%;
        }
    }
}
</style>