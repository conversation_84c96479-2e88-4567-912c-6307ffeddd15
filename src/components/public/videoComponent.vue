<template>
  <div class="video-component" v-if="!isIframe">
    <video controls :id="id" class="video-tag video-js vjs-default-skin">
      <source :src="videoUrl" />
    </video>
  </div>
  <div class="video-component" v-else>
    <iframe class="myiframe" :src="videoUrl" frameborder="0"></iframe>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, nextTick, computed } from 'vue'
import videojs from 'video.js'
import { v4 as uuidV4 } from 'uuid'

const props = defineProps<{
  videoUrl: string | { isIframe?: boolean; _videoUrl: string }
}>()
// {
//   videoUrl: {
//     type: [Object, String],
//     default: '',
//   },
// }
let videoUrl = computed(() => {
  if (props.videoUrl && typeof props.videoUrl == 'string') {
    try {
      // 零视视频url 是json字符串
      let _url = JSON.parse(props.videoUrl as string)
      return _url
    } catch (error) {
      return props.videoUrl
    }
  } else if (props.videoUrl && (props.videoUrl as { _videoUrl: string })._videoUrl) {
    return (props.videoUrl as { _videoUrl: string })._videoUrl
  } else {
    return ''
  }
})

const isLinkingvision = computed(() => {
  return !!(typeof videoUrl.value == 'object' && videoUrl.value?.token)
})
let isIframe = computed(() => {
  if (props.videoUrl && typeof props.videoUrl == 'string') {
    return false
  } else {
    return (props.videoUrl as { isIframe?: boolean }).isIframe || false
  }
})

let videoInstance: any = null

const id = 'video_' + uuidV4()

onMounted(() => {
  if (isLinkingvision.value) {
    let videoConfig = {
      videoid: id, // 视频id
      protocol: window.location.protocol, //'http:' or 'https:'
      host: videoUrl.value.host, //'localhost:8080'
      rootpath: '/', // '/' or window.location.pathname
      token: videoUrl.value.token,
      hlsver: 'v1', //v1 is for ts, v2 is for fmp4
      session: videoUrl.value.session, //session got from login
    }
    nextTick(() => {
      videoInstance = H5sPlayerCreate(videoConfig)
      videoInstance.connect()
    })

    return
  }
  if (isIframe.value) {
    return
  } else {
    nextTick(() => {
      videoInstance = videojs(id, {
        autoplay: true,
      })
      videojs.hook('error', function (player, err) {
        document.querySelector('.vjs-modal-dialog-content').innerHTML =
          `<div style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;color: #fff;">
            <div>
              <div>
                暂时无法播放视频
              </div>
              <div>
                请查看以下问题解决：1.设备网络问题；2.设备电源问题；3.设备硬件故障
              </div>
            </div>
          </div>`
      })
    })
  }
})

onUnmounted(() => {
  videoInstance && videoInstance.dispose && videoInstance?.dispose()
  videoInstance?.disconnect && videoInstance?.disconnect() // 零视 视频销毁
  // vdom.disconnect();
  // 			delete this.videodom[id];
  // 			this.videodom[id] = null;
  // 			this.$refs[id][0].pause();
})
</script>

<script lang="ts">
export default {
  name: 'videoComponent',
}
</script>

<style lang="scss">
.video-component {
  height: 100%;
  width: 100%;

  .myiframe,
  .video-tag,
  video {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}

.vjs-error .vjs-error-display::before {
  display: none !important;
  content: '';
}
</style>
