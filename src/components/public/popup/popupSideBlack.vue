<template>
  <popup-component
    class="popup-side-black-wrap"
    v-model="isOpen"
    :close-on-click-modal="closeOnClickModal"
    name="left"
    :append-to-body="true"
    :mask-style="{ opacity: 0 }"
    @mask-click="maskClick"
  >
    <div class="popup-side">
      <header class="flex justify-between items-center relative">
        <div class="flex items-center">
          <div class="sub-title inline-block">{{ popupTitle }}</div>
          <slot name="subTitle"></slot>
        </div>
        <el-icon @click="close" :size="24" color="#999999">
          <icon-close-bold class="close-icon" />
        </el-icon>
      </header>
      <div class="content">
        <slot></slot>
      </div>

      <slot name="footer"></slot>
    </div>
  </popup-component>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  popupTitle: {
    type: String,
    default: '测试标题',
  },
  width: {
    type: String,
    default: '420px',
  },
  closeOnClickModal: {
    type: Boolean,
    default: true,
  },
})
console.log('传进来的width是：' + props.width)
const emits = defineEmits(['close', 'update:modelValue'])
// 使用计算属性处理 v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => {
    emits('update:modelValue', value)
  },
})
function maskClick() {
  close()
}

function close() {
  emits('close')
  emits('update:modelValue', false)
}
</script>

<script lang="ts">
export default {
  name: 'popupSide',
}
</script>

<style lang="scss" scoped>
.popup-side {
  width: 420px;
  height: calc(100% - 88px);
  display: grid;
  grid-template-rows: auto 1fr auto;
  position: fixed;
  right: 24px;
  top: 70px;
  box-shadow: -3px 0px 20px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(9, 32, 65, 1);
  border: 1px solid rgba(53, 134, 255, 0.5);

  header {
    height: 38px;
    border: none;
    padding: 6px 20px;
    background: url('@/assets/image/energyStorageSafety/popup-title-bg.png') no-repeat center;
    background-size: contain;

    .back {
      color: #1089ff;
      letter-spacing: 2px;
      padding: 0 10px;
    }
  }

  .content {
    position: relative;
    padding: 20px;

    overflow-y: overlay;
    scrollbar-gutter: stable;
  }

  .popup-side-footer {
    width: 100%;
    height: 64px;
    border-top: 1px solid #ebeef5;
    display: flex;
    justify-content: end;
    align-items: center;
    padding: 0 24px;
  }

  .el-icon {
    cursor: pointer;

    .close-icon {
      width: 24px;
      height: 24px;
    }

    svg {
      width: inherit;
      height: inherit;
    }
  }

  .sub-title {
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    font-style: italic;
  }

  .sub-title2 {
    color: #333333;
    position: relative;
    // padding-left: 15px;
    font-size: 16px;
    font-family: 'Alibaba-PuHuiTi-Medium';
    white-space: nowrap;
    // &::before {
    //   content: '';
    //   width: 4px;
    //   height: 16px;
    //   background: #3093fa;
    //   display: inline-block;
    //   position: absolute;
    //   top: 50%;
    //   left: 0;
    //   border-radius: 2px;
    //   transform: translateY(-50%);
    // }
  }

  .sub-title.round {
    &::before {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      position: inherit;
      margin-right: 5px;
    }
  }
}
</style>
