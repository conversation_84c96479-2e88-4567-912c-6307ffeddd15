<template>
  <popup-wrap
    v-model="isShow"
    class="popup-video-list"
    :popup-title="popupTitle"
  >
    <template v-if="props.videoList.length > 1" #title>
      <div class="py-10px">
        <div>
          <el-button
            @click="handleClick(item, index)"
            :type="index === activeIndex ? 'primary' : ''"
            v-for="(item, index) in props.videoList"
            :key="index"
            >视频{{ index + 1 }}</el-button
          >
        </div>
      </div>
    </template>
    <p class="px-20px pb-10px">
      地址：{{ currentVideo._deviceAddress || '未知' }}
    </p>
    <video-component
      style="width: 1100px; height: 600px"
      :video-url="videoPath"
      :key="now()"
    ></video-component>
  </popup-wrap>
</template>

<script lang="ts" setup>
import { watch, computed, ref, onMounted } from 'vue'
import popupWrap from '@/components/public/popup/popupWrap.vue'
import videoComponent from '@/components/public/videoComponent.vue'
import { ElButton } from 'element-plus'
import { getXkbVideoUrl } from '~/common/services'
// import { now } from '~/common/utils';

const activeIndex = ref(0)

const props = defineProps({
  popupTitle: {
    type: String,
    default: '视频播放'
  },
  videoList: {
    type: Array,
    default: () => []
  }
})

const isShow = ref(false)

const alxkbUrl = ref('')

const currentVideo = computed(() => {
  const ai = activeIndex.value
  return (props.videoList[ai] || {}) as any
})

const videoPath = computed(() => {
  if (currentVideo.value._isAlxkb) {
    return alxkbUrl.value
  }
  return currentVideo.value._videoUrl
})

function handleClick(item, index) {
  activeIndex.value = index
  console.log(item)
  if (item._isAlxkb) {
    getXkbVideoUrl({
      deviceId: item.deviceId,
      deviceNum: item.deviceNum
    }).then((res: any) => {
      //如果路径为空 随机地址 视频组件内部会处理
      alxkbUrl.value = res || 'aaabbb_' + Date.now()
    })
  }
}

onMounted(() => {
  isShow.value = true
  handleClick(props.videoList[0], 0)
})
</script>

<script lang="ts">
export default {
  name: 'popupVideoList'
}
</script>

<style lang="scss">
.popup-video-list {
  .content {
    padding: 0;
  }
  header {
    border: none;
  }
}
</style>
