<template>
  <popup-component
    class="popup-side-wrap"
    v-model="isOpen"
    :close-on-click-modal="closeOnClickModal"
    name="left"
    @mask-click="maskClick"
  >
    <div class="popup-side">
      <header class="flex justify-between items-center relative">
        <div class="flex items-center">
          <el-icon v-if="isBackIcon" class="mr-10px" :size="18" color="#666666" @click="close">
            <Back />
          </el-icon>
          <div v-if="isBackIcon" class="sub-title2 inline-block">
            {{ popupTitle }}
          </div>
          <div v-if="!isBack && !isBackIcon" class="sub-title inline-block">
            {{ popupTitle }}
          </div>
          <slot name="subTitle"></slot>
        </div>
        <el-icon @click="close" :size="24" color="#999999" v-if="!isBack">
          <icon-close-bold class="close-icon" />
        </el-icon>
        <span v-else @click="close" track class="cursor-pointer back"> 返回 </span>
      </header>
      <div class="content">
        <slot></slot>
      </div>

      <slot name="footer"></slot>
    </div>
  </popup-component>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { Back } from '@element-plus/icons-vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  popupTitle: {
    type: String,
    default: '测试标题',
  },
  width: {
    type: String,
    default: '420px',
  },
  isBack: {
    type: Boolean,
    default: false,
  },
  closeOnClickModal: {
    type: Boolean,
    default: true,
  },
  isBackIcon: {
    type: Boolean,
    default: false,
  },
})
console.log('传进来的width是：' + props.width)
const emits = defineEmits(['close', 'update:modelValue'])
// 使用计算属性处理 v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => {
    emits('update:modelValue', value)
  },
})
function maskClick() {
  close()
}

function close() {
  emits('close')
  emits('update:modelValue', false)
}
</script>

<script lang="ts">
export default {
  name: 'popupSide',
}
</script>

<style lang="scss">
.popup-side-wrap {
  .popup-mask {
    opacity: 0;
  }

  .popup-side {
    width: 420px;
    height: 100%;
    display: grid;
    grid-template-rows: auto 1fr auto;
    background: #ffffff;
    // background: #F7F9FA; // UI图上是有背景颜色的
    position: fixed;
    right: 0;
    box-shadow: -3px 0px 20px rgba(0, 0, 0, 0.2);
    border-radius: 10px 0 0 10px;

    header {
      height: 64px;
      border-bottom: 1px solid var(--el-border-color);
      padding: 0 24px;

      .back {
        color: #1089ff;
        letter-spacing: 2px;
        padding: 0 10px;
      }
    }

    .content {
      position: relative;
      padding: 20px;

      overflow-y: overlay;
      scrollbar-gutter: stable;
    }

    .popup-side-footer {
      width: 100%;
      height: 64px;
      border-top: 1px solid #ebeef5;
      display: flex;
      justify-content: end;
      align-items: center;
      padding: 0 24px;
    }

    .el-icon {
      cursor: pointer;

      .close-icon {
        width: 24px;
        height: 24px;
      }

      svg {
        width: inherit;
        height: inherit;
      }
    }

    .sub-title {
      color: #333333;
      position: relative;
      padding-left: 15px;
      font-size: 16px;
      font-weight: bold;
      // font-family: 'Alibaba-PuHuiTi-Medium';
      white-space: nowrap;

      &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: #3093fa;
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 0;
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }

    .sub-title2 {
      color: #333333;
      position: relative;
      // padding-left: 15px;
      font-size: 16px;
      font-family: 'Alibaba-PuHuiTi-Medium';
      white-space: nowrap;
      // &::before {
      //   content: '';
      //   width: 4px;
      //   height: 16px;
      //   background: #3093fa;
      //   display: inline-block;
      //   position: absolute;
      //   top: 50%;
      //   left: 0;
      //   border-radius: 2px;
      //   transform: translateY(-50%);
      // }
    }

    .sub-title.round {
      &::before {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: inherit;
        margin-right: 5px;
      }
    }
  }
}
</style>
