<template>
  <popup-wrap
    v-model="modelValue"
    class="popup-video"
    :popup-title="popupTitle"
  >
    <video-component
      style="width: 1100px; height: 600px"
      :video-url="videoUrl"
    ></video-component>
  </popup-wrap>
</template>

<script lang="ts" setup>
import { watch, computed } from 'vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  videoUrl: {
    type: String,
    default: ''
  },
  popupTitle: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:modelValue'])

const modelValue = computed({
  get: () => props.modelValue,

  set: (val) => {
    emits('update:modelValue', val)
  }
})

watch(
  () => props.modelValue,
  (newVal) => emits('update:modelValue', newVal)
)

console.log(props.videoUrl, '***videoUrl')
</script>

<script lang="ts">
export default {
  name: 'popupVideo'
}
</script>

<style lang="scss">
.popup-video {
  .content {
    padding: 0;
  }
}
</style>
