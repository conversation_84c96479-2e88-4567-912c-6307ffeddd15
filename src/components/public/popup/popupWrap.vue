<template>
  <popup-component v-model="isOpen">
    <div class="popup-wrap">
      <header class="flex justify-between items-center">
        <div class="sub-title" :style="{ maxWidth: subTitleWidth }">
          <myTooltip :str="popupTitle" :size="16" :weight="700"></myTooltip>
        </div>
        <slot name="unitName"></slot>
        <el-icon @click="close" :size="24" color="#999999">
          <icon-close-bold class="close-icon" />
        </el-icon>
      </header>
      <!-- <div class="content">
        <slot></slot>
      </div> -->
      <div :class="popupTitle == '安消联动' ? 'content2' : 'content'">
        <slot></slot>
      </div>
    </div>
  </popup-component>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  popupTitle: {
    type: String,
    default: '测试标题',
  },
  width: {
    type: String,
    default: '400px',
  },
  subTitleWidth: {
    type: String,
    default: '400px',
  },
})

const emit = defineEmits(['close', 'update:modelValue'])

// 使用计算属性处理 v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  },
})

function close() {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<script lang="ts">
export default {
  name: 'popupWrap',
}
</script>

<style lang="scss">
.popup-wrap {
  background: white;
  border-radius: 6px;
  header {
    height: 64px;
    border-bottom: 1px solid var(--el-border-color);
    padding: 0 24px;
  }
  .content {
    padding: 24px;
  }
  .content2 {
    padding: 24px 24px 35px 24px;
  }
  .sub-title {
    max-width: 44em;
    color: #333333;
    position: relative;
    font-size: 18px;
    font-family: 'Alibaba-PuHuiTi-Medium';
  }
  .el-icon {
    cursor: pointer;

    .close-icon {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
