<template>
  <el-tabs class="detail-popup-top-tab h-full" v-model="active" @tab-click="tabClick">
    <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name">
      <slot :name="item.name" />
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'

const props: any = defineProps({
  active: {
    default: 'tab1'
  },
  tabs: {
    default: [
      {
        name: 'tab1',
        label: 'tab1'
      }
    ]
  }
})
const emits = defineEmits(['tabClick'])

onMounted(() => { })

function tabClick(e: any) {
  emits('tabClick', e)
}
</script>
<style lang="scss" scoped>
$spacing: 6px;

.detail-popup-top-tab {
  ::v-deep .el-tabs__header {
    padding: 0;
    background: #fff;

    .el-tabs__nav-wrap {
      &::after {
        display: none;
      }

      .el-tabs__nav-scroll {
        height: 52px;

        .el-tabs__nav {
          width: 100%;
          height: 100%;
          display: flex;
          border-radius: 10px;
          justify-content: space-between;
          padding: 0 6px;
          background-color: #f0f2f5;

          .el-tabs__item {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 16px;
            padding: 0;

            &.is-active {
              margin-top: $spacing;
              color: #fff;
              height: 40px;
            }
          }

          .el-tabs__active-bar {
            height: calc(100% - $spacing - $spacing);
            position: absolute;
            top: $spacing;
            left: 0;
            padding: $spacing;
            background: #0080ff;
            color: #fff;
            border-radius: 8px;
            z-index: 0;
          }
        }
      }
    }
  }

  :deep(.el-tabs__content) {
    height: calc(100% - 70px);
    // overflow: auto;
    overflow: hidden !important;

    &:hover {
      overflow-y: overlay !important;
      scrollbar-gutter: stable;
    }

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
