<template>
  <popup-wrap v-model="modelValue" class="popup-video" :popup-title="popupTitle">
    <ul class="flex" :video-Data="videoData" v-if="videoShow">
      <li class="w-80px h-40px text-center" v-for="(item, index) in videoData" :key="index">
        <el-button :type="Index == index ? 'primary' : ''" @click="changeVideo(item, index)"
          >视频{{ index + 1 }}</el-button
        >
      </li>
    </ul>
    <!-- <video-component style="width: 1100px; height: 600px;" :video-url="videoUrl"></video-component> -->
    <video-component style="width: 1100px; height: 600px" :video-url="videoUrl" :key="videoUrl"></video-component>
    <!--  1tgxi4unfoDrsB1S5X8i-->
  </popup-wrap>
</template>

<script lang="ts" setup>
import { watch, computed, ref, onMounted } from 'vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  // videoUrl: {
  //     type: String,
  //     default: ''
  // },
  popupTitle: {
    type: String,
    default: '',
  },
  videoData: {
    type: Array,
    default: () => [],
  },
})
// alert(props.videoUrl)
const videoShow = computed(() => {
  return props.videoData.length > 1
})

const emits = defineEmits(['update:modelValue'])

const modelValue = computed({
  get: () => props.modelValue,

  set: (val) => {
    emits('update:modelValue', val)
  },
})

watch(
  () => props.modelValue,
  (newVal) => emits('update:modelValue', newVal)
)
const Index: any = ref('')
const videoUrl: any = ref('')
watch(
  () => props.videoData,
  (newVal: any) => {
    // console.log('newVal',newVal)
    videoUrl.value = newVal.length ? newVal[0].useInfo.videoUrl : ''
  }
)
function changeVideo(item: any, val: any) {
  console.log(item)
  Index.value = val
  videoUrl.value = item.useInfo.videoUrl
}
onMounted(() => {
  // console.log('videoData',props.videoData)
})
</script>

<script lang="ts">
export default {
  name: 'videoAlarm',
}
</script>

<style lang="scss">
.popup-video {
  .content {
    padding: 0;
  }
  li {
    line-height: 40px;
  }
}
</style>
