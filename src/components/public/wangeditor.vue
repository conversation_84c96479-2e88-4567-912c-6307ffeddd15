<template>
  <div class="rich-editor-container" :style="containerStyle">
    <Toolbar
      :style="toolbarStyle"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      :style="editorStyle"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>
<script lang="ts" setup>
// import '@wangeditor/editor/dist/css/style.css'
import {
  onBeforeUnmount,
  ref,
  shallowRef,
  onMounted,
  watch,
  reactive
} from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import type { IEditorConfig, IToolbarConfig } from '@wangeditor/core'
import $API from '~/common/api'
import config from '~/config'
import { ElMessage } from 'element-plus'
import { nextTick } from 'process'
import { useUserInfo } from '@/store'

const props = defineProps({
  defaultValue: {
    type: String,
    default: ''
  },
  mode: {
    type: String,
    default: 'simple'
  },
  editorConfig: {
    type: Object,
    default: () => ({ placeholder: '请输入内容...' })
  },
  toolbarConfig: {
    type: Object,
    default: () => ({})
  },
  containerStyle: {
    type: Object,
    default: () => ({})
  },
  toolbarStyle: {
    type: Object,
    default: () => ({
      borderBottom: '1px solid #ccc'
    })
  },
  editorStyle: {
    type: Object,
    default: () => ({
      height: '200px',
      overflowY: 'hidden'
    })
  },
  onCreatedCallback: {
    type: Function,
    default: () => {}
  },
  uploadUrl: {
    type: String,
    default: '/notice/uploadFile'
  }
})

const ui = useUserInfo()

const editorRef = shallowRef()

const valueHtml = ref(props.defaultValue)

const emit = defineEmits(['updateNoticeFormContent', 'getHtmlVal'])

watch(
  () => valueHtml.value,
  () => {
    if (checkVal(valueHtml.value)) emit('updateNoticeFormContent', '')
    else emit('updateNoticeFormContent', valueHtml.value)
  }
)

/**
 * 判断editor富文本域是否为空
 * str返回的值为"" 代表输入框里面有值 成功
 * str返回！="" 代表里面有空格 回车 失败
 * */
function checkVal(str) {
  let num = 0,
    reg = /<p>(&nbsp;|&nbsp;\s+)+<\/p>|<p>(<br>)+<\/p>/g
  while (num < str.length && str != '') {
    num++
    let k = str.match(reg)
    if (k) {
      str = str.replace(k[0], '')
    }
  }
  return str == ''
}

onMounted(() => {
  if (props.toolbarConfig.toolbarKeys) {
    toolbarConfig.toolbarKeys = props.toolbarConfig.toolbarKeys
  }
})

onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = (editor: any) => {
  editorRef.value = editor
  valueHtml.value = props.defaultValue
  if (props.editorConfig.readOnly) {
    nextTick(() => {
      editor.disable()
    })
  }
}

// 获得输入的内容
const handleChange = (editor: any) => {
  emit('getHtmlVal', editor.getHtml())
}

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = reactive({
  // 用于去掉不需要的工具栏配置
  excludeKeys: [
    'insertVideo', // 去掉插入视频功能
    'fullScreen' // 去掉全屏功能
  ]
})
// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
  // 最长输入5000字
  maxLength: 5000,
  // 进入页面不自动聚焦
  autoFocus: true,
  MENU_CONF: {}
}
// 上传图片
//@ts-ignore
editorConfig.MENU_CONF['uploadImage'] = {
  async customUpload(file: any, insertFn: any) {
    upLoadImgVideo('1', file, insertFn)
  }
}

// 上传视频
//@ts-ignore
editorConfig.MENU_CONF['uploadVideo'] = {
  async customUpload(file: any, insertFn: any) {
    upLoadImgVideo('2', file, insertFn)
  }
}

// 封装 - 上传图片、视频
const upLoadImgVideo = (type: string, file: any, insertFn: any) => {
  if (file.size / 1024 / 1024 > 50) {
    ElMessage.error(`文件大小不能超过50MB!`)
    return false
  } else {
    // 这里根据自己的需求进行处理 --- S
    let formData: any = new FormData()
    formData.append('file', file)
    formData.append('createUserId ', ui.value.userId)
    formData.append('fileType', type)
    //  这里根据自己的需求进行处理 --- E
    $API
      .post({
        url: props.uploadUrl,
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      .then((res: any) => {
        if (res.code == 'success') {
          // 上传成功后拿到的路径插入到富文本编辑器中
          insertFn(config.image_url + res.data)
        } else {
          ElMessage.warning(`上传失败`)
        }
      })
      .catch(() => {
        ElMessage.warning(`上传失败`)
      })
  }
}

defineExpose({
  setContent(content) {
    valueHtml.value = content
  }
})
</script>
<script lang="ts">
export default {
  name: 'RichEditor'
}
</script>
<style lang="scss" scoped>
.rich-editor-container {
  border: 1px solid #ccc;
  z-index: 300;
}
</style>
