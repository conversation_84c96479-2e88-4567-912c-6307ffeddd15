<template>
  <div class="audioPlayBox" @click="play">
    <audio ref="audioRef" class="audio-player" :src="config.base_host + audioUrl"></audio>
    <p class="progressBox">
      <svg-icon v-if="!isPlaying" name="audio" :size="13" color="#1890FF" class="audioIcon" ref="playIcon"></svg-icon>
      <img v-else class="!w-16px" src="@/assets/image/audio1.gif" alt="" srcset="" />
    </p>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import $API from '~/common/api'
import config from '~/config'

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const isPlaying = ref(false)
const audioUrl = ref('')
const audioRef = ref()
const playIcon = ref()

async function play() {
  let _audio = audioRef.value
  //监听audio是否加载完毕，如果加载完毕，则读取audio播放时间
  if (_audio.networkState === 3)
    return ElMessage.error('当前浏览器不支持该音频播放，请下载至本地')
  // 1.暂停当前正在播放的音频,暂停之后监听当前播放的audio,把动画状态isPlaying值重置为初始值
  stopOtherAudios()
  if (!isPlaying.value) {
    _audio.play()
    isPlaying.value = true
  } else {
    _audio.pause()
    isPlaying.value = false
  }
}

function getAudioSrc() {
  return $API.post({
    url: '/dutyRecord/downloadFile',
    params: {
      recordId: props.id
    }
  })
}

const stopOtherAudios = () => {
  const audioElements = document.getElementsByTagName('audio')
  for (let i = 0; i < audioElements.length; i++) {
    const audio = audioElements[i]
    if (audio !== audioRef.value) {
      audio.pause()
    }
  }
}

onMounted(async () => {
  const res = await getAudioSrc()
  audioUrl.value = res.data
  audioRef.value.addEventListener('pause', function () {
    // 2.暂停结束都会触发
    console.log('暂停结束都会触发')
    isPlaying.value = false
  })
})

onUnmounted(() => {
  stopOtherAudios()
})
</script>

<script lang="ts">
export default {
  name: 'audioComponent'
}
</script>
<style lang="scss">
.audioPlayBox {
  display: flex;
  align-items: center;
  width: 134px;
  height: 24px;
  background-color: #eaedf0;
  border-radius: 4px;
  cursor: pointer;

  .progressBoxo {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}

@keyframes play {
  0% {
    width: 0;
    /*初始状态 透明度为0*/
  }

  50% {
    width: 30px;
  }

  100% {
    width: 50px;
    /*结尾状态 透明度为1*/
  }
}

.progressBox {
  padding-left: 5px;

  .animate-play {
    animation: play 2s infinite 0.8s;
  }

  .audioIcon {
    margin-top: -1px;
  }
}
</style>
