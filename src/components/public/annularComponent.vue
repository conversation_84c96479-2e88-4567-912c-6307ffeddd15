<template>
  <div
    class="annular-component"
    :style="`width: ${width}rem; height: ${width}rem`"
  >
    <div
      class="outer_circle"
      :style="`width: ${width * 0.776}rem;height: ${width * 0.776}rem`"
    ></div>
    <div class="inner_circle"></div>
    <div class="grade_wrap">
      <div style="display: flex">
        <span class="score_value font-din-num">{{ value }}</span
        ><span class="score_txt"> {{ unit }} </span>
      </div>
    </div>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="50"
      height="50"
      viewBox="0 0 50 50"
    >
      <defs>
        <linearGradient x1="1" y1="0" x2="0" y2="0" id="gradient">
          <stop offset="0%" stop-color="#3093FA"></stop>
          <stop offset="50%" stop-color="#5AE5FE"></stop>
          <stop offset="100%" stop-color="#4FDFB3"></stop>
        </linearGradient>
      </defs>
      <circle
        fill="transparent"
        class="pie_bg"
        stroke-width="5"
        cx="25"
        cy="25"
        r="20"
      ></circle>
      <circle
        fill="transparent"
        class="pie_bar"
        stroke-width="5"
        cx="25"
        cy="25"
        r="20"
        style="stroke-dasharray: 12.5664, 125.664"
      ></circle>
    </svg>
    <slot name="bottom"></slot>
  </div>
</template>

<script lang="ts">
export default {
  name: 'annularComponent'
}
</script>

<script lang="ts" setup>
import { onMounted, watch, nextTick } from 'vue'

const props = defineProps({
  value: {
    type: [Number, String],
    default: 0
  },
  width: {
    type: Number,
    default: 1.34
  },
  title: {
    type: String,
    default: '消防安全指数'
  },
  unit: {
    type: String,
    default: '%'
  }
})

function drawPieCircle(value: number) {
  const pieBar = document.querySelector('.pie_bar') as any
  const pathLen = 40 * Math.PI //圆的周长
  pieBar!.style.strokeDasharray =
    (pathLen * Number(value)) / 100 + ' ' + pathLen
}

watch(
  () => props.value,
  (newValue) => {
    nextTick(() => {
      drawPieCircle(Number(newValue))
    })
  },
  {
    immediate: true
  }
)

onMounted(() => {})
</script>

<style lang="scss">
.annular-component {
  position: relative;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  box-shadow: 1px 10px 10px #e4f1fd;
  background-color: #fff;
  position: relative;
  svg {
    vertical-align: middle;
    width: 284px;
    height: 284px;
  }

  .pie_bg {
    stroke: #e4f1fd;
    opacity: 0.3;
  }

  .pie_bar {
    stroke: url(#gradient);
  }

  circle {
    stroke-dashoffset: 0;
    transform: rotate(0);
    transform-origin: center;
    transition: all 0.2s;
    stroke: url(#gradient);
    z-index: 2;
  }
  .outer_circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background-color: #67a1fd;
    border-radius: 50%;
  }
  .inner_circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 208px;
    height: 208px;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 1px 10px 10px #e4f1fd inset;
  }
  .grade_wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    // width: 180px;
    // height: 180px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .score_value {
      font-size: 56.29px;
      color: #333;
    }
    .score_txt {
      font-size: 0.125rem;
      color: #adbac6;
      margin-top: 30px;
      margin-left: 6px;
    }
    .grade_index_txt {
      color: #8695ab;
      font-size: 18px;
    }
  }
}
</style>
