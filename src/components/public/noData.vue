<script lang="ts">
import { h, defineComponent } from 'vue'
import imgSrc from '@/assets/image/no-data-new.png'
export default defineComponent({
  name: 'noData',
  props: {
    labelTitle: {
      type: String,
      default: '暂无数据'
    },
    color: {
      type: String,
      default: '#909399'
    },
    imgSrc: {
      type: String,
      default: imgSrc
    }
  },
  render() {
    return h(
      'div',
      {
        class: 'no-data-page text-center'
      },
      [
        h('img', {
          src: this.imgSrc,
          class: 'inline-block w-200px'
        }),
        h(
          'p',
          {
            style: `color: ${this.color}`,
            class: 'w-250px'
          },
          this.labelTitle
        )
      ]
    )
  }
})
</script>

<style>
.no-data-page {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
