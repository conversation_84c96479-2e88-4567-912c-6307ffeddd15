<template>
  <div class="custom-table-wrap">
    <el-table ref="tableRef" v-loading="loading" :data="tableDataModel" v-bind="tableAttrs" class="custom-table">
      <template v-for="(column, i) in props.columns" :key="i">
        <el-table-column v-bind="column">
          <template v-if="column.slot" #default="{ row }">
            <slot :name="column.slot" :row="row" />
          </template>
          <template v-for="(slotNow, slotName) in column.slots || {}" #[slotName]="scope">
            <slot :name="slotNow" v-bind="scope" />
          </template>
        </el-table-column>
      </template>
      <template v-for="(slotNow, slotName) in props.slots || {}" #[slotName]="scope">
        <slot :name="slotNow" v-bind="scope" />
      </template>
    </el-table>
    <div class="table-list_pagination" v-if="!props.noPage && tableDataModel.length > 0">
      <el-pagination
        :currentPage="pageModel.pageNo"
        :page-size="pageModel.pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :small="false"
        :disabled="false"
        layout="total, prev, pager, next, sizes"
        :total="pageModel.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        v-bind="props.pagination"
      />
    </div>
    <div v-else :style="{ height: props.bottomHeight }"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, useAttrs, onUnmounted } from 'vue'
import type { TableColumnCtx, TableProps, ElTable, PaginationProps } from 'element-plus'
import Sortable from 'sortablejs'

defineOptions({
  name: 'TableList',
})
export interface ITableRow {
  [key: string]: string | number | boolean | Date | any
}
export type IColumn = Partial<TableColumnCtx<ITableRow>> & {
  slot?: string
  slots?: {
    default?: string
    header?: string
    filterIcon?: string
    expand?: string
  }
}
export type IPaginationProps = Partial<PaginationProps>
interface IData<T = ITableRow> {
  total: number
  rows: T[]
}
export interface IRes<T = ITableRow> {
  code?: string
  data?: IData<T>
}
export interface IPageModel {
  pageNo: number
  pageSize: number
  total: number
}

export type IProps<T = ITableRow> = Partial<TableProps<ITableRow>> & {
  data?: T[]
  auto?: boolean
  columns: IColumn[]
  noPage?: boolean
  transformRes?: (res: any) => IRes<T>
  api?: ((params: IPageModel) => Promise<IRes<T>>) | boolean
  slots?: {
    [key in 'append' | 'empty']?: string
  }
  pagination?: IPaginationProps
  drag?: boolean
  bottomHeight?: string
}

const props = withDefaults(defineProps<IProps>(), {
  auto: true,
  noPage: true,
  drag: false,
  data: () => [] as ITableRow[],
  api: false,
  columns: () => [] as IColumn[],
  transformRes: (res: IRes) => res as unknown as IRes,
  bottomHeight: '20px',
})

const attrs = useAttrs()
const emit = defineEmits(['update:data'])
const tableRef = ref<InstanceType<typeof ElTable> | null>(null)
const loading = ref(false)
const tableData = ref<ITableRow[]>([])
const pageModel = ref<IPageModel>({
  pageNo: 1,
  pageSize: (props.pagination && props.pagination.pageSizes && props.pagination.pageSizes[0]) || 10,
  total: 0,
})

// 兼容静态数据展示和接口请求展示
const tableDataModel = computed<ITableRow[]>({
  get() {
    if (typeof props.api === 'function') {
      return tableData.value
    } else {
      return props.data
    }
  },
  set(val) {
    if (typeof props.api === 'function') {
      tableData.value = val
    } else {
      emit('update:data', val)
    }
  },
})
// 透传时剔除当前组件使用的props
const tableAttrs = computed(() => {
  const undefinedProps = Object.keys(props).reduce((acc, key) => {
    if (key !== 'data' && key !== 'columns') {
      acc[key] = undefined
    }
    return acc
  }, {})
  return {
    ...attrs,
    ...undefinedProps,
  }
})

const getTableRef = () => {
  return tableRef.value || {}
}
const handleSizeChange = (val: number) => {
  pageModel.value.pageSize = val
  getTableData()
}
const handleCurrentChange = (val: number) => {
  pageModel.value.pageNo = val
  getTableData()
}
function getTableData(page?: number) {
  if (typeof props.api === 'function') {
    if (page && typeof page === 'number') {
      pageModel.value.pageNo = page
    }
    loading.value = true
    props
      .api(pageModel.value)
      .then((res: IRes<ITableRow>) => {
        const resData = props.transformRes(res)
        pageModel.value.total = 0
        if (resData && resData.code == 'success') {
          tableData.value = resData.data?.rows || []
          pageModel.value.total = resData.data?.total || 0
          if (pageModel.value.pageNo > 1 && tableData.value.length === 0) {
            getTableData(pageModel.value.pageNo - 1)
          }
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}
onMounted(() => {
  if (typeof props.api === 'function' && props.auto) {
    getTableData()
  }
  if (props.drag) {
    initDragAndDrop()
  }
})

let sortable: Sortable | null = null
const initDragAndDrop = () => {
  const tableEl = tableRef.value?.$el
  if (!tableEl) return

  const tbody = tableEl.querySelector('tbody')
  if (!tbody) return

  sortable = new Sortable(tbody, {
    animation: 150, // 拖拽动画时长（ms）
    handle: '.el-table__row', // 拖拽触发区域（整行可拖）
    onEnd: (evt) => {
      // 拖拽结束后更新数据顺序
      const newData = [...tableDataModel.value]
      const [movedItem] = newData.splice(evt.oldIndex, 1)
      newData.splice(evt.newIndex, 0, movedItem)
      tableDataModel.value = newData
    },
  })
}
onUnmounted(() => {
  sortable?.destroy() // 组件卸载时销毁实例，避免内存泄漏
})

defineExpose({
  getTableRef,
  tableData,
  getTableData,
})
</script>
<style scoped lang="scss">
.custom-table-wrap {
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
}
.table-list_pagination {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}
.custom-table {
  flex: 1;
  --el-table-header-bg-color: rgba(66, 111, 191, 0.3);
  --el-table-header-text-color: #fff;
  --el-table-tr-bg-color: transparent;
}
</style>
