<template>
    <div class="gs-cropper-component">
        <div class="gs-cropper-wrapper">
            <div class="gs-cropper-canvas">
                <img :src="props.imageUrl" ref="img" alt="" />
            </div>
            <div class="gs-cropper-preview">
                <div class="gs-cropper-preview-circle"></div>
                <div class="gs-cropper-preview-square"></div>   
            </div>
        </div>
        <div class="pt-20px flex justify-end">
            <!-- <el-button type="primary" @cl;>选择图片</el-button> -->
            <el-button type="primary" @click="confirm">确认选择</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import Cropper from 'cropperjs';

const props = defineProps({
    imageUrl: {
        type: String,
        default: ''
    }
});

const emits = defineEmits(['confirm']);

let cropper: Cropper | null = null;

const img = ref();

function confirm() {
    const canvas = cropper!.getCroppedCanvas() as HTMLCanvasElement;
    canvas.toBlob((result) => {
        const file = new File([result!], Date.now() + '.png', {
            type: result!.type
        })
        emits('confirm', {
            file: file,
            image: canvas.toDataURL()
        }, 'image/png');
    })
}

function init() {
    cropper = new Cropper(img.value, {
        // aspectRatio: 0.2,
        // aaspectRatio: 5,
        // cropBoxResizable: false,
        preview: '.gs-cropper-preview-circle, .gs-cropper-preview-square',
        viewMode: 1,
        dragMode: 'move',
        crop(event) {
            // console.log(event)
        },
    });
}

onMounted(() => {
    init();
})
</script>

<script lang="ts">
export default {
    name: 'cropper-component'
}
</script>

<style lang="scss">
.gs-cropper-component {
    width: 100%;
    height: 100%;
    .gs-cropper-wrapper {
        &::after {
            display: block;
            clear: both;
            content: "";
        }
        .gs-cropper-canvas {
            width: 300PX;
            height: 300PX;
            float: left;
        }
        .gs-cropper-preview {
            float: left;
            margin-left: 20px;
            .gs-cropper-preview-circle {
                width: 140PX !important;
                height: 140PX !important;
                margin-bottom: 20PX;
                overflow: hidden;
                border-radius: 50%;
                img {
                    border-radius: 50%;
                    overflow: hidden;
                }
            }
            .gs-cropper-preview-square {
                width: 140PX !important;
                height: 140PX !important;
                overflow: hidden;
            }
        }
    }
    img {
        height: 100%;
        display: block;
    }
}
</style>