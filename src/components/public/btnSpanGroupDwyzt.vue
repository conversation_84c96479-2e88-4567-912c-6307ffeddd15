<!-- 按钮组组件 -->
<template>
  <div :class="plain ? 'button-group-plain' : 'button-group'">
    <span v-for="btnItem in btnOptions" :key="btnItem.groupId" class="btn" track
      :class="{ activeTab: activeTab === btnItem.groupId }" @click="tabChange(btnItem.groupId)">
      {{ btnItem.groupName }}
    </span>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
type item = {
  groupId: string
  groupName: string
}

const btnOptions = ref<item[]>([
  {
    groupId: '1',
    groupName: '剩余电流'
  },
  {
    groupId: '2',
    groupName: '线温度'
  },
  {
    groupId: '3',
    groupName: '相电流'
  },
  {
    groupId: '4',
    groupName: '相电压'
  }
])

const props = defineProps({
  activeTab: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: []
  },
  plain: {
    type: Boolean,
    default: false // 默认为安全\设备一张图的按钮样式,当plain为true的时候为普通样式
  }
})

const emits = defineEmits(['update:activeTab', 'setTab'])

const tabChange = (type) => {
  emits('update:activeTab', type)
  emits('setTab', type)
}

onMounted(() => {
  if (props.options.length > 0) {
    btnOptions.value = props.options as item[]
  }
})
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  align-items: flex-end;
  cursor: pointer;
  user-select: none;

  // .btn {
  //   color: #999999;
  //   margin-right: 14px;
  // }
  &>div {
    background-color: red;
    line-height: normal;
    line-height: 28px;
    background: rgba(4, 37, 80, 1);
    border: 1px solid #3f9bcf;
    border-left: none;
    padding: 0 8px;
    box-shadow: inset 0px 0px 18px 0px rgba(63, 155, 207, 1);
    font-size: 14px;
    color: rgba(116, 201, 255, 1);
  }

  &>div:nth-of-type(1) {
    border-left: 1px solid #3f9bcf;
  }

  .activeTab {
    background-image: url(@/assets/image/yzt/tabs-bg.png);
    background-size: 100% 100%;
    color: #fff;
  }
}

.button-group-plain {
  display: flex;
  align-items: flex-end;
  cursor: pointer;
  user-select: none;

  .btn {
    color: #999999;
    font-size: 12px;
    margin-right: 14px;
  }

  &>div {
    line-height: normal;
    line-height: 28px;
    border: 1px solid #dcdfe6;
    padding: 0 8px;
    font-size: 14px;
    color: #666666;
  }

  // & > div:not(.activeTab) {
  //   border-left: none;
  // }

  &>div:nth-of-type(1) {
    border-left: 1px solid #dcdfe6;
    border-radius: 4px 0px 0px 4px;
  }

  &>div:last-child {
    border-radius: 0 4px 4px 0px;
  }

  .activeTab {
    color: #333333;
    border-color: #0080ff !important;
  }
}
</style>
