<template>
  <div class="gis-search" :class="{ searchClass: searchClass }">
    <div
      :class="[inputShow ? 'search-box-actived' : '', 'search-box pa-10 border-box']"
      @mouseenter="enterHandle"
      @mouseleave="leaveHandle"
    >
      <el-input
        v-show="inputShow"
        v-model="input"
        placeholder="请输入单位名称"
        clearable
        @input="inputChange"
        @focus="handleSelectFocus"
        @blur="handleSelectBlur"
      >
        <template #suffix>
          <el-icon color="#fff">
            <Search />
          </el-icon>
        </template>
      </el-input>

      <div class="icon-box" v-if="!inputShow">
        <el-icon color="#FFF">
          <Search />
        </el-icon>
      </div>
    </div>
    <div class="list-box" v-show="showList">
      <ul v-if="filterList">
        <li
          class="item"
          v-for="(n, i) in filterList"
          track
          @click="selectItem(n)"
          :key="i"
          :class="{ active: n.value == activeId }"
        >
          <myTooltip :str="n.label"></myTooltip>
        </li>
        <li class="item no-text" v-if="filterList.length <= 0">暂无数据</li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onActivated } from 'vue'
import { Search } from '@element-plus/icons-vue'
const inputShow = ref(false)
const input = ref('')
const activeId = ref('')
const showList = ref(false)
const emits = defineEmits(['change'])
const selectFocus = ref(false)

interface listItem {
  label: string
  value: string
}

const props = defineProps<{
  list: Array<listItem>
  searchClass?: ''
}>()

onActivated(() => {
  input.value = ''
  inputShow.value = false
})
const searchListData = computed(() => {
  return props.list || []
})

const filterList = computed(() => {
  return searchListData.value.filter((i) => i.label.indexOf(input.value) != -1) || []
})

const selectItem = (n) => {
  input.value = n.label
  activeId.value = n.value
  emits('change', n)
  setTimeout(() => {
    showList.value = false
  }, 100)
}

const inputChange = (val) => {
  if (val) showList.value = true
  else if (!val) showList.value = false
}

const enterHandle = () => {
  inputShow.value = true
}
const leaveHandle = () => {
  if (selectFocus.value) return
  inputShow.value = false
  setTimeout(() => {
    showList.value = false
  }, 100)
}

const handleSelectFocus = () => {
  selectFocus.value = true
}
const handleSelectBlur = () => {
  selectFocus.value = false
  leaveHandle()
}
</script>

<style lang="scss" scoped>
.search-box {
  width: 36px;
  min-height: 36px;
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
  background: rgba(18, 89, 252, 0.5);
  border: 1px solid #079afd;
  box-shadow: inset 0px 0px 16px 0px rgba(10, 212, 255, 0.34);

  :deep(.el-input__wrapper) {
    background: rgba(18, 89, 252, 0.1);
    border: 1px solid #079afd;
    box-shadow: inset 0px 0px 16px 0px rgba(10, 212, 255, 0.34);
  }

  // display: flex;
  // justify-content: center;
  // align-items: center;
  :deep(.el-input) {
    color: #fff;

    .el-input__inner {
      min-height: 36px;
      background-color: rgba(0, 0, 0, 0);
      color: #fff;
    }

    input:-moz-placeholder {
      color: #fff;
    }

    .el-input__suffix-inner {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    input:-ms-input-placeholder {
      color: #fff;
    }

    input::-webkit-input-placeholder {
      color: #fff;
    }
  }
}

.list-box {
  background: rgba(18, 89, 252, 0.1);
  // border: 1px solid #079AFD;
  box-shadow: inset 0px 0px 16px 0px rgba(10, 212, 255, 0.34);
  margin-top: 10px;
  max-height: 500px;
  overflow: auto;

  .item {
    width: 100%;
    max-width: 356px;
    cursor: pointer;
    color: #fff;
    padding: 8px 10px;
    border-top: 1px solid #079afd;

    &:not(.no-text):hover {
      background: rgb(7, 154, 253, 0.75);
    }
  }

  .item:nth-of-type(1) {
    border-top: none;
  }

  .active {
    box-shadow: inset 0px 0px 16px 0px rgba(10, 212, 255, 0.72);
  }

  .no-text {
    color: #eee;
    text-align: center;
    cursor: default;
  }
}

.search-box.search-box-actived {
  width: 360px;
  transition: all 0.5s cubic-bezier(0, 0.105, 0.035, 1.57);

  .search-input {
    flex: 1;
    transition: all 0.5s cubic-bezier(0, 0.105, 0.035, 1.57);
  }

  .search-button {
    color: #fff;
    background-color: #4570f6;
    transition: all 0.3s;
  }
}

.input-box {
  height: 100%;
  width: 100%;
  background-color: red;
  flex: 1;
}

.icon-box {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-color: red;
}
</style>
