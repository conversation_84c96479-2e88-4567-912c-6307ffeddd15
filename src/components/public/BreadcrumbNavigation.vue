<template>
  <div class="breadcrumb-navigation flex items-center justify-between pl-20px">
    <div class="unit-name-box">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          :class="{ 'last-breadcrumb': index === list.length - 1 }"
          v-for="(item, index) in list"
          :key="index"
          :to="item.path"
          >{{ item.name }}</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>

    <div class="flex items-center justify-between">
      <solt></solt>
      <div class="pr-20px" v-show="isShowBack">
        <el-button @click="goBack"> 返回 </el-button>
      </div>
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'breadcrumbNavigation',
}
</script>
<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import { computed } from 'vue'
const router = useRouter()
const route = useRoute()

const isShowBack = computed(() => {
  return route.query.isShowBack ? true : false
})
const list = computed(() => {
  let _navList: any[] = []
  const matched = route.matched
  _navList = matched
    .map((item, i) => {
      const next = (matched[i] || {}).meta || {}
      const { title, isLink, jumpPath } = item.meta
      return {
        name: title as string,
        path: isLink || next.isLink ? (jumpPath ? jumpPath : item.path) : '',
      }
    })
    .filter((item) => !!item.name)
  if (route.query.unitName && route.query.showName) {
    _navList[_navList.length - 1].name = route.query.unitName
  }
  return _navList
})

const goBack = () => {
  if (route.query.isShowBack && route.query.jumpAddress) {
    router.push(route.query.jumpAddress as string)
  } else if (route.query.isShowBack) {
    router.go(-1)
  }
}
</script>

<style lang="scss">
.breadcrumb-navigation {
  height: 50px;
  min-height: 50px;

  .el-breadcrumb {
    font-size: 16px;
    line-height: inherit;

    .el-breadcrumb__item {
      // cursor: pointer;
      .el-breadcrumb__inner {
        color: #666;
        font-weight: 400;
        // &:hover {
        //   color: #333;
        // }
      }

      .el-breadcrumb__inner.is-link:hover,
      .el-breadcrumb__inner a:hover {
        color: #333;
        // font-weight: 700;
      }
    }

    .last-breadcrumb {
      .el-breadcrumb__inner {
        color: #607590;

        &:hover {
          color: #607590;
        }
      }
    }
  }

  .unit-name-box {
    display: flex;
  }

  .unit-name {
    // font-size: 16px;
    // font-weight: 700;
  }
}
</style>
