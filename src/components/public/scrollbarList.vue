<template>
  <div class="alarmInformation h-[calc(100%)]">
    <el-scrollbar
      class="w-full h-full"
      ref="scrollbarRef"
      @scroll="scroll"
      @mouseenter="onMouseenter"
      @mouseleave="onMouseleave"
    >
      <div ref="innerRef">
        <slot></slot>
      </div>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
const max = ref(0)
const scrollbarRef = ref()
const innerRef = ref<HTMLDivElement>()
const scrllValue = ref(0)
const timer = ref()

onMounted(() => {
  console.log(11111)
  nextTick(() => {
    timerOpen()
  })
})

onUnmounted(() => {
  timerClear()
})

const onMouseenter = () => {
  timerClear()
}

const onMouseleave = () => {
  timerOpen()
}
const scroll = ({ scrollTop }) => {
  scrllValue.value = scrollTop
}

const timerOpen = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  if (innerRef.value && scrollbarRef.value) {
    max.value = innerRef.value!.clientHeight - scrollbarRef.value.wrapRef.clientHeight + 30
  }

  if (max.value <= 30) return
  timer.value = setInterval(() => {
    scrllValue.value++
    if (scrllValue.value >= max.value) {
      scrllValue.value = 0
    }
    if (scrollbarRef.value) {
      scrollbarRef.value!.setScrollTop(scrllValue.value)
    }
  }, 80)
}
const timerClear = () => {
  clearInterval(timer.value)
}
</script>

<style scoped lang="scss">
.alarmInformation {
  display: flex;
  flex-direction: column;
}
</style>
