<script lang="ts">
import { h, defineComponent } from 'vue';
import imgSrc from '@/assets/image/empty.png';
export default defineComponent({
  name: 'custom-empty',
  props: {
    labelTitle: {
      type: String,
      default: '暂无数据'
    }
  },
  render() {
    return h(
      'div',
      {
        class: ['text-center', 'custom-empty']
      },
      [
        h(
          'div',
          {
            class: 'custom-empty-page'
          },
          [
            h('img', {
              src: imgSrc,
              class: 'inline-block w-160px mb-10px'
            }),
            h(
              'p',
              {
                class: 'gray'
              },
              this.labelTitle
            )
          ]
        )
      ]
    );
  }
});
</script>

<style scoped lang="scss">
.custom-empty {
  min-height: 110px;

  .custom-empty-page {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .gray {
      // color: var(--el-color-white);
    }
  }
}
</style>
