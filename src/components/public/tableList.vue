<template>
  <div class="w-full h-full table-list flex flex-col">
    <!-- <header v-if="showHeader">
            <el-row :gutter="20">
                <slot name="header"></slot>
            </el-row>
            <div class="right-content">
                <slot name="right"></slot>
            </div>
        </header> -->
    <div
      class="table-list_wrap w-full flex-1 overflow-hidden"
      :class="{ hasTop: !hasTop }"
    >
      <el-table
        :border="border"
        ref="multipleTableRef"
        v-bind="tableProps"
        :data="data"
        v-if="!userDefined"
        :span-method="arraySpanMethod"
        @selection-change="handleSelectionChange"
        highlight-current-row
        :row-key="rowKey"
        :style="{ height: height }"
        @sort-change="sortChange"
      >
        <slot> </slot>
        <template v-slot:empty>
          <div>
            <no-data v-if="emptyType == 1"></no-data>
            <custom-empty
              v-else
              class="h-[calc(100%)] w-full relative"
            ></custom-empty>
          </div>
        </template>
      </el-table>
      <slot v-else></slot>
      <div
        :class="
          pagerCount != 0
            ? 'pagination-wrap-new flex justify-end pb-0px pt-5px pl-10px'
            : 'pagination-wrap flex justify-end pb-0px pt-5px'
        "
        v-if="!noPage"
      >
        <el-pagination
          v-if="pagerCount == 0 && pageModel.total != 0"
          v-model:currentPage="pageModel.pageNo"
          v-model:page-size="pageModel.pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :small="false"
          :disabled="false"
          layout="total, prev, pager, next, sizes"
          :total="pageModel.total"
          @size-change="handleSizeChange"
          @current-change="currentChange"
        />
        <el-pagination
          v-if="pagerCount != 0 && pageModel.total != 0"
          v-model:currentPage="pageModel.pageNo"
          v-model:page-size="pageModel.pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :small="false"
          :disabled="false"
          :pager-count="pagerCount"
          layout="total, prev, pager, next,sizes"
          :total="pageModel.total"
          @size-change="handleSizeChange"
          @current-change="currentChange"
        />
        <!--:background="background" 没有定义background变量控制台报警告-->
      </div>
    </div>
    <!-- <div class="flex justify-center pt-68px pb-0px foot-title">
              ©2022 辰安天泽产品研发部出品 
        </div> -->
  </div>
</template>

<script lang="ts" setup>
import { AnySrvRecord } from 'dns'
import { ref, computed, getCurrentInstance, onMounted } from 'vue'
import noData from '~/components/public/noData.vue'
import { EVENT_TYPE } from '~/common/eventType'
const instance = getCurrentInstance()

const props = defineProps({
  hasTop: {
    // 是否有顶部padding  true-有  false-没有  默认有
    type: Boolean,
    default: true
  },
  pagerCount: {
    type: Number,
    default: 0
  },
  data: {
    type: Array,
    default: () => []
  },
  rowKey: {
    type: String || Function,
    default: ''
  },

  noPage: {
    type: Boolean,
    default: false
  },

  pageModel: {
    type: Object,
    default: () => ({
      pageNo: 1,
      pageSize: 10,
      total: 0
    })
  },

  loading: {
    type: Boolean,
    default: false
  },

  showHeader: {
    type: Boolean,
    default: true
  },

  border: {
    type: Boolean,
    default: true
  },

  tableProps: {
    type: Object,
    default: () => ({})
  },

  userDefined: {
    type: Boolean,
    default: false
  },

  columns: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: ''
  },
  emptyType: {
    type: Number || String,
    default: 1
  }
})

const pageNo = ref<any>(1)
pageNo.value = props.pageModel.pageNo
const pageSize = ref<any>(10)
pageSize.value = props.pageModel.pageSize

const multipleTableRef = ref()

const handleClearSelection = () => {
  multipleTableRef.value!.clearSelection()
}

const emits = defineEmits([
  'current-change',
  'size-change',
  'page-change',
  'handle-selection-change',
  'sort-change',
  'arraySpanMethod'
])
const handleSizeChange = (val: number) => {
  pageSize.value = val
  emits('size-change', val)
  pageChange()
}
function currentChange(val: number) {
  pageNo.value = val
  emits('current-change', val)
  pageChange()
}

function pageChange() {
  emits('page-change', { pageNo: pageNo.value, pageSize: pageSize.value })
}

function handleSelectionChange(val: any) {
  emits('handle-selection-change', val)
}

function sortChange(val: any) {
  emits('sort-change', val)
}

const arraySpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  emits('arraySpanMethod', {
    row,
    column,
    rowIndex,
    columnIndex
  })

  if (row.eventType == EVENT_TYPE.MAYBE_ALARM) {
    if (columnIndex === 0 || column.property == 'action') return [1, 1]
    else {
      if (column.property == 'unitName') {
        return [1, 7]
      } else {
        return [0, 0]
      }
    }
  }
}

defineExpose({
  handleClearSelection
})
</script>

<script lang="ts">
export default {
  name: 'tableList'
}
</script>

<style>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
  margin-bottom: 20px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
</style>
