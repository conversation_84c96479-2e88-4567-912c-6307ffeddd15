<template>
    <svg :style="svgClass">
        <use :xlink:href="symbolId" :fill="color" />
    </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
    prefix: {
      type: String,
      default: 'icon',
    },
    name: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      default: '#b1b1b1',
    },
    size: {
        type: Number,
        default: 32
    },
    height: {
        type: Number,
        defualt: undefined
    },
    width: {
      type: Number,
      default: undefined
    }
})

const symbolId = computed(() => `#${props.prefix}-${props.name}`)

const svgClass = computed(() => {
  const w = props.width,
        h = props.height,
        s = props.size;

  if (w && h) {
    return {
      height: h + 'px',
      width: w + 'px',
      minHeight: h + 'px',
      minWidth: w + 'px'
    }
  }
  return {
    height: s + 'px',
    width: s + 'px',
    minWidth: s + 'px',
    minHeight: s + 'px'
  }
})

</script>

<script lang="ts">
export default {
    name: 'svgIcon'
}
</script>