<template>
  <div class="card-title">
    <p class="title">{{ title }}</p>
    <div class="mb-16px mr-30px">
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
})
</script>
<style lang="scss" scoped>
.card-title {
  margin-top: 10px;
  height: 42px;
  width: 100%;
  background-image: url('@/assets/image/card-title-bg.png');
  background-size: auto 100%;
  background-position: 10px 0;
  background-repeat: no-repeat;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  &::after {
    content: '';
    position: absolute;
    left: 10px;
    bottom: 0;
    width: calc(100% - 10px);
    height: 2px;
    background: linear-gradient(180deg, rgba(83, 136, 241, 0) 1%, rgba(96, 149, 255, 0.6) 100%);
  }
}
.title {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 24px;
  color: #ffffff;
  line-height: 50px;
  text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
  padding-left: 50px;
}
</style>
