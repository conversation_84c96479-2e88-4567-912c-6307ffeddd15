<template>
  <div class="message_panel_wrap flex flex-col h-full" v-clickoutside="close">
    <header>
      <div class="flex items-center pb-20px pt-10px" style="border-bottom: 1px solid #f4f4f4">
        <div class="arrowleftbold flex flex-1 items-center cursor-pointer">
          <span class="ml-5px font-extrabold">消息</span>
        </div>
        <div class="flex flex-6 items-center cursor-pointer">
          <div class="flex-1 items-center cursor-pointer text-14px" @click="allRead" style="color: #3093fa">
            全部已读
          </div>
          <div class="flex-2 ml-10px items-center cursor-pointer" @click="close">
            <svg-icon name="cha" :size="12" color="#FD8595"></svg-icon>
          </div>
        </div>
      </div>
    </header>
    <div class="message-select flex items-center pt-20px pb-20px">
      <el-button size="large">消息类型</el-button>
      <el-select v-model="messageType" @change="selectChange" :teleported="false" class="w-140px">
        <el-option v-for="(item, index) in tabs" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="main flex-1" v-loading="loading">
      <div class="message_list" :infinite-scroll-disabled="noMore" v-infinite-scroll="load">
        <div class="message_item" v-for="item in messageList" :key="item.messageId" @click="messageClick(item)">
          <div class="title_wrap">
            <div class="flex items-center">
              <span class="title"> {{ item.messageTitle }}</span
              ><span v-show="item.isRead === 0" class="message_read"> </span>
            </div>
            <div class="date">{{ item.createTime }}</div>
          </div>
          <div class="content">{{ item.messageContent }}</div>
        </div>
        <p class="text-center text-14px" v-show="messageList.length > 0 && noMore">已加载全部</p>
      </div>
    </div>
    <footer>
      <div class="foot_wrap flex items-center justify-center" @click="router.push('/message')">
        <span class="text-14px">查看全部消息</span>
        <el-icon><icon-arrow-right-bold /></el-icon>
      </div>
    </footer>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ref, reactive, computed, onMounted, inject, Ref, toRaw } from 'vue'
import { PageModel, Message } from '@/types'
import { useUserInfo } from '@/store'
import { ElMessage } from 'element-plus'
import { setTempData } from '@/common/utils'
import { getPagePath } from '@/common/eventType'
import { getMessageList, changeMessageReadState } from '@/common/services'

const showMessage: Ref<boolean> = inject('showMessage')!

const router = useRouter()

const messageList = ref([] as Message[])

const loading = ref(false)

const ui = useUserInfo()

const tabs = ref([
  // {label: '全部', value: ''},
  { label: '火警提醒', value: '1' },
  { label: '预警提醒', value: '2' },
  { label: '故障提醒', value: '3' },
  { label: '动作提醒', value: '5' },
  { label: '主机失联提醒', value: '7' },
])

const messageType = ref('')
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
})

const noMore = computed(() => {
  return pageModel.pageNo * pageModel.pageSize >= pageModel.total
})

async function allRead() {
  loading.value = true
  const isSuccess = await changeMessageReadState([], '2')
  loading.value = false
  if (isSuccess) {
    ElMessage.success('全部已读成功')
    getData()
  }
}

function close() {
  showMessage.value = false
}

function selectChange(val) {
  messageType.value = val
  pageModel.pageNo = 1
  getData()
}

function getData() {
  loading.value = true
  const pm = pageModel
  getMessageList({
    messageType: messageType.value,
    pageNo: pm.pageNo,
    pageSize: pm.pageSize,
    unitId: ui.value.unitId,
    userId: ui.value.userId,
  }).then((res: any) => {
    loading.value = false
    if (pm.pageNo === 1) {
      messageList.value = res.rows
    } else {
      messageList.value = messageList.value.concat(res.rows)
    }
    pageModel.total = res.total
  })
}

async function messageClick(item: Message) {
  const t = item.messageType
  if (item.isRead === 0) {
    const isSuccess = await changeMessageReadState([item.messageId])
    if (isSuccess) item.isRead = 1
  }
  const obj: any = Object.assign({}, toRaw(item))
  obj.tempId = item.businessId
  obj.eventType = item.messageType
  setTempData(obj)
  const path = getPagePath(item)
  router.push(path)
  close()
}

function load() {
  pageModel.pageNo += 1
  getData()
}

onMounted(() => {
  getData()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss" scoped>
.message_panel_wrap {
  .message-select {
    height: 80px;
    .el-button {
      background: #e1e1e1 !important;
      color: #333333 !important;
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
      pointer-events: none;
    }
    .el-select .el-input.is-focus .el-input__inner {
      box-shadow: none;
    }
  }
  .img_wrap {
    position: relative;
  }
  .arrow_icon {
    width: 12px;
    height: 6px;
    cursor: pointer;
  }
  .main {
    // margin-top: 22px;
    // height: 70vh;
    overflow-x: scroll;
  }
  .message_list {
    max-height: 100%;
    // overflow-y: auto;
    .message_item {
      margin-bottom: 20px;
      padding-bottom: 23px;
      padding-right: 10px;
      box-sizing: border-box;
      border-bottom: 1px solid #f2f2f2;
      cursor: pointer;
    }
    .title_wrap {
      display: flex;
      justify-content: space-between;
    }
    .title_left {
      display: flex;
      align-items: center;
    }
    .title {
      color: #000;
    }
    .content {
      margin-top: 20px;
      color: #666;
    }
    .date {
      color: #b3b3b3;
    }
    .message_read {
      width: 6px;
      height: 6px;
      margin-left: 10px;
      border-radius: 50%;
      display: inline-block;
      background: rgba(253, 77, 79, 1);
    }
  }
  footer {
    padding: 20px 0 5px;
    // box-shadow: 1px 1px 2px #000;
    .foot_wrap {
      // width: 126px;
      // height: 33px;
      // margin: 0 auto;
      // line-height: 33px;
      // text-align: center;
      // border-radius: 6px;
      // background-color: #fff;
      // border: 1px solid rgba(24, 144, 255, 1);
      text-align: center;
      font-size: 12px;
      cursor: pointer;
    }
    .foot_txt {
      font-size: 14px;
      color: #1890ff;
    }
  }
}
:deep(.el-tabs__nav-scroll) {
  width: 50% !important;
  margin: 0 auto !important;
}
</style>
