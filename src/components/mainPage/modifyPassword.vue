<template>
  <div class="modify-password">
    <el-form label-width="110px" :model="model" :rules="rules" label-position="left" ref="form">
      <el-form-item label="原密码" prop="oldPwd">
        <el-input
          size="large"
          type="password"
          v-model="model.oldPwd"
          placeholder="请输入原密码"
          show-password
        ></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <el-input
          size="large"
          type="password"
          v-model="model.newPwd"
          placeholder="请输入新密码"
          show-password
        ></el-input>
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirmPwd">
        <el-input
          size="large"
          type="password"
          v-model="model.confirmPwd"
          placeholder="请确认新密码"
          show-password
        ></el-input>
      </el-form-item>
      <el-form-item
        label="密码长度为8-16位，且需同时包含大写字母、小写字母和数字，不允许有空格"
        class="offcial"
      ></el-form-item>
      <div class="flex justify-center">
        <el-button @click="submit" :loading="loading" type="primary">确认</el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { hex_md5 } from '@/common/md5'
import { useUserInfo } from '~/store'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { loginPath } from '@/router/loginPath'

const props = defineProps({
  onClose: {
    type: Function,
    default: () => {},
  },
})

const router = useRouter()

const userInfo = useUserInfo()

const loading = ref(false)

const model = reactive({
  userId: '',
  confirmPwd: '',
  newPwd: '',
  oldPwd: '',
})

const validatePassword = (rule: any, value: any, callback: any) => {
  const regex = /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?!.*\s).{1,}$/
  const msg = new Error('密码需同时包含大写字母、小写字母和数字，不允许有空格')
  if (!regex.test(value)) return callback(msg)
  else callback()
}

const rules = reactive({
  oldPwd: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 8, max: 16, message: '密码长度为8-16位', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' },
  ],
  newPwd: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, max: 16, message: '密码长度为8-16位', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' },
  ],
  confirmPwd: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { min: 8, max: 16, message: '密码长度为8-16位', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' },
  ],
})

const form = ref()

function submit() {
  form.value.validate((valid, fields) => {
    if (valid) {
      const ui = userInfo.value
      const p = Object.assign({}, model, {
        oldPwd: hex_md5(model.oldPwd + 'true'),
        userId: ui._userId,
      })
      loading.value = true
      $API
        .post({
          url: '/userManage/modifyPassword',
          params: p,
        })
        .then((res: any) => {
          loading.value = false
          if (res && res.code === 'success') {
            ElMessage.success(res.message)
            ui.value = undefined as any
            router.push(loginPath.value)
            props.onClose()
          }
        })
    }
  })
}
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.modify-password {
  width: 600px;
  padding: 0 45px;
  .offcial {
    .el-form-item__label {
      width: 100% !important;
      font-size: 14px;
    }
  }
}
</style>
