<template>
  <div class="h-full w-full global-header h-67px flex items-center justify-between">
    <div class="flex items-center">
      <div class="unit-name">
        {{ '大唐（内蒙古）能源储能电站早期风险监测预警系统' }}
        <!-- || ui.orgName  -->
      </div>
      <div v-if="menuList.length > 0" class="nav-menu flex">
        <el-dropdown v-for="menu in menuList" :key="menu.id" class="nav-item" trigger="hover">
          <div class="nav-item-content" :class="{ 'is-active': activeMenu === menu.id }" @click="handleMenuClick(menu)">
            <span>{{ menu.name }}</span>
          </div>
          <template #dropdown v-if="menu.children && menu.children.length > 0">
            <el-dropdown-menu class="nav-dropdown">
              <el-dropdown-item v-for="subMenu in menu.children" :key="subMenu.id" @click="handleSubMenuClick(subMenu)">
                {{ subMenu.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="flex items-center ml-60px">
      <div class="flex w-160px justify-between -mr-24px">
        <!-- 时间在页面内需要自动更新 -->
        <span class="text-[#fff]">
          {{ time }}
          {{ date }}
          <!-- {{ week }} -->
        </span>
      </div>
      <div class="mr-8px message-box cursor-pointer" @click="goMessage">
        <svg-icon name="message" :size="28"></svg-icon>
        <div class="myborder" v-if="!!messageNum">
          {{ messageNum > 99 ? '99+' : messageNum }}
        </div>
      </div>
      <div class="user-info flex items-center cursor-pointer" @click="showModifyPassword = true">
        <div class="avatar">
          <img :src="avatar" alt="" />
        </div>
        <span class="user-name">{{ ui.userName }}</span>
      </div>
      <div class="exit flex items-center cursor-pointer" @click="exit" track>
        <!-- <svg-icon name="exit" :size="14" color="#FD8595"></svg-icon> -->
        <span>退出</span>
      </div>
    </div>
  </div>
  <popup-component v-model="noticeShow">
    <div class="popup-wraps relative">
      <div class="absolute bg-icon"></div>
      <div class="absolute bj"></div>
      <div class="absolute dbj"></div>
      <header class="flex justify-between items-center">
        <div class="flex-1 flex justify-center">
          <div class="noticePopupTitle text-center z-1 text-24px">公告</div>
        </div>
        <div class="h-16px w-16px min-h-16px min-w-16px cursor-pointer z-1 mt-6px" @click.stop="noticeShow = false">
          <img src="../../assets/image/noticePopup/gb.png" alt="" class="h-full w-full" />
        </div>
      </header>
      <div class="content cursor-pointer">
        <div v-html="noticeMain"></div>
      </div>
    </div>
  </popup-component>

  <popup-wrap popup-title="修改密码" v-model="showModifyPassword">
    <modify-password
      :on-close="
        () => {
          showModifyPassword = false
        }
      "
    ></modify-password>
  </popup-wrap>
</template>

<script lang="ts" setup>
import { computed, h, ref, provide, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import defaultAvatar from '@/assets/image/default_avatar.png'
import config from '@/config'
import { ElMessageBox } from 'element-plus'
import $API from '~/common/api'
import modifyPassword from './modifyPassword.vue'
import { useCounterStore, useUserInfo } from '~/store'
import { loginPath } from '@/router/loginPath'

// 菜单项类型定义
interface MenuItem {
  id: string | number
  name: string
  path?: string
  children?: MenuItem[]
  [key: string]: any
}

const route = useRoute()
const time = ref('')
const date = ref('')
const week = ref('')
const weekDays = ['日', '一', '二', '三', '四', '五', '六']
// 菜单数据
const menuList = ref<MenuItem[]>([])
const activeMenu = ref<string | number>('')

// 获取菜单数据
const getMenuList = async () => {
  try {
    // 这里替换为实际的接口地址
    // const res = await $API.get({
    //   url: '/api/menu/list', // 替换为实际的接口地址
    //   params: {
    //     // 可以添加需要的参数
    //   },
    // })

    // 模拟数据，实际使用时替换为接口返回的数据
    menuList.value = [
      {
        id: 'overview',
        name: '态势总览',
        children: [
          { id: 'safety-overview', name: '安全总览一张图', path: '/overview/safetyOverviewMap' },
          { id: 'safety-monitoring', name: '安全态势一张图', path: '/overview/safetyMonitoringMap' },
        ],
      },
      {
        id: 'safety',
        name: '储能安全',
        children: [
          { id: 'safety-battery', name: '安全态势', path: '/energyStorageSafety/securitySituation' },
          { id: 'safety-thermal', name: '综合研判', path: '/energyStorageSafety/assessment' },
          { id: 'safety-thermal', name: '数据分析', path: '/energyStorageSafety/analysis' },
          { id: 'safety-fault', name: '预警判断', path: '/energyStorageSafety/diagnosis' },
          { id: 'safety-report', name: '报告分析', path: '/energyStorageSafety/report' },
          { id: 'safety-capacity', name: '容量标定', path: '/energyStorageSafety/capacity' },
        ],
      },
      {
        id: 'fire',
        name: '消防安全',
        children: [
          { id: 'fire-safety', name: '安全一张图', path: '/fireSafety/fireSafetyMap' },
          { id: 'fire-unitMonitoringList', name: '单位监测列表', path: '/fireSafety/unitMonitoringList' },
          { id: 'equipmentMonitoring', name: '设备实时监测', path: '/fireSafety/equipmentMonitoring' },
        ],
      },
      {
        id: 'configManage',
        name: '配置管理',
        children: [
          // { id: 'config-system', name: '系统设置', path: '/config/system' },
          { id: 'config-eval', name: '评估配置管理', path: '/configManage/evaluationConfig' },
          { id: 'config-user', name: '用户账号管理', path: '/configManage/userAccount' },
          { id: 'config-role', name: '角色权限管理', path: '/configManage/rolePermission' },
          { id: 'config-log', name: '操作日志管理', path: '/configManage/operationLog' },
        ],
      },
    ]

    // 设置当前激活的菜单
    setActiveMenu()
  } catch (error) {
    console.error('获取菜单失败:', error)
  }
}

// 设置当前激活的菜单
const setActiveMenu = () => {
  const currentPath = route.path
  menuList.value.some((menu) => {
    if (menu.children) {
      const activeChild = menu.children.find((item) => item.path === currentPath)
      if (activeChild) {
        activeMenu.value = menu.id
        return true
      }
    }
    return false
  })
}

// 菜单项点击事件
const handleMenuClick = (menu: MenuItem) => {
  if (menu.path) {
    router.push(menu.path)
  } else if (menu.children && menu.children.length > 0) {
    // 如果没有子菜单的点击事件，可以在这里处理
    activeMenu.value = menu.id
  }
}

// 子菜单项点击事件
const handleSubMenuClick = (subMenu: MenuItem) => {
  if (subMenu.path) {
    router.push(subMenu.path)
    // 更新激活状态
    menuList.value.some((menu) => {
      if (menu.children?.some((item) => item.id === subMenu.id)) {
        activeMenu.value = menu.id
        return true
      }
      return false
    })
  }
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    setActiveMenu()
  }
)

// 组件挂载时获取菜单数据
onMounted(() => {
  getMenuList()
})

const counterStore = useCounterStore()
const messageNum: any = computed(() => {
  return counterStore.messageNum
})

const showPopover = ref(false)

provide('showMessage', showPopover)

const ui = useUserInfo()

const router = useRouter()

// const message = useMessage();

const noticeShow = ref(false)

const noticeMain = ref('')

const avatar = computed(() => {
  const photoUrl = ui.value.photoUrl
  if (photoUrl) {
    return config.image_url + photoUrl
  }
  return defaultAvatar
})
const goMessage = () => {
  // counterStore.messageNum = 0
  router.push({
    path: '/help/message',
  })
}
onMounted(() => {
  // getNotReadNum()
  setInterval(() => {
    time.value = new Date().toLocaleTimeString()
    date.value = new Date().toLocaleDateString().replace(/\//g, '-')
    week.value = `星期${weekDays[new Date().getDay()]}`
  }, 1000)
})

const getNotReadNum = () => {
  // 获取未读消息数
  const val = ui.value
  $API
    .post({
      method: 'post',
      url: 'message/getNotReadNum',
      params: {
        superviseId: val.unitId,
        userId: val.userId,
      },
    })
    .then((res: any) => {
      counterStore.messageNum = res.data || ''
    })
}

function exit() {
  const val = ui.value
  ElMessageBox({
    title: '提示',
    message: h('span', null, '确认退出登录吗?'),
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        $API
          .post({
            method: 'post',
            url: '/login/logOut',
            params: {
              client: 'WEB',
              orgCode: val.unitId,
              userId: val.userId,
              userToken: val.userToken,
            },
          })
          .then((res: any) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (res && res.code === 'success') {
              ui.value = undefined as any
              sessionStorage.clear()
              // ElMessage.success('退出成功')
              done()
              router.push(loginPath.value)
            }
          })
      } else {
        done()
      }
    },
  })
}

const showModifyPassword = ref(false)
</script>

<style lang="scss">
.global-header {
  background-image: url('@/assets/image/layout/headerBg.png');
  background-size: 100% 100%;
  height: 67px;
  background-repeat: no-repeat;
  width: 100%;
  box-sizing: border-box;

  .unit-name {
    height: 42px;
    margin-left: 24px;
    text-align: center;
    font-family: YouSheBiaoTiHei, sans-serif;
    font-weight: 400;
    font-size: 32px;
    color: #ffffff;
    line-height: 42px;
    text-shadow: 0px 4px 9px rgba(6, 118, 165, 0.75);
    text-align: left;
  }
  .nav-menu {
    position: absolute;
    left: 796px;
    top: 12px;
    .nav-item {
      height: 36px;
      .nav-item-content {
        height: 36px;
        width: 174px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        outline: none; /* 移除焦点轮廓 */
        -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
        background: url('@/assets/image/layout/menu.png') no-repeat center / 100% 100%;
        transition: all 0.3s;

        &:hover,
        &.is-active {
          background: url('@/assets/image/layout/menuActive.png') no-repeat center / 100% 100%;

          span {
            color: #fff;
          }
        }
        span {
          width: 74px;
          height: 26px;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 20px;
          color: #b7c6e4;
          line-height: 26px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          transition: color 0.3s;
        }
      }
      &:last-child {
        margin-right: 0;
      }
    }
    .nav-dropdown {
      background: rgba(0, 48, 80, 0.9);
      border-radius: 4px;
      margin-top: 0;
      .el-dropdown-menu__item {
        color: #fff;
        font-size: 14px;
        padding: 0 20px;
        line-height: 36px;
        &:hover,
        &:focus {
          background-color: rgba(0, 149, 255, 0.3);
          color: #fff;
        }
      }
      .popper__arrow {
        display: none;
      }
    }
  }

  svg {
    margin-right: 5px;
  }

  .message-badge {
    .el-badge__content {
      right: 22px;
    }
  }

  .notice {
    height: 36px;
    // line-height: 36px;
    padding: 0 20px;
    width: 50%;
    border-radius: 19px;
    background: #e7f4ff;
    box-sizing: border-box;
    width: 500px;
  }

  .exit {
    border-radius: 12px;
    padding: 0px 10px;
    height: 30px;
    color: #fd8595;
  }

  .message-box {
    position: relative;

    .myborder {
      position: absolute;
      right: -0px;
      top: -5px;
      width: 25px;
      height: 20px;
      border-radius: 100%;
      background: #f00;
      color: #ffffff;
      text-align: center;
      font-size: 12px;
      line-height: 20px;
    }
  }

  .user-info {
    margin-right: 20px;

    .user-name {
      color: #1890ff;
    }

    .avatar {
      width: 30px;
      height: 30px;
      // padding: 4px;
      // border: 1px solid var(--el-border-color);
      border-radius: 8px;
      margin-right: 5px;
      display: inline-block;

      img {
        border-radius: 30px;
        width: 100%;
        height: 100%;
      }
    }
  }
}

.message_popover {
  width: 400px !important;
}
</style>
