<template>
  <div class="flex h-full w-full side-menu flex-col items-center overflow-hidden">
    <div
      v-if="systemName && systemName.trim().length > 0"
      class="flex items-center logo-info cursor-pointer"
      @click="router.push(menuList[0].resType == '0' ? `${menuList[0].children[0].resUrl}` : menuList[0].resUrl)"
    >
      <img :src="logo" class="h-28px" alt="" />
      <div class="ml-10px" v-if="!isCollapse">
        <p class="title">{{ systemName }}</p>
      </div>
    </div>

    <div
      v-else
      class="flex items-center logo-info2 cursor-pointer"
      @click="
        router.push(
          menuList[0].resType == '0'
            ? menuList[0].children.length != 0
              ? menuList[0].children[0].resUrl
              : menuList[0].resUrl
            : menuList[0].resUrl
        )
      "
    >
      <img :src="logo" class="" alt="" />
    </div>

    <div class="menu-wrap w-full" :class="isCollapse ? 'collapse' : 'notCollapse'">
      <el-menu :collapse="isCollapse" unique-opened ref="menu">
        <side-menu-item v-for="item in menuList" :menu-data="item" :key="item.id"></side-menu-item>
      </el-menu>
    </div>
    <div
      class="toggle"
      :style="{
        bottom: '20px',
        left: counterStore.isCollapse ? '10px' : '20px',
      }"
      @click="setCollapse"
    >
      <svg-icon v-if="isCollapse" name="expand_1" :size="14" color="#ffffff"></svg-icon>
      <svg-icon v-else name="fold_1" :size="14" color="#ffffff"></svg-icon>
      <!-- <el-icon @click="setCollapse">
        <icon-expand v-if="isCollapse" />
        <icon-fold v-else />
      </el-icon> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRaw, ref, watch, onMounted, computed, nextTick } from 'vue'
import { useUserInfo, useCounterStore } from '~/store'
import { Resource } from '@/types'
import { useRouter, useRoute } from 'vue-router'
import { MenuProvider } from 'element-plus'
import { useProvideMenu } from './useMenuContext'
import sideMenuItem from './menuItem.vue'
import logo_img from '~/assets/image/sideMenu/sys_logo.png'
import config from '@/config'

interface MenuMap {
  [key: string]: Resource
}

const counterStore = useCounterStore()
const userInfo = useUserInfo()

const router = useRouter()

const route = useRoute()

const menu = ref()

// const isCollapse = ref(false);
const menuIdMap: MenuMap = {}

const menuPathMap: MenuMap = {}

let menuList = ref([] as Resource[])

let rootMenu: MenuProvider | null = null

const logo = computed(() => {
  const logo_url = userInfo.value.systemLogo
  if (logo_url) {
    return config.image_url + logo_url
  }

  return logo_img
})

// const systemName = computed(() => userInfo.value.systemName || '智慧消防系统')
const systemName = computed(() => userInfo.value.systemName)
const isCollapse = computed(() => counterStore.isCollapse)
watch(route, handleRouteChange)
// watch(route, has)

useProvideMenu({
  handlMenuItemClick: (item) => {
    router
      .push({
        path: item.resUrl,
      })
      .catch((err) => {
        window.location.reload()
      })
  },

  setRootMenu(menu) {
    rootMenu = menu
  },
})

// function menuToMap(mList: Resource[]) {
//     mList.forEach(item => {
//         menuIdMap[item.id] = item;
//         menuPathMap[item.resUrl] = item;
//         if (!item.children) item.children = [];
//         item.children.length > 0 && menuToMap(item.children);
//     })
// }
function setCollapse() {
  counterStore.isCollapse = !counterStore.isCollapse
}
let menuIdArr = [] as any[]
function menuToMap(mList: Resource[]) {
  mList.forEach((item: Resource) => {
    menuIdArr.push(item)
    menuIdMap[item.id] = item
    menuPathMap[item.resUrl] = item
    if (!item.children) item.children = []
    item.children.length > 0 && menuToMap(item.children)
  })
  sessionStorage.setItem('menuIdArr', JSON.stringify(menuIdArr))
  sessionStorage.setItem('menuIdMap', JSON.stringify(menuIdMap))
  sessionStorage.setItem('menuPathMap', JSON.stringify(menuPathMap))
}

function handleRouteChange() {
  const { path } = route
  const paths = path.split('/')
  // parentPathName 父级路由名称不能传错,否则查询不到
  const { isChildComponents, parentPathName } = route.meta

  while (paths.length > 0) {
    const item = menuPathMap[paths.join('/')]

    if (item) {
      rootMenu!.activeIndex = item.id
      if (isChildComponents && parentPathName) {
        const curItem: any = item.children.find((child) => {
          return child.resUrl.includes(parentPathName as string)
        })
        if (curItem) rootMenu!.activeIndex = curItem.id
      }
      break
    } else {
      paths.pop()
    }
  }
}

const NINE_MENU_ID = '4da5c689986a4ca9b7fe196d1b289c04'

onMounted(async () => {
  const ui = userInfo.value
  //sta 如果使用动态路由请放开下面代码
  const resourceList = Object.assign([], toRaw(ui.resourceList))
  //end

  //如果静态路由请放开下面代码
  //  const resourceList = Object.assign([], toRaw(menuData));
  //静态路由
  const index = resourceList.findIndex((item: any) => item.id === NINE_MENU_ID)
  const item: Resource = resourceList[index]
  if (item) {
    // item.resName = t('val.nineMenuName')
  }
  if (ui.hasSupervise !== '1') {
    if (index !== -1) resourceList.splice(index, 1)
  }

  menuList.value = resourceList
  // menuList.value = menuData;

  menuToMap(menuList.value)
  await nextTick()
  handleRouteChange()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
$menuHeight: auto;

.side-menu {
  // background: #4474f4;
  background: #2d5698;
  position: relative;

  .toggle {
    color: #fff;
    position: absolute;
    transform: translate(0%, -50%);
    transition: all 0.5s;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .el-icon {
      width: 30px;
      height: 30px;

      svg {
        width: 100%;
        height: 100%;
      }
    }
  }

  .logo-info {
    height: 80px;
    box-sizing: border-box;
    color: white;

    .title {
      font-size: 20px;
    }
  }

  .logo-info2 {
    width: 100%;
    height: 80px;
    padding: 0 20px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .menu-wrap {
    width: 100%;
    position: relative;
    // height: calc(100% - 140px);
    overflow-x: scroll;
    height: 100%;

    &::-webkit-scrollbar {
      display: none;
    }

    .el-menu {
      background: #2d5698;
      border: none;

      .el-sub-menu__title,
      .el-menu-item {
        color: white;
        opacity: 0.8;
        height: $menuHeight;

        &:hover {
          background: #11397b;
          opacity: 1;
        }
      }

      .el-sub-menu .el-menu-item {
        height: $menuHeight;
      }

      .el-menu-item.is-active {
        color: #fff;
        background: #0081ff !important;
        opacity: 1;
      }
    }
  }

  .notCollapse {
    min-width: 300px;

    .el-icon.el-sub-menu__icon-arrow {
      right: 30px;
    }
  }

  .notCollapse {
    min-width: 208px;

    .el-icon.el-sub-menu__icon-arrow {
      right: 30px;
    }
  }

  .el-menu--collapse {
    width: 100%;

    .mue-btn {
      text-align: center;
      display: flex;
      padding: 0 10px !important;
    }
  }

  .collapse {
    // min-width: unset;
    // width: 40px;

    .el-sub-menu__title {
      text-align: center;
      display: flex;
      padding: 0 11px !important;
    }
  }
}
</style>
