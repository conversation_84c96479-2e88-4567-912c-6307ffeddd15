import { Injection<PERSON>ey, provide, inject } from 'vue'
import { Resource } from '@/types'
import { MenuProvider } from 'element-plus'

interface MenuContextProps {
    handlMenuItemClick: (item: Resource) => void,
    setRootMenu: (menu: MenuProvider) => void
}

const MenuContextKey: InjectionKey<MenuContextProps> = Symbol('menuContextKey');

export function useProvideMenu(props: MenuContextProps) {
    provide(MenuContextKey, props);
}

export function useMenuContext() {
    return inject(MenuContextKey)!;
}

