<template>
  <el-sub-menu :index="md.id" v-if="hasChild" :class="{ 'child-menu-bg': !isTopLevel }">
    <template #title>
      <svg-icon v-if="md.resIcon" :color="svnColor" :name="md.resIcon" :size="16"></svg-icon>
      <span class="ml-10px mr-20px" :style="fontSize">{{ md.resName }}</span>
    </template>
    <side-menu-item v-for="item in md.children" :key="item.id" :menu-data="item"></side-menu-item>
  </el-sub-menu>
  <el-menu-item
    :index="md.id"
    @click="handleClick"
    :class="{ 'child-menu-bg': !isTopLevel, 'mue-btn': md.parentId == -1 }"
    v-else
  >
    <svg-icon class="relative" style="z-index: 10" v-if="md.resIcon" :color="svnColor" :name="md.resIcon" :size="16">
    </svg-icon>
    <span class="ml-10px relative" :style="fontSize">{{ md.resName }}</span>
  </el-menu-item>
</template>

<script lang="ts" setup>
import { defineComponent, h, Fragment, computed, inject } from 'vue'
import { Resource } from '@/types'
import { ElSubMenu, ElMenuItem, MenuProvider } from 'element-plus'
import svgIcon from '~/components/public/svgIcon.vue'
import { useMenuContext } from './useMenuContext'

const props = defineProps({
  menuData: {
    type: Object,
    default: () => ({}),
  },
})

const rootMenu: MenuProvider = inject('rootMenu')!

const menuContext = useMenuContext()

const md = computed(() => props.menuData)

const hasChild = computed(() => {
  // 菜单的 resType 等于 1 2 时不显示子集
  return md.value.children.length > 0 && !['1', '2'].includes(md.value.resType)
})

const svnColor = computed(() => {
  return rootMenu.activeIndex === md.value.id ? '#3093FA' : 'white'
})

const isTopLevel = computed(() => !!md.value.resIcon)

const fontSize = computed(() => ({
  // fontSize: getRem(isTopLevel.value ? 16 : 15) + 'rem'
}))

menuContext.setRootMenu(rootMenu)

function handleClick() {
  sessionStorage.removeItem('tabsPaneIndex')
  sessionStorage.removeItem('editableTabsValue')
  menuContext.handlMenuItemClick(md.value as any)
}
</script>

<script lang="ts">
export default {
  name: 'sideMenuItem',
}
</script>

<style lang="scss">
.child-menu-bg {
  // background: #2D8AEA !important;
}
</style>
