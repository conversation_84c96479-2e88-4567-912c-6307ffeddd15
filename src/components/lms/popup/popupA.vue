<template>
  <div class="Popup p-[12px]">
    <div class="text-[16px] text-[#fff] mb-[12px]">
      {{ data.unitName }}
    </div>
    <div class="text-[16px] flex mb-[12px]">
      <div class="flex-shrink-0 flex items-center mr-[15px]">消防安全</div>
      <div class="flex items-center gap-10px">
        <div class="text fire">火警</div>
        <div class="text wragin">预警</div>
        <div class="text fire">故障</div>
        <div class="text wragin">隐患</div>
      </div>
    </div>
    <div class="text-[16px] flex items-center">
      <div class="flex-shrink-0 flex items-center mr-[15px]">消防安全</div>
      <div class="flex items-center gap-10px">
        <div class="text flex items-center fire">火警</div>
        <div class="text flex items-center wragin">预警</div>
        <div class="text flex items-center fire">故障</div>
        <div class="text flex items-center wragin">隐患水电费（12）</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
const props = defineProps<{
  data: any
}>()
const emits = defineEmits(['close'])
const buildInfo = computed(() => {
  return props.data
})
const close = () => {
  emits('close')
}
defineOptions({
  name: 'buildPopup'
})
</script>
<style lang="scss" scoped>
.Popup {
  background-image: url(@/assets/image/lms/popupA.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .text {
    padding: 4px 8px;
    border-radius: 2px 2px 2px 2px;
    font-size: 16px;
    line-height: 16px;
  }
  .fire {
    background: rgba(255, 0, 0, 0.17);
    border: 1px solid #ff0000;
  }
  .wragin {
    background: rgba(200, 0, 255, 0.17);
    border: 1px solid #c800ff;
  }
}
</style>
