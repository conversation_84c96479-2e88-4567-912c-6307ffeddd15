<template>
  <div class="Popup p-[12px]">
    <div class="flex">
      <div>
        <div class="text-[16px] text-[#fff] mb-[12px] min-w-220px">
          {{ data.unitName }}
        </div>
        <div class="mb-[12px]">设备设施：24</div>
        <div class="flex mb-[12px]">
          <div
            class="pb-[3px] border-b border-[rgba(255, 255, 255, 1)] cursor-pointer"
            @click="showNkt"
          >
            查看设备设施点位图 >
          </div>
        </div>
      </div>

      <div class="flex-shrink-0 mt-[12px] ml-[20px]">
        <div class="h-60px w-60px bg-[#fff000] rounded-[100%]"></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
const props = defineProps<{
  data: any
}>()
const emits = defineEmits(['close', 'popupClick'])
const showNkt = () => {
  emits('popupClick', props.data)
}
const buildInfo = computed(() => {
  return props.data
})
const close = () => {
  emits('close')
}
defineOptions({
  name: 'buildPopup'
})
</script>
<style lang="scss" scoped>
.Popup {
  background-image: url(@/assets/image/lms/popupA.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
