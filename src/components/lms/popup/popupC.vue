<template>
  <div class="Popup wragin px-[12px] py-[5px]">
    <div class="flex items-center">
      <div>
        <div class="text-[16px] text-[#fff]">
          {{ data.unitName }}
        </div>
      </div>
      <div class="flex-shrink-0 ml-[20px]">
        <div class="h-45px w-45px smallBg flex items-center justify-center">
          <span class="text-[20px]"> 97 </span>
          <span class="text-[14px]">分</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
const props = defineProps<{
  data: any
}>()
const emits = defineEmits(['close'])
const buildInfo = computed(() => {
  return props.data
})
const close = () => {
  emits('close')
}
defineOptions({
  name: 'buildPopup'
})
</script>
<style lang="scss" scoped>
.fire {
  background: linear-gradient(
    180deg,
    rgba(176, 0, 0, 0.65) 0%,
    rgba(64, 6, 6, 0.65) 100%
  );
  border: 1px solid #ff2626;
  .smallBg {
    background-image: url(@/assets/image/lms/fire-small.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
.wragin {
  background: linear-gradient(
    180deg,
    rgba(176, 147, 0, 0.65) 0%,
    rgba(64, 46, 6, 0.65) 100%
  );
  border: 1px solid #ffcc26;
  .smallBg {
    background-image: url(@/assets/image/lms/wragin-small.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
.normal {
  background: linear-gradient(
    180deg,
    rgba(0, 176, 109, 0.65) 0%,
    rgba(6, 64, 36, 0.65) 100%
  );
  border: 1px solid #26ff7d;
  .smallBg {
    background-image: url(@/assets/image/lms/normal-small.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
</style>
