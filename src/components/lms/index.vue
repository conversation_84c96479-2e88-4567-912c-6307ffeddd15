<template>
  <div class="w-full h-full">
    <div ref="Elmap" class="w-full h-full relative bg-[#f0f]"></div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, ref, watch } from 'vue'
import { onMounted } from 'vue'
import $API from '~/common/api'

// 导入整个库
import * as gisLib from '@/utils/index.es.js'
console.log('🚀 ~ gisLib:', gisLib)
// 使用多个组件
// import Popup from './popup.vue'
import popupA from './popup/popupA.vue'
import popupB from './popup/popupB.vue'
import popupC from './popup/popupC.vue'

const { getGisMap } = gisLib.lmsDisplay
getGisMap().setOption({
  baseMap: CONST_GeoData_China_15,
  baseMap_line: CONST_GeoData_China_line_15,
  userMap: CONST_GeoData_Item_15,
  userMap_inline: CONST_GeoData_Item_inline_15,
  userMap_outline: CONST_GeoData_Item_outline_15
})

interface Props {
  pointer: { x: number; y: number; [key: string]: any }[]
}
const props = defineProps<Props>()
const _pointer = computed(() => {
  return props.pointer || []
})

const iconList = ref([
  {
    name: '0',
    src: new URL('@/assets/image/lms/pointer.png', import.meta.url).href,
    size: [50, 115]
  }
])

const pointers = ref([
  {
    ownerType: 1,
    notes: '',
    unitServiceEndDate: '',
    city: '340100',
    latitude: 3715022.519373,
    county: '340104',
    houseNumber: '上湖名院1#403室',
    subCenterName: '合肥市独居老人监测民生实事服务项目',
    ownerId: '6745939158a858640bdf211b',
    contactsName: '俞自然',
    serverState: 1,
    ownerName: '经开区俞自然',
    province: '340000',
    cityName: '合肥市',
    unitServiceStartDate: '',
    unitId: '',
    usageType: '10',
    longitude: 1.3048142788741e7,
    countyName: '蜀山区',
    address: '安徽省合肥市蜀山区',
    unitName: '俞自然',
    subCenterCode: '340104YYZX1862010297393348608',
    contactsPhone: '13865999391',
    createTime: '2024-11-26 14:35:16',
    provinceName: '安徽省',
    projectId: '68C0CC92384A43E1A6CD56B594828670',
    status: 0,
    eventType: '0',
    priorityEventType: '2'
  },
  {
    ownerType: 1,
    notes: '',
    unitServiceEndDate: '',
    city: '340100',
    latitude: 3760482.187799,
    county: '340121',
    houseNumber: '刘兴村杨圩组',
    subCenterName: '合肥市独居老人监测民生实事服务项目',
    ownerId: '5e3b0ea8a10575669998e1fe6c4d7b0d',
    contactsName: '杨领开',
    serverState: 1,
    ownerName: '长丰县杨领开',
    province: '340000',
    cityName: '合肥市',
    unitServiceStartDate: '',
    unitId: '',
    usageType: '10',
    longitude: 1.3062236082486e7,
    countyName: '长丰县',
    address: '安徽省合肥市长丰县',
    unitName: '',
    subCenterCode: '340104YYZX1862010297393348608',
    contactsPhone: '15255112559',
    createTime: '2024-11-26 14:35:16',
    provinceName: '安徽省',
    projectId: 'B9EE0CD041B44F6FA7341C455EBDF32B',
    status: 0,
    eventType: '0',
    priorityEventType: '1'
  },
  {
    ownerType: 1,
    notes: '',
    unitServiceEndDate: '',
    city: '340100',
    latitude: 3715022.519373,
    county: '340104',
    houseNumber: '上湖名院1#403室',
    subCenterName: '合肥市独居老人监测民生实事服务项目',
    ownerId: '6745939158a858640bdf211b',
    contactsName: '俞自然',
    serverState: 1,
    ownerName: '经开区俞自然',
    province: '340000',
    cityName: '合肥市',
    unitServiceStartDate: '',
    unitId: '',
    usageType: '10',
    longitude: 1.3048142788741e7,
    countyName: '蜀山区',
    address: '安徽省合肥市蜀山区',
    unitName: '',
    subCenterCode: '340104YYZX1862010297393348608',
    contactsPhone: '13865999391',
    createTime: '2024-11-26 14:35:16',
    provinceName: '安徽省',
    projectId: '68C0CC92384A43E1A6CD56B594828670',
    status: 0,
    eventType: '0',
    priorityEventType: '2'
  },
  {
    ownerType: 1,
    notes: '',
    unitServiceEndDate: '',
    city: '340100',
    latitude: 3714518.910822,
    county: '340104',
    houseNumber: '月半湾商住楼265室',
    subCenterName: '合肥市独居老人监测民生实事服务项目',
    ownerId: '6745939158a858640bdf212a',
    contactsName: '汤秀珍',
    serverState: 1,
    ownerName: '经开区汤秀珍',
    province: '340000',
    cityName: '合肥市',
    unitServiceStartDate: '',
    unitId: '',
    usageType: '10',
    longitude: 1.3048284117617e7,
    countyName: '蜀山区',
    address: '安徽省合肥市蜀山区',
    unitName: '',
    subCenterCode: '340104YYZX1862010297393348608',
    contactsPhone: '13965083269',
    createTime: '2024-11-26 14:35:16',
    provinceName: '安徽省',
    projectId: '68C0CC92384A43E1A6CD56B594828670',
    status: 0,
    eventType: '0'
  },
  {
    ownerType: 1,
    notes: '',
    unitServiceEndDate: '',
    city: '340100',
    latitude: 3714228.954372,
    county: '340104',
    houseNumber: '省委宣传9#503',
    subCenterName: '合肥市独居老人监测民生实事服务项目',
    ownerId: '6745939158a858640bdf212f',
    contactsName: '张修琼',
    serverState: 1,
    ownerName: '经开区张修琼',
    province: '340000',
    cityName: '合肥市',
    unitServiceStartDate: '',
    unitId: '',
    usageType: '10',
    longitude: 1.3048550187537e7,
    countyName: '蜀山区',
    address: '安徽省合肥市蜀山区',
    unitName: '',
    subCenterCode: '340104YYZX1862010297393348608',
    contactsPhone: '18256970579',
    createTime: '2024-11-26 14:35:16',
    provinceName: '安徽省',
    projectId: '68C0CC92384A43E1A6CD56B594828670',
    status: 0,
    eventType: '0'
  }
])

const Elmap = ref()
let layer = null
onMounted(async () => {
  nextTick(async () => {
    // let pointerData = pointers.value.map((i) => {
    //   const { longitude: x, latitude: y } = i

    //   const isChinaPointer =
    //     GISShare.SMap.Fitting.CHINA.OutOfChinaExtent_WebMercator_BD09MC(x, y)
    //   if (isChinaPointer)
    //     return {
    //       ...i
    //     }
    //   const _pointer = IndoorMap.projectXY_FromTo(
    //     [x, y],
    //     CONST_SRID_BD09MC,
    //     CONST_SRID_WebMC
    //   )
    //   return {
    //     ...i,
    //     x: _pointer[0],
    //     y: _pointer[1]
    //   }
    // })
    await getGisMap().mount(Elmap.value)
    const res = await $API.post({
      url: '/safetyOverview/getMapScatterPoints',
      params: {
        superviseId: '1924393413690953729'
      }
    })
    let pointerData = res.data.map((i) => {
      return {
        ...i,
        x: Number(i.longitude),
        y: Number(i.latitude)
      }
    })
    console.log('🚀 ~ res:', pointerData)

    getGisMap().creatIcon(iconList.value)
    getGisMap().creatLayer('ceshi')
    getGisMap().addPointersTolaye('ceshi', pointerData)
    getGisMap().onMouseClick = (data) => {
      console.log('🚀 ~ data:', data)
      if (data && data.length > 0) {
        let _data = data[0]?.object?.gsData
        console.log('🚀 ~ _data:', _data)
        getGisMap().addPointerPopup(
          _data,
          popupC,
          {},
          {
            offset: [0, -115]
          }
        )
      }
    }
  })
})
</script>
