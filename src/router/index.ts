/*
 * @Author: “sunheng” “<EMAIL>”
 * @Date: 2025-03-05 10:12:23
 * @LastEditors: “sunheng” “<EMAIL>”
 * @LastEditTime: 2025-04-17 10:23:40
 * @FilePath: \supervise-clnt-platform-web\src\router\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createWebHashHistory, createRouter } from 'vue-router'
import { useUserInfo } from '@/store'
import { isEmpty } from '@/utils'
import { getRoutes } from './routes'
import config from '@/config'
import { cnceAllSignal } from '@/hooks/controller/cancel'
import { loginPath } from '@/router/loginPath'

const routes = getRoutes()

const router = createRouter({
  history: createWebHashHistory(),
  // routes: getRoutes()
  routes: routes,
})

const isHasRouter = (path: string) => {
  const defArr = [
    {
      resUrl: '/login',
    },
    {
      resUrl: '/login-page',
    },
    {
      resUrl: '/autoLogin',
    },
    {
      resUrl: '/login-view',
    },
  ]
  let menuIdArr: any[] = []
  try {
    menuIdArr = sessionStorage.getItem('menuIdArr') ? JSON.parse(sessionStorage.getItem('menuIdArr') || '[]') : []
  } catch (e) {
    //TODO handle the exception
    menuIdArr = []
  }
  return [...defArr, ...menuIdArr].find((i) => i.resUrl === path)
}
console.log('🚀 ~ isHasRouter ~ isHasRouter:', isHasRouter)

const trackPageView = (to) => {
  const _frosua = (window as any).frosua
  if (_frosua && _frosua.trackPageView) {
    // console.log('trackPageView----', to, _frosua, _frosua.trackPageView)

    _frosua.trackPageView({
      pageTitle: to.meta.title || to.meta.pageTitle, // 页面标题
      resourceNo: '', // (选填) 页面在应用管理中心中的资源编码
      pageUrl: to.path, // (选填) 页面url, 不传的话会去浏览器地址栏中的url，如果页面切换不改变浏览器地址栏，需要传该参数
    })
  }
}

router.beforeEach(function (to, from, next) {
  if (to.path.includes('login')) {
    sessionStorage.setItem('loginPath', to.path)
    loginPath.value = to.path
  }

  // 创建新的CancelToken用于新的请求
  cnceAllSignal()

  const ui = useUserInfo()
  if (config.isOpenFrosua) {
    trackPageView(to)
  }
  if (
    isEmpty(ui.value) &&
    to.path !== '/login' &&
    to.path !== '/login-page' &&
    to.path !== '/autoLogin' &&
    to.path !== '/login-view'
  ) {
    return next(loginPath.value)
  } else {
    // const hasRouter = isHasRouter(to.path)
    // if (!hasRouter) return next(loginPath.value)
    next()
  }
})

if (Object.is(import.meta.env.MODE, 'development')) {
  ;(window as any).router = router
}

export default router
