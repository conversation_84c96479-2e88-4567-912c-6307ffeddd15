import { RouteRecordRaw } from 'vue-router'
import { base_route, base_routes, routes as rs } from './routes'
import { getUserInfo, isWoker, isAdmin, isSafetyManage } from '@/common/utils'
// import dutyPage from '@/views/homePage/dutyPage.vue';
// import principalPage from '@/views/homePage/principalPage.vue';
// import mainPage from '@/views/mainPage.vue';
import defaultLayout from '@/layout/default.vue'
const userInfo = getUserInfo()

const routesMap: Record<string, RouteRecordRaw> = {}

function routesToMap(routes: RouteRecordRaw[], parent?: RouteRecordRaw) {
  routes.forEach((route) => {
    const path = parent?.path
    const key = (path ? path + '/' : '') + route.path
    routesMap[key] = route
    if (route.children) routesToMap(route.children, route)
  })
}

export function getRoutes(): RouteRecordRaw[] {
  const routes: RouteRecordRaw[] = []
  console.log(rs)

  routesToMap(rs)
  if (userInfo) {
    const resList = userInfo.resourceList
    // const homeId = resList[0].id;
    // resList = resList.slice(1);
    const woker = isWoker(userInfo)
    const admin = isAdmin(userInfo)
    const safetyManage = isSafetyManage(userInfo)

    if (!admin || safetyManage || (admin && safetyManage)) {
      routes.unshift({
        path: '/home-page',
        name: 'homePage',
        component: woker ? defaultLayout : '',
        // component: woker ? dutyPage : principalPage,
        meta: {
          isAdmin: !woker,
          id: 'homePage',
        },
      })
    }
    try {
      // 默认跳转的地址
      let cItem
      const arr = ['安全管理一张图', '安全一张图', '设备一张图']
      const item = resList.find((m) => arr.includes(m.resName))
      if (item?.children && item.children.length > 0) {
        cItem = item.children.find((n) => arr.includes(n.resName))
      }
      if (item && cItem) {
        base_route.redirect = cItem.resUrl
      } else {
        base_route.redirect =
          resList[0].resType == '0'
            ? resList[0].children.length != 0
              ? resList[0].children[0].resUrl
              : resList[0].resUrl
            : resList[0].resUrl
      }
    } catch (error) {}
  }
  // base_route.children = routes;
  base_route.children = rs.concat(routes)

  return base_routes
}
