import { RouteRecordRaw } from 'vue-router'
import defaultLayout from '@/layout/default.vue'
import mainPage from '@/views/mainPage.vue'
// import fireSafetySituationAnalysis from '@/views/fireSafetySituation/fireSafetySituationAnalysis/index.vue'
const base_route: RouteRecordRaw = {
  path: '/',
  // redirect: "/home-page",
  component: mainPage,
  children: [],
}

const base_routes: RouteRecordRaw[] = [
  base_route,

  {
    path: '/login',
    component: () => import('@/views/login2.vue'),
  },
  {
    path: '/login-view',
    component: () => import('@/views/login.vue'),
  },
  {
    path: '/login-page',
    component: () => import('@/views/login2.vue'),
  },
  {
    path: '/autoLogin',
    name: 'autoLogin',
    meta: {
      title: 'autoLogin',
    },
    component: () => import('@/views/autoLogin.vue'),
  },
]

const routes: RouteRecordRaw[] = [
  // echarts案例图
  {
    path: '/echartsCase',
    name: 'echartsCase',
    meta: {
      title: 'echartsCase',
    },
    component: () => import('@/views/echartsCase/index.vue'),
  },
  {
    path: '/staging',
    name: 'staging',
    meta: {
      title: '工作台',
      isLink: true,
      jumpPath: '/staging/index',
    },
    component: defaultLayout,
    children: [
      {
        path: 'index',
        name: 'index',
        meta: {
          // title: '工作台'
          pageTitle: '工作台',
        },
        component: () => import('@/views/staging/index.vue'),
      },
    ],
  },
  {
    path: '/overview',
    name: 'overview',
    meta: {
      title: '态势总览',
    },
    component: defaultLayout,
    children: [
      {
        path: 'safetyOverviewMap',
        name: 'safetyOverviewMap',
        meta: {
          title: '安全总览一张图',
          pageTitle: '安全总览一张图',
        },
        component: () => import('@/views/safetyOverviewMap/index.vue'),
      },
      {
        path: 'safetyMonitoringMap',
        name: 'safetyMonitoringMap',
        meta: {
          title: '安全态势一张图',
          pageTitle: '安全态势一张图',
        },
        component: () => import('@/views/safetyMonitoringMap/index.vue'),
      },
    ],
  },
  {
    path: '/help',
    name: 'help',
    meta: {
      title: '帮助中心',
    },
    component: defaultLayout,
    children: [
      {
        path: 'userManual',
        name: 'userManual',
        meta: {
          title: '用户手册',
        },
        component: () => import('@/views/systemManage/userManual/index.vue'),
      },
      {
        path: 'message',
        name: 'message',
        meta: {
          title: '消息中心',
        },
        component: () => import('@/views/systemManage/message/index.vue'),
      },
    ],
  },
  {
    path: '/energyStorageSafety',
    name: 'energyStorageSafety',
    meta: {
      title: '储能安全',
    },
    component: defaultLayout,
    children: [
      {
        path: 'securitySituation',
        name: 'securitySituation',
        meta: {
          title: '安全态势',
        },
        component: () => import('@/views/energyStorageSafety/securitySituation/index.vue'),
      },
      {
        path: 'assessment',
        name: 'assessment',
        meta: {
          title: '综合研判',
        },
        component: defaultLayout,
        children: [
          {
            path: '',
            name: 'assessmentIndex',
            meta: {
              title: '',
            },
            component: () => import('@/views/energyStorageSafety/assessment/index.vue'),
          },
          {
            path: 'battery',
            name: 'battery',
            meta: {
              title: '电池详情',
            },
            component: () => import('@/views/energyStorageSafety/assessment/details.vue'),
          },
        ],
      },
      {
        path: 'analysis',
        name: 'analysis',
        meta: {
          title: '数据分析',
        },
        component: () => import('@/views/energyStorageSafety/analysis/index.vue'),
      },
      {
        path: 'diagnosis',
        name: 'diagnosis',
        meta: {
          title: '预警诊断',
        },
        component: defaultLayout,
        children: [
          {
            path: '',
            name: 'diagnosisIndex',
            meta: {
              title: '',
            },
            component: () => import('@/views/energyStorageSafety/diagnosis/index.vue'),
          },
          {
            path: 'getDiagnosis',
            name: 'getDiagnosis',
            meta: {
              title: '去诊断',
            },
            component: () => import('@/views/energyStorageSafety/diagnosis/getDiagnosis.vue'),
          },
          {
            path: 'seeDiagnosis',
            name: 'seeDiagnosis',
            meta: {
              title: '查看诊断报告',
            },
            component: () => import('@/views/energyStorageSafety/diagnosis/seeDiagnosis.vue'),
          },
        ],
      },
      {
        path: 'report',
        name: 'report',
        meta: {
          title: '报告分析',
        },
        component: () => import('@/views/energyStorageSafety/report/index.vue'),
      },
      {
        path: 'capacity',
        name: 'capacity',
        meta: {
          title: '容量标定',
        },
        component: () => import('@/views/energyStorageSafety/capacity/index.vue'),
      },
    ],
  },
  // 消防安全
  {
    path: '/fireSafety',
    name: 'fireSafety',
    meta: {
      title: '消防安全',
    },
    component: defaultLayout,
    children: [
      // 安全一张图
      {
        path: 'fireSafetyMap',
        name: 'fireSafetyMap',
        meta: {
          title: '安全一张图',
        },
        component: () => import('@/views/fireSafety/fireSafetyMap/index.vue'),
      },
      // 单位监测列表
      {
        path: 'unitMonitoringList',
        name: 'unitMonitoringList',
        meta: {
          title: '单位监测列表',
        },
        component: () => import('@/views/fireSafety/unitMonitoringList/index.vue'),
      },
      {
        path: 'unitMonitoringDetail',
        name: 'unitMonitoringDetail',
        meta: {
          title: '单位监测详情',
        },
        component: () => import('@/views/fireSafety/unitMonitoringDetail/index.vue'),
      },
      // 设备实时监测
      {
        path: 'equipmentMonitoring',
        name: 'equipmentMonitoring',
        meta: {
          title: '设备实时监测',
        },
        component: () => import('@/views/fireSafety/equipmentMonitoring/index.vue'),
      },
      // 消防安全态势
      {
        path: 'fireSafetySituation',
        name: 'fireSafetySituation',
        meta: {
          title: '消防安全态势',
        },
        component: () => import('@/views/fireSafety/equipmentMonitoring/index.vue'),
      },
    ],
  },
  {
    path: '/configManage',
    name: 'configManage',
    meta: {
      title: '配置管理',
    },
    component: defaultLayout,
    children: [
      {
        path: 'evaluationConfig',
        name: 'evaluationConfig',
        meta: {
          title: '评估配置管理',
        },
        component: () => import('@/views/configManage/evaluationConfig/index.vue'),
      },
      {
        path: 'userAccount',
        name: 'userAccount',
        meta: {
          title: '用户账号管理',
        },
        component: () => import('@/views/configManage/userAccount/index.vue'),
      },
      {
        path: 'rolePermission',
        name: 'rolePermission',
        meta: {
          title: '角色权限管理',
        },
        component: () => import('@/views/configManage/rolePermission/index.vue'),
      },
      {
        path: 'operationLog',
        name: 'operationLog',
        meta: {
          title: '操作日志管理',
        },
        component: () => import('@/views/configManage/operationLog/index.vue'),
      },
    ],
  },
]

export { base_route, base_routes, routes }
