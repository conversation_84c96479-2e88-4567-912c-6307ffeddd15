<template>
  <div class="view-wrap w-full h-screen">
    <router-view v-if="isRouterAlive"></router-view>
  </div>
</template>
<script lang="ts" setup>
import mainSearchDomDeal from '@/common/mainSearchDomDeal'
import { nextTick, watch, onMounted, ref, provide } from 'vue'
import { useRoute } from 'vue-router'
import { useUserInfo } from '@/store'
import config from '@/config'
const ui = useUserInfo()
const route = useRoute()
watch(
  () => route,
  () => {
    nextTick(() => {
      mainSearchDomDeal()
    })
  },
  {
    deep: true,
  }
)
watch(ui, () => {
  if (config.isOpenFrosua) {
    setUser()
  }
})
onMounted(() => {
  if (config.isOpenFrosua) {
    // alert(config.isFrosuaPro)
    frosua.init({
      clientId: '55260',
      source: 'web', // 申请接入时由用户行为分析运维人员下发
      sourceType: 0, // 0-Web端，1-APP，2-微信小程序  客户端请选择0 默认值是0 数据来源类型
      isAllTrack: true, // 开启全埋点
      userNo: ui.value.id, // （选填）用户编码
      // userType: userInfo.type, // （选填）用户类型 0-iam用户 1-外部用户 2-其他用户
      userName: ui.value.userName, // (选填)用户名
      orgCode: ui.value.orgCode, // （选填）当前使用的组织机构编码
      orgName: ui.value.orgName, // （选填）当前使用的组织机构名称
      roleNo: ui.value.roleIds, // （选填）当前使用的角色编码
      roleName: ui.value.roleNames, // （选填）当前使用的角色名
      language: 'zh_CN', // （选填）当前使用的语言
      // env: config.isFrosuaPro, // 当前上报数据的环境，不填会上报默认上报生产，填prod也是上报生产，填其他上报测试环境 process.env.VUE_APP_IAM_ENV
      env: 'dev', // 当前上报数据的环境，不填会上报默认上报生产，填prod也是上报生产，填其他上报测试环境 process.env.VUE_APP_IAM_ENV

      getEleName: (el) => {
        let text = el.innerText
        let innerHTML = el.innerHTML
        // (选填) 根据传进来的点击元素el，生成上报时候的事件名称。 上报时候事件名称的优先级规则：uba-n的值 > getEleName的值 > 按钮上的文本 > 按钮的title提示 > 按钮的html
        return text || innerHTML || el
      },
    })
  }
})
const setUser = () => {
  const { id, userName, orgCode, orgName, roleIds, roleNames } = ui.value
  if (!id) return
  frosua.setUser(id, 0, userName)
  frosua.setOrg(orgCode, orgName)
  frosua.setRole(roleIds, roleNames)
}

const isRouterAlive = ref(true)

const reload = () => {
  isRouterAlive.value = false
  //通过nextTick()产生一个微任务,在一次dom事件循环后,重新创建组件
  nextTick(() => {
    isRouterAlive.value = true
  })
}

provide('reload', reload)
</script>
<style lang="scss">
.view-wrap {
  background: #f1f2f6;

  .no-header-dialog {
    &.el-dialog {
      .el-dialog__header {
        display: none;
      }
    }
  }
}
</style>
