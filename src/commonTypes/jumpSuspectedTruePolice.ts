/**
 * 跳转事件处置页面
 * 类型 获取参数 设置参数
 */
import { useRoute, useRouter } from 'vue-router'
import { omit } from 'lodash'

const queryParamKey = 'jumpSuspectedTruePolice'
export interface jumpSuspectedTruePolice {
  /** 监管单位Id */
  superviseId?: string
  /** 监管名称 */
  unitName?: string
  /** 单位id */
  unitId?: string
  /** 时间范围类型 */
  timeType?: string | number
}

export function jumpSuspectedTruePoliceSetParams(payload: jumpSuspectedTruePolice): Record<string, string> {
  try {
    const queryStr = JSON.stringify(payload)
    return { [queryParamKey]: queryStr }
  } catch (error) {
    console.error('🚀路由参数格式错误')
    return {}
  }
}

export function jumpSuspectedTruePoliceUseParams(): [() => jumpSuspectedTruePolice, () => void] {
  const router = useRouter()
  const route = useRoute()

  function jumpSuspectedTruePoliceGetParams(): jumpSuspectedTruePolice {
    try {
      const query = JSON.parse(route.query[queryParamKey] as string)
      return query
    } catch (error) {
      console.error('🚀路由参数格式错误')
      return {} as jumpSuspectedTruePolice
    }
  }

  function jumpSuspectedTruePoliceClearParams() {
    const newQuery = omit(route.query, [queryParamKey])
    router.replace({
      path: route.path,
      query: newQuery,
    })
  }

  return [jumpSuspectedTruePoliceGetParams, jumpSuspectedTruePoliceClearParams]
}
