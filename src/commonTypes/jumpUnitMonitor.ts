/**
 * 跳转事件处置页面
 * 类型 获取参数 设置参数
 */
import { useRoute, useRouter } from 'vue-router'
import { omit } from 'lodash'

const queryParamKey = 'JumpUnitMonitor'
export interface JumpUnitMonitor {
  /** 监管单位Id */
  superviseId?: string
  /** 评估项名称 */
  levelName?: string
  /** 监管名称 */
  unitName?: string
  /** 一张图 */
  isYZT?: boolean
  /** 监管id */
  unitId?: string
  /** 监管id */
  viewType?: string
  timeType?: string | number
}

export function jumpUnitMonitorSetParams(payload: JumpUnitMonitor): Record<string, string> {
  try {
    const queryStr = JSON.stringify(payload)
    return { [queryParamKey]: queryStr }
  } catch (error) {
    console.error('🚀路由参数格式错误')
    return {}
  }
}

export function jumpUnitMonitorUseParams(): [() => JumpUnitMonitor, () => void] {
  const router = useRouter()
  const route = useRoute()

  function jumpUnitMonitorGetParams(): JumpUnitMonitor {
    try {
      const query = JSON.parse(route.query[queryParamKey] as string)
      return query
    } catch (error) {
      console.error('🚀路由参数格式错误')
      return {} as JumpUnitMonitor
    }
  }

  function jumpUnitMonitorClearParams() {
    const newQuery = omit(route.query, [queryParamKey])
    router.replace({
      path: route.path,
      query: newQuery,
    })
  }

  return [jumpUnitMonitorGetParams, jumpUnitMonitorClearParams]
}
