/**
 * 跳转事件处置页面
 * 类型 获取参数 设置参数
 */
import { useRoute, useRouter } from 'vue-router'
import { omit } from 'lodash'

const queryParamKey = 'JumpDataStatisticsQuery'
export interface JumpDataStatisticsQuery {
  /** 监管单位Id */
  superviseId?: string
  /** 处置动作 */
  disposeAction?: string
  /** tab key */
  eventType: string | number
  /** 监测值守核警结果 */
  operationResult?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
  /** 分管单位Id */
  chargeId?: string
  /** 单位Id */
  unitId?: string
  /** 处置结果 */
  disposeResult?: string
  groupByValue?: string
  /** 故障编号 */
  disposeId?: string
  /** 用户id */
  userId?: string
  /** 开始时间 结束时间 */
  trendTime?: [string, string]
  /** 是否超期*/
  isTimeout?: string
  /** 处置状态*/
  fireResult?: string
  /** 当前时间*/
  nowTime?: string
  /** 是否规范*/
  isRegular?: string | number
  /** 处置方式 1已处置 2处置中 0待处置*/
  disposeState?: '0' | '1' | '2'
  /** 隐患问题*/
  problemNo?: string
  /** 平台接收时间-开始时间*/
  firstTimeStart?: string
  /** 平台接收时间-结束时间*/
  firstTimeEnd?: string
  /** //故障来源(0:物联网监测 1:人工上报 2:核警转故障)*/
  faultSource?: '0' | '1' | '2'
  /** 0: 待处置 1: 处置中 2: 已处置*/
  handleState?: '0' | '1' | '2'
  /** 方法*/
  method?: string
  viewType?: string
  fireState?: string
  /** 等级*/
  rank?: string
  /** 设备类型id*/
  deviceTypeId?: string
  /** 离线时间*/
  offlineTimeData?: string
}

export function setPageJsonQueryParamsAdapter(payload: JumpDataStatisticsQuery): Record<string, string> {
  try {
    const queryStr = JSON.stringify(payload)
    return { [queryParamKey]: queryStr }
  } catch (error) {
    console.error('🚀路由参数格式错误')
    return {}
  }
}

export function usePageJsonQueryParams(): [() => JumpDataStatisticsQuery, () => void] {
  const router = useRouter()
  const route = useRoute()

  function getPageJsonQueryParams(): JumpDataStatisticsQuery {
    try {
      const query = JSON.parse(route.query[queryParamKey] as string)
      return query
    } catch (error) {
      console.error('🚀路由参数格式错误')
      return {} as JumpDataStatisticsQuery
    }
  }

  function clearPageJsonQueryParams() {
    const newQuery = omit(route.query, [queryParamKey])
    router.replace({
      path: route.path,
      query: newQuery,
    })
  }

  return [getPageJsonQueryParams, clearPageJsonQueryParams]
}
