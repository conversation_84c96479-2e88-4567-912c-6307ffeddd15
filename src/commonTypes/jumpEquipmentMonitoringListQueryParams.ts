/**
 * 跳转设备监测列表页面
 * 类型 获取参数 设置参数
 */
import { useRoute, useRouter } from 'vue-router'
import { omit } from 'lodash'

const queryParamKey = 'JumpEquipmentMonitoringListQueryParams'
export interface JumpEquipmentMonitoringListQueryParams {
  /** 监管单位Id */
  superviseUnitId?: string
  /** 单位Id */
  unitId?: string
  /** 单位名称 */
  unitName?: string
  /** 单位Id（接口用） */
  ownerId?: string
  /** 设备编号 */
  deviceTypeId?: string
  /** 设备父级编号 */
  deviceTypePid?: string
  /** 设备状态 0在线 1离线 2未激活 */
  onlineState?: '0' | '1' | '2'
  /** 监测状态 0正常监视 1火警 2预警 3故障 4隐患 5动作 */
  monitorState?: '0' | '1' | '2' | '3' | '4' | '5'
  /** IMEI */
  IMEI?: string
}

export function setPageJsonQueryParamsAdapter(payload: JumpEquipmentMonitoringListQueryParams): Record<string, string> {
  try {
    const queryStr = JSON.stringify(payload)
    return { [queryParamKey]: queryStr }
  } catch (error) {
    console.error('🚀路由参数格式错误')
    return {}
  }
}

export function usePageJsonQueryParams(): [() => JumpEquipmentMonitoringListQueryParams, () => void] {
  const router = useRouter()
  const route = useRoute()

  function getPageJsonQueryParams(): JumpEquipmentMonitoringListQueryParams {
    try {
      const query = JSON.parse(route.query[queryParamKey] as string)
      return query
    } catch (error) {
      console.error('🚀路由参数格式错误')
      return {} as JumpEquipmentMonitoringListQueryParams
    }
  }

  function clearPageJsonQueryParams() {
    const newQuery = omit(route.query, [queryParamKey])
    router.replace({
      path: route.path,
      query: newQuery,
    })
  }

  return [getPageJsonQueryParams, clearPageJsonQueryParams]
}
