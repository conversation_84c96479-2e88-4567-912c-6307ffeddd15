/// <reference types="vite/client" />
import { SlateDescendant, SlateElement, SlateText } from '@wangeditor/editor'
declare module '@wangeditor/editor' {
  // 扩展 Text
  interface SlateText {
    text: string
  }

  // 扩展 Element
  interface SlateElement {
    type: string
    children: SlateDescendant[]
  }
}

declare module '@wangeditor/editor-for-vue' {
  const wangeditor: any
  export = wangeditor
}

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
declare module '@/utils'
declare module 'vue-img-cutter'
interface Window {
  router?: any
  SockJS: any
  Stomp: any
  GISShare: any
  IndoorMap: any
  IndoorScene: any
  ol: any
  IndoorService: any
  CONST_OVBuildStyleInfo_Default: any
  CONST_GSCache: GSCache
  CONST_GSUrlHeader: GSUrlHeader
  CONST_GSOptions: GSOptions
  CONST_GSParams: GSParams
  onBuildPopupTop: any
  onBuildPopupClose: any
  CONST_Indoor: any
  CONST_Indoor_Preview: any
  CONST_Indoor_Over: any
  CONST_Indoor_Map: any
  QueryVideoDevice: Function
  QueryAerialViewImg_Def: Function
  QueryFloorAreaImage_Def: Function
  ConfigureScene: any
  CONST_GSOptions_configure: GSOptions
}

interface GSCache {
  unitBaseInfoXDicCache: any
  adminCodeDicCache: any
  indoorAreaDicCache_Map: any
  indoorAreaDicCache_Scene: any
  indoorAreaExtentDicCache_Map: any
  indoorAreaExtentDicCache_Scene: any
  gridAreaDicCache_Map: any
  gridAreaDicCache_Scene: any
  ovUnitModelInfoDicCache_Scene: any
}
interface GSUrlHeader {
  bwUrlHeader: string
  gsUrlHeader: string
}
interface GSOptions {
  dbService: any
  dbService_Record: any
  unitUrlHeader: string
  wmsURL: string
  deviceIconUrlHeader: string
  deviceIconAlarmGifUrl: string
  skyUrl: any[]
  ovUnitModelUrlHeader: string
  deviceFieldNameState: string
  deviceFieldNameOnlineState: string
  deviceStateValueConvertFun: any
}
interface GSParams {
  styleInfo_2D: any
  styleInfoGrid_2D: any
  styleInfo_3D: any
  styleInfoGrid_3D: any
  tileURL: string
  tileURL_Satellite: string
  tileURL_Traffic: string
  tileURL_Annotation: string
}
