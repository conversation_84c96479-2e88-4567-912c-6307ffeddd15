/**
 * Created by Pan<PERSON><PERSON><PERSON><PERSON> on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}
/**
 * 主机回路点位
 * @param {(Object)} newInfo
 */
export function switchCode(newInfo) {
  // 如果三个都不存在，则显示二次码
  if (!newInfo.laMake && !newInfo.laLoop && !newInfo.laPoint) {
    return '--'
  } else {
    newInfo.laMake = newInfo.laMake ? newInfo.laMake : ''
    newInfo.laLoop = newInfo.laLoop ? newInfo.laLoop : ''
    newInfo.laPoint = newInfo.laPoint ? newInfo.laPoint : ''
    let pinjie = ''
    if (newInfo.laMake !== '') {
      pinjie = newInfo.laMake
    }
    if (newInfo.laMake !== '' && newInfo.laLoop !== '') {
      pinjie = newInfo.laMake + '-' + newInfo.laLoop
    }
    if (
      newInfo.laMake !== '' &&
      newInfo.laLoop !== '' &&
      newInfo.laPoint !== ''
    ) {
      pinjie = newInfo.laMake + '-' + newInfo.laLoop + '-' + newInfo.laPoint
    }
    return pinjie
    // return newInfo.laMake + '-' + newInfo.laLoop + '-' + newInfo.laPoint ;
  }
}
export function switchCodeClass(newInfo) {
  // 如果三个都不存在，则显示二次码
  if (!newInfo.laMake && !newInfo.laLoop && !newInfo.laPoint) {
    return '无'
  } else {
    newInfo.laMake = newInfo.laMake ? newInfo.laMake : ''
    newInfo.laLoop = newInfo.laLoop ? newInfo.laLoop : ''
    newInfo.laPoint = newInfo.laPoint ? newInfo.laPoint : ''
    let pinjie = ''
    if (newInfo.laMake !== '') {
      pinjie = newInfo.laMake
    }
    if (newInfo.laMake !== '' && newInfo.laLoop !== '') {
      pinjie = newInfo.laMake + '-' + newInfo.laLoop
    }
    if (
      newInfo.laMake !== '' &&
      newInfo.laLoop !== '' &&
      newInfo.laPoint !== ''
    ) {
      pinjie = newInfo.laMake + '-' + newInfo.laLoop + '-' + newInfo.laPoint
    }
    return pinjie
    // return newInfo.laMake + '-' + newInfo.laLoop + '-' + newInfo.laPoint ;
  }
}

export function isEmpty(obj) {
  return Object.keys(obj).length === 0
}

// 判断楼层平面图是否采集
export function hasFloorImg(val, unitId) {
  let data = val.floorId && val.floorId.indexOf(unitId)
  let floorImgValues = true
  if (data != 0) {
    floorImgValues = false
  } else if (!val.mapX && !val.longitude && !val.mapY && !val.latitude) {
    floorImgValues = false
  } else {
    floorImgValues = true
  }
  return floorImgValues
}

// 千位分隔符
export const thousandBitSeparator = (number) => {
  const res = number.toString().replace(/\d+/, function (n) {
    // 先提取整数部分
    return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
      return `${$1},`
    })
  })
  return res
}
