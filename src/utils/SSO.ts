import $API from '@/common/api'
import { UserInfo } from '@/types'
import { WritableComputedRef } from 'vue'

function getSSO(query: Record<string, string>, userInfo: WritableComputedRef<UserInfo>, next: any) {
  const { single, token, apiUrl, ...rest } = query || {}
  if (single !== 'sso') return
  $API
    .post({
      url: apiUrl,
      data: {
        token,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        const { data } = res
        localStorage.setItem('orgCode', data.orgCode)
        data.userId = data.authId
        data._userId = data.id
        data.unitId = data.orgCode
        data.unitType = data.ownerType
        userInfo.value = data
        next({ query: rest })
      }
    })
}

/**
 * 根据路由参数判断是否调用sso
 * @param query
 * @returns
 */
function useSSO(query: Record<string, string>) {
  if (query.single === 'sso') {
    return true
  }
  return false
}

export const SSO = {
  useSSO,
  getSSO,
}
