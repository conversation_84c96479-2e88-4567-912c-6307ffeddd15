import { createApp } from 'vue'
import App from './App.vue'
import elementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import piniaInstance from './store'
import router from './router'
import registerComponent from './common/registerComponent'

import 'virtual:windi.css'
import 'normalize.css'
import '~/assets/css/fonts.css' // 全局字体定义
import 'element-plus/theme-chalk/dark/css-vars.css'
import '~/assets/css/element.scss'
// import 'element-plus/dist/index.css'
import 'virtual:svg-icons-register'
import '~/assets/css/base.scss'
import '~/assets/css/common.scss'
import 'video.js/dist/video-js.css'
import '~/assets/css/base.css'
import 'cropperjs/dist/cropper.min.css'
import '~/assets/css/style.css'
import '~/assets/css/wangEditor.css'
// import 'animate.css'
const app = createApp(App)
registerComponent(app)
app.use(piniaInstance)
// 处理element-ui内联样式px2rem,全局挂载
function px2rem(px: string) {
  if (/%/gi.test(px)) {
    // 有百分号%，特殊处理，表述pc是一个有百分号的数，比如：90%
    return px
  } else {
    return parseFloat(px) / 192 + 'rem'
  }
}
app.config.globalProperties.$px2rem = px2rem // 放到全局

app.use(router)

app.use(elementPlus, { size: '', locale: zhCn })

app.mount('#app')
