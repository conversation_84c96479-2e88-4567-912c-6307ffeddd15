<template>
  <div class="w-full mt-20px">
    <div class="top flex w-full">
      <div class="left w-640px flex flex-col">
        <div class="real-time-police flex-1">
          <!-- 实时警情 -->
          <RealTimePolice />
        </div>
        <div class="today-fire-situation mt-20px">
          <!-- 今日处置情况 -->
          <TodayFireSituation />
        </div>
      </div>
      <div class="right ml-20px flex-1 flex flex-col">
        <div class="gis-page p-20px bg-white flex-1">
          <!-- gis -->
          <GisPage :isIsabled='true' />
        </div>
        <div class="equipment-operation mt-20px">
          <!-- 运行情况 -->
          <EquipmentOperation />
        </div>
      </div>
    </div>
    <div class="bottom mt-20px flex w-full">
      <div class="overall-alarm-trend flex-1">
        <!-- 总体报警趋势 -->
        <OverallAlarmTrend />
      </div>
      <div class="fire-safety-factor ml-20px flex-1">
        <!-- 消防安全指数 -->
        <FireSafetyFactor />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import GisPage from './gisPage/index.vue'
import RealTimePolice from '~/components/overview/real-time-police.vue'
import TodayFireSituation from '~/components/overview/today-fire-situation.vue'
import EquipmentOperation from '~/components/overview/equipment-operation.vue'
import FireSafetyFactor from '~/components/overview/fire-safety-factor.vue'
import OverallAlarmTrend from '~/components/overview/overall-alarm-trend.vue'

const route = useRoute()
const router = useRouter()

onMounted(() => {})
</script>
<style lang="scss" scoped></style>
