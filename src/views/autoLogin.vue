<template>
  <div class="login-view relative" v-if="false">
    <div class="my-popover" v-if="isDefLogin">
      <!-- trigger="hover" -->
      <el-popover
        placement="left"
        :width="200"
        trigger="hover"
        popper-class="login-popper-box"
        content="this is content, this is content, this is content"
      >
        <template #default>
          <div class="text-box">
            <div class="title">
              <div class="name">姓名</div>
              <div>电话</div>
            </div>
            <div class="item-box" v-for="(n, index) in peopleList" :key="index">
              <div class="name">
                {{ n.name }}
              </div>
              <div>
                {{ n.phone }}
              </div>
            </div>
          </div>
        </template>
        <template #reference>
          <div class="popover-text">
            <svg-icon name="rexianfuwufuwu" :size="40" color="rgba(255,255,255,.3)"></svg-icon>
            <div>运维</div>
          </div>
        </template>
      </el-popover>
    </div>
    <div class="flex absolute logo-info items-center">
      <div>
        <img :src="loginIcon" class="w-386px" alt="" />
      </div>
      <!-- <div class="text-28px">消防监管服务平台</div> -->
    </div>
    <div class="flex absolute logo-title items-center">
      <!-- <div class="text-28px word">消防监管服务平台</div> -->
    </div>
    <!-- <img src="@/assets/image/login/side.png" class="side-img" alt=""> -->
    <div class="login-box absolute">
      <div class="border-left-box"></div>
      <div class="border-right-box"></div>
      <div class="corner left-0 top-0"></div>
      <div class="corner right-0 top-0 rotate-90"></div>
      <div class="corner left-0 bottom-0 -rotate-90"></div>
      <div class="corner right-0 bottom-0 rotate-180"></div>
      <p class="text-center text-34px font-hp-regular title">账号密码登录</p>
      <!-- <el-input v-model="loginName" class="el-input-class" placeholder="请输入账号用户名"></el-input>
      <el-input v-model="password" class="el-input-class" placeholder="请输入密码"></el-input> -->
      <el-input
        v-model="loginName"
        @keyup.enter="submit"
        :placeholder="active == '1' ? '请输入账号用户名' : '请输入手机号'"
      ></el-input>
      <el-input v-if="active == '1'" v-model="password" @keyup.enter="submit" type="password" placeholder="请输入密码">
      </el-input>
      <div class="code-box">
        <div class="code-input">
          <el-input v-model="captcha" @keyup.enter="submit" placeholder="请输入验证码"> </el-input>
        </div>
        <div class="captcha-box" v-if="active === '1'" @click="getCaptcha">
          <img :src="captchaUrl" alt="" />
        </div>
        <div class="codeBtn" :class="{ mydisabled: isDisabled }" v-if="active === '2'" @click="getSmsCaptcha">
          {{ btnText }}
        </div>
      </div>
      <el-button :loading="loading" class="mb-30px mt-40px" v-if="active === '1'" type="primary" @click="submit"
        >登录</el-button
      >
      <el-button :loading="loading" class="mb-30px mt-40px" v-if="active === '2'" type="primary" @click="phoneSubmit"
        >登录</el-button
      >

      <div class="mb-20px" style="height: 60px" v-if="active == '2'"></div>
      <div class="my-divider">
        <div class="border-left"></div>
        <div class="text">其他登录方式</div>
        <div class="border-right"></div>
      </div>
      <div class="footer-btn-box">
        <div class="btn" @click="setActive('1')" :class="{ active: active == '1' }">
          <svg-icon name="huafei" :size="14"></svg-icon>
          <span>账户密码</span>
        </div>
        <div class="btn" @click="setActive('2')" :class="{ active: active == '2' }">
          <!-- <svg-icon name="rexianfuwufuwu" :size="14" color="#ffffff"></svg-icon> -->
          <svg-icon name="touxiang" :size="14" color="#fff"></svg-icon>
          <span> 手机验证码 </span>
        </div>
      </div>
    </div>

    <div class="footer-ba" v-if="isDefLogin">
      <div>Copyright © TanZer 2010-2022 All Rights Reserved. 皖ICP备</div>
      <!-- <div class="mt-20px">
        Copyright © 2018 天泽智联科技股份公司 All rights reserved
        <span class="ipba" @click="goIpBa" track>皖ICP备18023090号-4</span>
      </div>
      <div class="imgbox" @click="goBeian" track>
        <img src="../assets/image/homePage/20210722100502_75536.png" />
        皖公网安备 34019102001071号
      </div> -->
    </div>
  </div>
  <div
    class="w-full h-full"
    v-else
    v-loading="true"
    element-loading-text="登录中..."
    element-loading-background="rgba(255, 255, 255, 0.1)"
  ></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { hex_md5 } from '../common/md5'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import { useRouter, useRoute } from 'vue-router'
import defImg from '@/assets/image/login/jgpt_left_logo.png'
import zgcb from '@/assets/image/login/zgcb-login.png'

const router = useRouter()
const route = useRoute()
const pagePath = computed(() => route.path)
const isDefLogin = computed(() => {
  return pagePath.value === '/login' || pagePath.value === '/autoLogin'
})
console.log('🚀 ~ pagePath:', pagePath)
const loginIcon = computed(() => {
  return isDefLogin.value ? defImg : zgcb
})

const loginName = ref('')
const password = ref('')
const loading = ref(true)
const captchaUrl = ref('')
const captcha = ref('')
const ui = useUserInfo()
const active = ref('1')
const btnText = ref('获取验证码')
const smsNum: any = ref(60)
const smsTimer: any = ref(null)
const isDisabled: any = ref(false)
const peopleList = ref([
  {
    name: '余海龙',
    phone: '19965147299',
  },
  {
    name: '胡亚维',
    phone: '13391519136',
  },
  {
    name: '李文',
    phone: '15855465320',
  },
  {
    name: '朱丽丽',
    phone: '15855465320',
  },
])

onMounted(() => {
  getCaptcha()
  loginName.value = 'system'
  password.value = 'tanZer@2023'
  submit()
})

const captchaId = ref('')
const getCaptcha = async () => {
  // 获取验证码  账户密码登录
  let res = await $API.post({
    url: '/login/captcha',
    params: {
      height: 30,
      width: 70,
    },
  })
  captchaUrl.value = res.data.code
  captchaId.value = res.data.captchaId
}
const getSmsCaptcha = async () => {
  // 获取手机号验证码
  if (isDisabled.value) return
  // /^1[34578]\d{9}$/
  const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
  if (!reg.test(loginName.value)) {
    ElMessage.error('请输入正确的手机号')
    return
  }
  let res
  try {
    res = await $API.post({
      url: '/login/smsCaptcha',
      params: {
        phone: loginName.value,
      },
    })

    btnText.value = `重新发送(${smsNum.value})`
    isDisabled.value = true
    smsTimer.value = setInterval(() => {
      smsNum.value--
      btnText.value = `重新发送(${smsNum.value})`
      if (smsNum.value <= 0) {
        clearInterval(smsTimer.value)
        isDisabled.value = false
        smsTimer.value = null
        btnText.value = '重新发送'
        smsNum.value = 60
      }
    }, 1000)
  } catch (error) {
    console.log(error)
  }
}
const phoneSubmit = async () => {
  // 手机号登录
  let res
  if (!loginName.value) return ElMessage.error('请输入正确的手机号')
  if (!captcha.value) return ElMessage.error('验证码不能为空')

  try {
    res = await $API.post({
      url: '/login/smsLogin',
      params: {
        telPhone: loginName.value,
        captcha: captcha.value,
        client: 'WEB',
        sysType: '1',
      },
    })
    console.log(res)
    if (res && res.code === 'success') {
      const d = res.data
      localStorage.setItem('orgCode', d.orgCode)
      d.userId = d.authId
      d._userId = d.id
      d.unitId = d.orgCode
      d.unitType = d.ownerType
      ui.value = d
      console.log(d)
      let resourceList = d.resourceList
      console.log(geturl(resourceList))
      const url = geturl(resourceList)
      router.push(url)
    }
  } catch (error) {
    console.log(error)
  }
}
const setActive = (val: string) => {
  // 更改登录方式
  active.value = val
  loginName.value = ''
  password.value = ''
  captcha.value = ''
}
function submit() {
  // 账号密码登录
  let l = loginName.value,
    p = password.value
  if (l === '') {
    return ElMessage.error('账号不能为空')
  }

  if (p === '') {
    return ElMessage.error('密码不能为空')
  }
  // if (!captcha.value) return ElMessage.error('验证码不能为空')
  p = hex_md5(p + 'true')
  loading.value = true
  $API
    .post({
      url: '/login/login',
      params: {
        client: 'WEB',
        loginName: l,
        password: p,
        captcha: captcha.value,
        sysType: 1, // 系统（0-渠道 1-行业 默认：0-渠道）
        captchaId: captchaId.value,
      },
    })
    .then((res: any) => {
      loading.value = false
      if (res && res.code === 'success') {
        const d = res.data
        localStorage.setItem('orgCode', d.orgCode)
        d.userId = d.authId
        d._userId = d.id
        d.unitId = d.orgCode
        d.unitType = d.ownerType
        ui.value = d
        console.log(d)
        let resourceList = d.resourceList
        console.log(geturl(resourceList))
        const url = geturl(resourceList)
        router.push(url)
      } else {
        // 自动从新获取验证码
        // getCaptcha()
        router.push('/login')
      }
    })
}
const geturl = (arr) => {
  const nameArr = ['安全管理一张图', '安全一张图', '设备一张图']
  let item = arr.find((m) => nameArr.includes(m.resName))
  let cItem
  if (item?.children && item.children.length > 0) {
    cItem = item.children.find((n) => nameArr.includes(n.resName))
  } else if (item && item.resUrl) {
    return item.resUrl
  }
  if (item && cItem) {
    return cItem.resUrl
  } else {
    return getBaseUrl(arr)
  }
}
const getBaseUrl = (arr: any[] = []) => {
  // const item = resList.find((m) => arr.includes(m.resName))
  // if (item?.children && item.children.length > 0) {
  //   cItem = item.children.find((n) => arr.includes(n.resName))
  // }

  if (arr[0] && arr[0].children && arr[0].children.length > 0) {
    return getBaseUrl(arr[0].children)
  } else {
    return arr[0].resUrl
  }
}

// function goBeian() {
//   window.open(
//     'https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=34019102001071',
//     '_black'
//   )
// }
// function goIpBa() {
//   //
//   window.open('https://beian.miit.gov.cn/#/Integrated/index', '_black')
// }
</script>

<script lang="ts">
export default {}
</script>
<style lang="scss">
.login-popper-box {
  background: rgba(0, 57, 125, 0.9) !important;
  border: 2px solid #72add9 !important;
  box-shadow: 0px 0px 20px 0px rgba(187, 187, 187, 0.25) !important;
  border-radius: 8px !important;

  .el-popper__arrow::before {
    background: rgba(0, 57, 125, 0.9) !important;
    border: 2px solid #72add9 !important;
  }

  .text-box {
    color: #fff;

    .name {
      min-width: 60px;
    }

    .title {
      font-size: 16px;
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      color: #ffffff;
      line-height: 40px;
      display: flex;
      align-items: center;
    }

    .item-box {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
  }
}
</style>
<style lang="scss" scoped>
.login-view {
  position: relative;
  background: url('@/assets/image/login/login_bg_new.png') no-repeat;
  // background-size: 100% calc(100% - 60px);
  background-size: 100% 100%;
  width: 100%;
  height: 100%;

  // height: calc(100% - 60px);
  // margin-bottom: 60px;
  .my-popover {
    position: absolute;
    right: 15px;
    bottom: 20%;
    background: rgba(0, 57, 125, 0.9);
    border: 2px solid #72add9;
    box-shadow: 0px 0px 20px 0px rgba(187, 187, 187, 0.25);
    border-radius: 8px;

    .popover-text {
      color: #fff;
      text-align: center;
      font-size: 14px;
      padding: 5px;
    }

    // background: red;
  }

  .footer-ba {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    // padding-bottom: 15px;
    padding-bottom: 20px;
    font-size: 12px;
    color: #fff;
    // color: rgba(127, 132, 148, 1);

    // background: rgba(255, 255, 255, 0.2);

    .ipba {
      cursor: pointer;
    }

    .imgbox {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 10px;
      cursor: pointer;

      img {
        margin-right: 5px;
      }
    }
  }

  .side-img {
    width: 678px;
    height: 669px;
    position: absolute;
    left: 10%;
    top: 50%;
    transform: translateY(-50%);
  }

  .logo-info {
    color: white;
    // background: red;
    left: 139px;
    top: 30px;
  }

  .logo-title {
    color: white;
    left: 755px;
    top: 157px;
    text-align: center;

    .word {
      font-weight: 500;
      width: 410px;
      height: 48px;
      font-size: 50px;
    }
  }

  .login-box {
    padding: 40px 60px;
    background: white;
    background: rgba(0, 57, 125, 0.7);
    right: 6%;
    top: 40%;
    width: 460px;
    transform: translateY(-50%);
    color: #fff;
    border: 2px solid #72add9;
    box-shadow: 0px 0px 20px 0px rgba(187, 187, 187, 0.25);
    border-radius: 8px;

    .border-left-box {
      height: 200px;
      width: 50px;
      // background: red;
      position: absolute;
      left: 0px;
      top: 0;
      transform: translateX(-50%);
      background-image: url(../assets/image/login/border.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .border-right-box {
      height: 230px;
      width: 50px;
      // background: red;
      position: absolute;
      right: 0px;
      bottom: 0;
      transform: translateX(50%);
      background-image: url(../assets/image/login/border.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .title {
      color: #fff;
      font-size: 24px;
      margin-bottom: 30px;
    }

    .code-box {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;

      :deep(.el-input) {
        margin-bottom: 0px;
      }

      .code-input {
        flex: 1;
      }

      .codeBtn {
        height: 43px;
        line-height: 43px;
        border: 1px solid #74c6f1;
        background: rgba(15, 72, 153, 0.5);
        border-radius: 4px;
        padding: 0 10px;
        margin-left: 15px;
        cursor: pointer;
      }

      .mydisabled {
        border: 1px solid #999;

        background: rgba(255, 255, 255, 0.3);
        color: #eee;
      }

      .captcha-box {
        height: 43px;
        width: 100px;
        margin-left: 15px;

        & > img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .my-divider {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;

      .text {
        margin: 0 10px;
      }

      .border-right,
      .border-left {
        height: 2px;
        background: linear-gradient(to right, rgba(24, 144, 255, 1), rgba(24, 144, 255, 0.1));
        flex: 1;
      }

      .border-left {
        background: linear-gradient(to left, rgba(24, 144, 255, 1), rgba(24, 144, 255, 0.1));
      }
    }

    .footer-btn-box {
      margin-top: 20px;
      display: flex;
      justify-content: space-around;

      .btn {
        display: flex;
        align-items: center;
        color: rgba(255, 255, 255, 1);
        opacity: 0.5;
        cursor: pointer;

        span {
          display: inline-block;
          margin-left: 6px;
        }
      }

      .active {
        opacity: 1;
      }
    }

    :deep(.el-input) {
      margin-bottom: 30px;

      .el-input__wrapper {
        background: rgba(64, 158, 255, 0.1);
        box-shadow: 0 0 0 1px rgba(0, 83, 149, 0) !important;
        border: 1px solid rgba(116, 198, 241, 1);
        color: rgba(255, 255, 255, 1);
        padding: 0 4px 0 0;
        border-radius: 5px;

        &:hover {
          box-shadow: 0 0 0 1px rgba(0, 83, 149, 0) !important;
        }
      }

      .el-input__inner {
        height: 50px;
        line-height: 50px;
        font-size: 16px;
        // background: rgba(64, 158, 255, .1);
        // box-shadow: 0 0 0 2px rgba(0, 83, 149, .5);
        background: rgba(0, 0, 0, 0);
        color: rgba(255, 255, 255, 1);
        text-indent: 1em;
        caret-color: #eee;
        margin-left: -2px;
        // &:focus {
        //   box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.5);
        // }
      }

      .el-input__inner::first-line {
        color: #eee;
      }

      .el-input--prefix {
        background: #fff;
        border: 1px solid #ccc;
      }

      input:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px rgba(7, 70, 145, 1) inset !important;
        -webkit-text-fill-color: #fff !important;
      }
    }

    .el-button {
      width: 100%;
      height: 50px;
      // line-height: 50px;

      > span {
        font-size: 20px;
      }
    }

    .corner {
      width: 40px;
      height: 40px;
      // background: url("@/assets/image/login/corner.png") no-repeat;
      background-size: 100%;
      position: absolute;
    }
  }
}
</style>
