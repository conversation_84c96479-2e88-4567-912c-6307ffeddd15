<template>
  <div class="w-full h-full bg-white p-25px message-box">
    <div class="tabs mb-25px">
      <el-button
        v-for="n in buttonList"
        :type="n.value == activeStatus ? 'primary' : 'default'"
        @click="setStatus(n.value)"
        v-show="n.isShow"
        :key="n.value"
        >{{ n.label }}</el-button
      >
    </div>
    <div class="content">
      <template v-if="tableData && tableData.length">
        <div class="content-item cursor-pointer" v-for="n in tableData" :key="n.messageId" track @click="goDetail(n)">
          <div class="icon">
            <img src="../../../assets/image/message/fire.png" v-if="!n.itemType && n.messageType == 102" alt="" />
            <img src="../../../assets/image/message/hidden.png" v-if="n.itemType == 1" alt="" />
            <img src="../../../assets/image/message/hidden-20.png" v-if="n.itemType == 2" alt="" />
            <img src="../../../assets/image/message/fault.png" v-if="n.itemType == 4" alt="" />
            <img src="../../../assets/image/message/fault-20.png" v-if="n.itemType == 5" alt="" />
          </div>
          <div :class="n.isRead == 1 ? 'gray' : ''">
            <div class="title-box">
              <div class="flex items-center header">
                <span> {{ n.messageTitle }} </span>
                <i class="unread" v-if="n.isRead != 1"></i>
              </div>
              <div class="time">
                {{ n.createTime }}
              </div>
            </div>
            <div class="text">
              {{ n.messageContent }}
            </div>
          </div>
        </div>
      </template>
      <div class="content-item" v-else>
        <noData></noData>
      </div>
    </div>
    <!-- 'pagination-wrap flex justify-end pb-0px pt-5px' -->
    <div class="table-list">
      <div class="pagination-wrap flex justify-end pb-0px pt-5px pl-10px">
        <el-pagination
          :currentPage="pageModel.pageNo"
          :page-size="pageModel.pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :small="false"
          :disabled="false"
          layout="total, prev, pager, next, sizes"
          :total="pageModel.total"
          @size-change="handleSizeChange"
          @current-change="currentChange"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import noData from '@/components/public/noData.vue'
import { setPageJsonQueryParamsAdapter } from '@/commonTypes/jumpDataStatisticsQuery'
import { useRouter } from 'vue-router'
import { useCounterStore, useUserInfo } from '~/store'

const router = useRouter()
import $API from '~/common/api'
const counterStore = useCounterStore()

const activeStatus: any = ref(0)
const ui = useUserInfo()

const buttonList: any = ref([
  {
    label: '全部',
    value: '0',
    isShow: true,
  },
  {
    label: '真警提醒',
    value: '1',
    isShow: true,
  },
  {
    label: '超期10天隐患提醒',
    value: '2',
    isShow: ui.value.topSuperviseId == ui.value.orgCode ? false : true,
  },
  {
    label: '超期20天隐患提醒',
    value: '3',
    isShow: ui.value.topSuperviseId == ui.value.orgCode ? true : false,
  },
  {
    label: '超期10天故障提醒',
    value: '4',
    isShow: ui.value.topSuperviseId == ui.value.orgCode ? false : true,
  },
  {
    label: '超期20天故障提醒',
    value: '5',
    isShow: ui.value.topSuperviseId == ui.value.orgCode ? true : false,
  },
])
const pageModel = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  total: 0,
})
const tableData: any = ref([])
onMounted(() => {
  getTableData()
})
const goDetail = (item) => {
  if (item.isRead != 1) {
    changeReadState(item.messageId)
  }
  let eventType = 1
  if (item.messageType == '106') {
    eventType = 4
  } else if (item.messageType == '111') {
    eventType = 3
  }
  router.push({
    path: '/fireRemoteManage/eventHanding',
    query: {
      ...setPageJsonQueryParamsAdapter({
        eventType,
        disposeId: item.businessId,
        userId: ui.value.userId,
      }),
      isShowBack: 1,
    },
  })
}
const handleSizeChange = (val) => {
  pageModel.pageNo = 1
  pageModel.pageSize = val

  getTableData()
}
const currentChange = (val) => {
  pageModel.pageNo = val
  getTableData()
}

const setStatus = (val: any) => {
  activeStatus.value = val
  pageModel.pageNo = 1
  pageModel.total = 0
  getTableData()
}
const changeReadState = async (messageIds, type = 1) => {
  try {
    const res: any = await $API.post({
      url: '/message/changeReadState',
      params: {
        messageIds: messageIds,
        type: type,
        userId: ui.value.userId,
      },
    })
    if (res.code == 'success') {
      counterStore.messageNum--
      if (counterStore.messageNum < 0) counterStore.messageNum = 0
    }
  } catch (error) {}
}
async function getTableData() {
  try {
    const { pageNo, pageSize } = pageModel
    const res: any = await $API.post({
      url: '/message/getMessageInfoList',
      params: {
        superviseId: ui.value.orgCode,
        pageNo,
        pageSize,
        msgType: activeStatus.value,
        userId: ui.value.userId,
      },
    })
    tableData.value = res.data.rows
    pageModel.total = res.data.total
  } catch (error) {}
}
</script>

<style lang="scss" scoped>
.message-box {
  display: flex;
  flex-direction: column;

  .content {
    flex: 1;
    overflow: hidden;

    &:hover {
      overflow: auto;
    }

    .content-item {
      padding: 20px 0;
      border-bottom: 1px solid #eee;
      display: flex;

      img {
        display: block;
        width: 42px;
      }

      .icon {
        margin-right: 15px;
      }

      .title-box {
        display: flex;
        margin-bottom: 10px;
        align-items: center;

        .header {
          min-width: 100px;
        }

        .unread {
          display: inline-block;
          border-radius: 100%;
          height: 6px;
          width: 6px;
          background: #f00;
          margin-left: 8px;
        }

        .time {
          margin-left: 40px;
        }
      }

      .gray {
        color: #909399;
      }
    }
  }

  .page-box {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
