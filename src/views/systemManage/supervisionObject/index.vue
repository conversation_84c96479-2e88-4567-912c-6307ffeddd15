<template>
  <div class="w-full h-full flex flex-col">
    <div class="top-header bg-white px-30px py-10px flex justify-between items-center">
      <span class="flex">{{ route.query.superviseUnitName + ':' }}已监管单位：<span class="count">{{ superviseRelCount.BNum +
        superviseRelCount.CNum }}</span>
        家</span>

      <div class="flex">
        <el-button class="page-btn" @click="goBack">返回</el-button>
      </div>
    </div>

    <!-- <div
      :class="
        advanced
          ? 'query-conditions-form-role mt-20px h-105px'
          : 'query-conditions-form-role mt-20px h-60px'
      "
    > -->
    <div class="query-conditions-form-sbda mt-20px main_search">
      <header-item title="单位名称">
        <el-input v-model="queryParams.ownerName" placeholder="请输入" clearable class="flex-1 h-full" @input="handleQuery">
        </el-input>
      </header-item>
      <header-item title="行政区划" :span="5">
        <AdministrativeDivisionCas v-model="queryParams.area" @change="areaChange" />
      </header-item>
      <header-item title="单位类型" :span="5">
        <UnitTypeSelect v-model="queryParams.ownerType" @change="handleQuery" placeholder="全部">
        </UnitTypeSelect>
      </header-item>
      <header-item v-show="advanced" title="监管状态">
        <el-select v-model="queryParams.superviseStatus" placeholder="全部" clearable @change="handleQuery">
          <el-option v-for="item in superviseStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </header-item>
      <header-item v-show="advanced" title="监测服务状态">
        <el-select v-model="queryParams.serverStatus" placeholder="全部" clearable @change="handleQuery">
          <el-option v-for="item in serverStatusOptionsSearch" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </header-item>
      <div class="export action">
        <el-space :size="10">
          <el-button type="primary" class="export-btn" @click="educe">
            导出
          </el-button>
          <el-button class="page-btn" @click="batchActionType = 'bind'">批量监管</el-button>
          <el-button class="page-btn" @click="batchActionType = 'unbind'">批量解除监管</el-button>
          <!-- <span
            class="cursor-pointer"
            style="color: #4187f9"
            @click="advanced = !advanced"
          >
            {{ advanced ? '收起' : '展开' }}
            <el-icon v-if="advanced" color="#4187F9"><ArrowUp /></el-icon>
            <el-icon v-else color="#4187F9"><ArrowDown /></el-icon>
          </span> -->
          <!-- </template> -->
          <!-- <template v-if="queryParams.ownerType === 3">
            <el-button type="primary" @click="batchActionType = 'unbind'"
              >批量解除监管对象</el-button
            >
            <el-button type="primary" @click="batchActionType = 'bind'"
              >批量关联监管对象</el-button
            >
          </template> -->
        </el-space>
      </div>
    </div>
    <div class="bg-white" v-if="batchActionType">
      <div class="select-rows px-20px py-10px flex items-center" style="
          background: #e5f2ff;
          border: 1px solid #baddff;
          border-radius: 4px;
        ">
        <span class="mr-5px">
          <img src="@/assets/image/tips_i.png" alt="" /></span>
        <span class="flex-1">已选择
          <span class="mx-2px" style="color: #0080ff">{{
            selectedTableRows.length
          }}</span>
          项</span>
        <el-space :size="10">
          <el-button @click="batchCancel">取消</el-button>
          <el-button type="primary" :loading="batchActionLoading" :disabled="selectedTableRows.length === 0"
            @click="batchAction">批量{{ batchActionTypeName }}</el-button>
        </el-space>
      </div>
    </div>

    <div class="flex-1 overflow-hidden">
      <table-list ref="tableListRef" :hasTop="batchActionType ? true : false" @page-change="pageChange" :data="tableData"
        v-loading="loading" :pageModel="pageModel" row-key="unitId" @handle-selection-change="handleSelectionChange"
        stripe>
        <el-table-column type="selection" :selectable="selectable" reserve-selection width="55" v-if="batchActionType" />
        <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.key" :label="item.title"
          :show-overflow-tooltip="item.showOverflowTooltip">
          <template #default="scope" v-if="['serverStatus', 'serverState'].includes(item.key)">
            <span v-if="queryParams.ownerType == 0" :class="scope.row.serverStatus == 1
              ? 'service-status'
              : 'service-status-grey'
              "></span>
            <span v-if="queryParams.ownerType == 3" :class="scope.row.serverState == 0
              ? 'service-status'
              : 'service-status-grey'
              "></span>
            {{
              getNameByCode(scope.row[item.key], serverStatusOptions) || '--'
            }}
          </template>
          <!-- <template #default="scope" v-else-if="item.key === 'unitDataStatus'">
            {{
              getNameByCode(scope.row[item.key], unitDataStatusOptions) || '--'
            }}
          </template> -->
          <template #default="scope" v-else-if="item.key === 'supervisedStatus'">
            <span :class="
                scope.row.supervisedStatus == 0
                  ? 'jg-status-orange'
                  : 'service-status'
              "></span>
            {{ scope.row.supervisedStatus === 0 ? '未监管' : '已监管' }}
          </template>

          <template #default="scope" v-else-if="item.key === 'area'">
            {{
            scope.row.provinceName + scope.row.cityName + scope.row.countyName
            }}
          </template>
          <template #default="scope" v-else-if="item.key === 'action'">
            <el-button :loading="scope.row.actionLoading" type="text" @click="rowAction(scope.row, 1)">{{
              scope.row.supervisedStatus === 1 ? '解除监管' : '关联监管'
              }}</el-button>
          </template>
          <template #default="scope" v-else>
            {{ scope.row[item.key] || '--' }}
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
</template>

<script lang="ts" setup>
import UnitTypeSelect from '@/components/public/common-form/unitTypeSelect.vue'
import UnitSelect from '@/components/public/common-form/unitSelect.vue'
import { ref, onMounted, reactive, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import AdministrativeDivisionCas from '@/components/public/common-form/administrativeDivisionCas.vue'
import UnitNameOptionsByUnitType from '@/components/public/common-form/unitNameOptionsByUnitType.vue'
import { getNameByCode, unitTypeOptions } from '@/common/fixedOptions'
import { querySuperviseRelPageList } from '@/common/comResponse/index'
import { useUserInfo } from '@/store'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'
import { getUnitName } from '@/common/comResponse/index'
import { exportFile } from '~/common/comResponse'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { ElTable } from 'element-plus'
const route = useRoute()
const router = useRouter()
const ui = useUserInfo()
const advanced = ref<boolean>(true)
const tableListRef = ref()
const superviseRelCount = ref<any>({
  BNum: 0,
  CNum: 0
})

const queryParams = ref<any>({})

const superviseStatusOptions = [
  { label: '未监管', value: 0 },
  { label: '已监管', value: 1 }
]
const serverStatusOptionsSearch = [
  { label: '已启用', value: 1 }, // 企业单位1 是启用
  { label: '未启用', value: 0 } // 企业单位0 是禁用
]
const unitDataStatusOptions = [
  { label: '待申请', value: 0 },
  { label: '审批中', value: 1 },
  { label: '采集中', value: 3 },
  { label: '已完成', value: 4 }
]

const loading = ref<boolean>(false)
const pageModel = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  total: 0
})
const tableData = ref<any>([])
const columns = ref<any>([])

const selectedTableRows = ref<any>([])

const batchActionType = ref<string>('')

const batchActionTypeName = computed(() => {
  return batchActionType.value === 'bind' ? '监管' : '解除'
})

const batchActionLoading = ref<boolean>(false)
const serverStatusOptions = ref()
console.log(serverStatusOptions, 'serverStatusOptions')
onMounted(() => {
  init()
})
const ownerTypeValue = ref<any>(0)
function ownerTypeChange(val: any) {
  queryParams.value.ownerName = ''
  queryParams.value.superviseStatus = ''
  queryParams.value.unitDataStatus = ''
  queryParams.value.province = ''
  queryParams.value.city = ''
  queryParams.value.county = ''
  queryParams.value.contactsName = ''
  queryParams.value.telPhone = ''
  queryParams.value.serverStatus = ''
  queryParams.value.ownerType = val
  if (ownerTypeValue.value != val) {
    queryParams.value.ownerName = ''
    ownerTypeValue.value = val
  }
  handleQuery()
}
// 导出
const educe = () => {
  const params = {
    ...queryParams.value
  }
  exportFile(
    {
      url: '/superviseRel/exportSuperviseRelList',
      params,
      responseType: 'flow'
    },
    columns.value
      .map((m) => ({
        title: m.title,
        key: m.field ? m.field : m.key
      }))
      .filter((n) => n.key != 'action')
  )
}
function handleQuery() {
  getTableColumns()
  getTableData()
}

function areaChange(val: any) {
  queryParams.value.province = ''
  queryParams.value.city = ''
  queryParams.value.county = ''
  if (val && val.length > 0) {
    if (val[0]) queryParams.value.province = val[0]
    if (val[1]) queryParams.value.city = val[1]
    if (val[2]) queryParams.value.county = val[2]
  }
  handleQuery()
}

function handleSelectionChange(val: any) {
  selectedTableRows.value = val
  // console.log(selectedTableRows.value)
}

function selectable(row: any, index: number) {
  if (batchActionType.value === 'bind') {
    // 绑定时，只能选择未监管状态的
    return row.supervisedStatus === 0
  } else if (batchActionType.value === 'unbind') {
    // 解除时，只能选择已监管状态的
    return row.supervisedStatus === 1
  }
}

async function querySuperviseRelCount() {
  try {
    const res: any = await $API.post({
      url: '/superviseRel/querySuperviseRelCount',
      params: {
        superviseId: route.query.superviseUnitId
      }
    })
    if (res && res.code === 'success') {
      superviseRelCount.value = res.data
    }
  } catch (error) { }
}

async function getTableData() {
  tableData.value = []
  loading.value = true
  try {
    const { pageNo, pageSize } = pageModel
    const {
      ownerType,
      ownerName,
      province,
      city,
      county,
      superviseStatus,
      unitDataStatus,
      serverStatus,
      contactsName,
      telPhone,
      superviseId
    } = queryParams.value
    let params: any
    if (ownerType === 0) {
      params = {
        unitDataStatus,
        serverStatus
      }
    } else if (ownerType === 3) {
      params = {
        contactsName,
        telPhone,
        serverStatus
      }
    }
    params = {
      ...params,
      ownerName,
      superviseStatus,
      province,
      city,
      county,
      ownerType,
      pageNo,
      pageSize,
      superviseId: route.query.superviseUnitId
    }
    const data: any = await querySuperviseRelPageList(params)
    tableData.value = data.rows

    pageModel.total = data.total

    tableData.value.forEach((item: any) => {
      item.actionLoading = false
    })
  } catch (error) {
  } finally {
    loading.value = false
  }
}

function batchCancel() {
  batchActionType.value = ''
  selectedTableRows.value = []
  getTableData()
}

function rowAction(row: any, type: number) {
  let url = ''
  if (row.supervisedStatus === 1) {
    // 解除
    url = '/superviseRel/unbindSuperviseRel'
  } else if (row.supervisedStatus === 0) {
    // 绑定
    url = '/superviseRel/bindSuperviseRel'
  }
  const params = {
    createUserId: ui.value.id,
    createUserName: ui.value.userName,
    ownerList: [
      {
        ownerId: row.ownerId || row.unitId,
        ownerName: row.ownerName || row.unitName,
        ownerType: row.ownerType || 0,
        subCenterCode: row.subCenterCode
      }
    ],
    superviseId: route.query.superviseUnitId
  }
  requestActionInterface(url, params, row.actionLoading, type)
}

function batchAction() {
  batchActionLoading.value = true
  pageModel.pageNo = 1
  let url = ''
  const ownerList = selectedTableRows.value.map((item: any) => {
    return {
      ownerId: item.ownerId || item.unitId,
      ownerName: item.ownerName || item.unitName,
      ownerType: item.ownerType || 0,
      subCenterCode: item.subCenterCode
    }
  })
  const params = {
    createUserId: ui.value.id,
    createUserName: ui.value.userName,
    ownerList,
    superviseId: route.query.superviseUnitId
  }
  if (batchActionType.value === 'bind') {
    // 绑定
    url = '/superviseRel/bindSuperviseRel'
  } else if (batchActionType.value === 'unbind') {
    // 解除
    url = '/superviseRel/unbindSuperviseRel'
  }
  requestActionInterface(url, params, batchActionLoading.value, 0)
  tableListRef.value.handleClearSelection()
}

async function requestActionInterface(
  url: string,
  params: any,
  loadingValue: any,
  type: number
) {
  loadingValue = true
  try {
    const res: any = await $API.post({ url, data: params })
    if (res && res.code === 'success') {
      // type 为0是批量解除绑定监管 为1是单个解除绑定监管
      let msg = ''
      if (type === 0) {
        msg =
          url === '/superviseRel/bindSuperviseRel'
            ? '批量监管成功'
            : '批量解除监管成功'
      } else {
        msg =
          url === '/superviseRel/bindSuperviseRel' ? '监管成功' : '解除监管成功'
      }

      ElMessage.success(msg)
      batchActionType.value = ''
      batchActionLoading.value = false
      querySuperviseRelCount()
      handleQuery()
    }
  } catch (error) {
  } finally {
    loadingValue = false
  }
}

function resetQueryParams() {
  queryParams.value = {
    ownerType: 0,
    superviseId: route.query.superviseUnitId
  }
}

function init() {
  resetQueryParams()
  querySuperviseRelCount()
  handleQuery()
}

watch(
  () => queryParams.value.ownerType,
  (val) => {
    console.log(val, 'ownerType')
    if (val === 0) {
      serverStatusOptions.value = [
        { label: '已启用', value: 1 }, // 企业单位1 是启用
        { label: '未启用', value: 0 } // 企业单位0 是禁用
      ]
    } else {
      serverStatusOptions.value = [
        { label: '已启用', value: 0 }, // 九小家庭0 是启用
        { label: '未启用', value: 1 } // 九小家庭1 是禁用
      ]
    }
  }
)

function pageChange(obj: any) {
  pageModel.pageNo = obj.pageNo
  pageModel.pageSize = obj.pageSize
  getTableData()
}

function getTableColumns(ownerType: any = queryParams.value.ownerType) {
  if (ownerType === 0) {
    columns.value = [
      {
        title: '单位名称',
        key: 'unitName',
        showOverflowTooltip: true
      },
      {
        title: '单位类型',
        key: 'ownerTypeName',
        showOverflowTooltip: true
      },
      // {
      //   title: '运营中心',
      //   key: 'subCenterName',
      //   showOverflowTooltip: true
      // },
      {
        title: '行政区划',
        key: 'area',
        field: 'district',
        showOverflowTooltip: true
      },
      {
        title: '详细地址',
        key: 'unitAddress',
        showOverflowTooltip: true
      },
      // {
      //   title: '电子档案状态',
      //   key: 'unitDataStatus',
      //   showOverflowTooltip: true
      // },
      {
        title: '监测服务状态',
        key: 'serverStatus',
        showOverflowTooltip: true
      },
      {
        title: '监管状态',
        key: 'supervisedStatus',
        showOverflowTooltip: true
      },
      {
        title: '操作',
        key: 'action'
      }
    ]
  } else if (ownerType === 3) {
    columns.value = [
      {
        title: '单位名称',
        key: 'ownerName',
        showOverflowTooltip: true
      },
      {
        title: '单位类型',
        key: 'ownerTypeName',
        showOverflowTooltip: true
      },
      {
        title: '行政区划',
        key: 'area',
        showOverflowTooltip: true
      },
      {
        title: '详细地址',
        key: 'address',
        showOverflowTooltip: true
      },
      // {
      //   title: '门牌号',
      //   key: 'houseNumber',
      //   showOverflowTooltip: true
      // },
      // {
      //   title: '联系人',
      //   key: 'contactsName',
      //   showOverflowTooltip: true
      // },
      // {
      //   title: '联系电话',
      //   key: 'contactsPhone',
      //   showOverflowTooltip: true
      // },
      {
        title: '监测服务状态',
        key: 'serverState',
        showOverflowTooltip: true
      },
      {
        title: '监管状态',
        key: 'supervisedStatus',
        showOverflowTooltip: true
      },
      {
        title: '操作',
        key: 'action'
      }
    ]
  }
}
const options = ref<any>([])
const list = ref<any>([])
const optionLoading = ref(false)
const remoteMethod = (query: string) => {
  if (query) {
    optionLoading.value = true
    getUnitName({
      superviseStatus: ' ', // ' '是全部, 0 是未监管, 1是已监管 注意:' '必须是手动输入一个空格,否则为被接口的删除空对象删除掉,从而添加上他的默认值1
      superviseId: ui.value.orgCode,
      unitType: ownerTypeValue.value,
      unitName: query
    })
      .then((res) => {
        // list.value = res
        // options.value = list.value.filter((item) => {
        //   return item.unitName.toLowerCase().includes(query.toLowerCase())
        // })
        options.value = res
      })
      .finally(() => {
        optionLoading.value = false
      })
  } else {
    options.value = []
  }
}
const goBack = () => {
  router.back()
}
const clearOptions = () => {
  options.value = []
}
</script>
<style lang="scss" scoped>
.back-button {
  margin: 10px 10px;
  text-align: right;
}

.top-header {
  font-size: 16px;
  color: #333;

  .count {
    color: #0080ff;
    margin: 0 5px;
    font-size: 16px;
  }
}

::v-deep .table-list .el-table__body .el-table__row .el-table__cell {
  border-bottom: 1px solid #e5e7eb;
}

.export {
  // margin-right: -22px;
}

.service-status-grey {
  margin-bottom: 0.01042rem;
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  border-radius: 50%;
  background: #bfbfbf;
}

.jg-status-orange {
  margin-bottom: 0.01042rem;
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  border-radius: 50%;
  background: #fc9f1c;
}

.service-status {
  margin-bottom: 0.01042rem;
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  border-radius: 50%;
  background: #52c41a;
}

.select-rows {
  margin: 0px 20px;
}
</style>
