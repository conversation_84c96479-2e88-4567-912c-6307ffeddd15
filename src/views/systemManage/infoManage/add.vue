<template>
  <div class="formView h-full">
    <h3 class="text-16px font-bold mb-20px">
      {{ currentType === 'edit' ? '编辑' : '新增' }}动态
    </h3>
    <el-form
      ref="formRef"
      :model="trendsForm"
      :rules="rules"
      :label-width="95"
      :label-position="labelPosition"
      class="bg-white w-full"
    >
      <el-row class="!mb-0px">
        <el-col :span="7">
          <el-form-item
            label="标题"
            :label-width="95"
            prop="superviseSystemDynamicsTitle"
          >
            <el-input
              v-model="trendsForm.superviseSystemDynamicsTitle"
              maxlength="50"
              placeholder="请输入通知标题"
              autocomplete="off"
              clearable
              show-word-limit
              class="flex-1 h-full"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" class="h-full">
          <el-form-item
            label="内容"
            prop="superviseSystemDynamicsContent"
            class="superviseSystemDynamicsContent"
          >
            <rich-editor
              :toolbarConfig="toolbarConfig"
              :containerStyle="{ width: '100%' }"
              :editorStyle="{ height: '320px' }"
              @updateNoticeFormContent="updateNoticeFormContent"
              :default-value="trendsForm.superviseSystemDynamicsContent"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="justify-between">
        <div></div>
        <div>
          <el-button @click="goBack">取消</el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="publishNow(formRef)"
          >
            立即发布
          </el-button>
        </div>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, onUnmounted, watch, computed } from 'vue'
import type { ElForm } from 'element-plus'
import $API from '~/common/api'
import { useRouter, useRoute } from 'vue-router'
import { useUserInfo, useSystemDynamicsInfo } from '@/store'

import richEditor from '@/components/public/wangeditor.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const toolbarConfig = {
  toolbarKeys: [
    'headerSelect', // 标题
    'header1', // 标题1
    'header2', // 标题2
    'header3', // 标题3
    'header4', // 标题4
    'header5', // 标题5
    'bold', // 粗体
    'underline', // 下划线
    'italic', // 斜体
    'through', // 删除线
    'clearStyle', // 清除格式
    'color', // 字体颜色
    'bgColor', // 背景色
    'fontSize', // 字号
    'fontFamily', // 字体
    'lineHeight', // 行高
    'indent', // 增加缩进
    'delIndent', // 减少缩进
    'justifyLeft', // 左对齐
    'justifyRight', // 右对齐
    'justifyCenter', // 居中对齐
    'justifyJustify', // 两端对齐
    'divider', // 分割线
    'emotion', // 表情
    'bulletedList', // 无序列表
    'numberedList', // 有序列表
    'redo', // 重做
    'undo', // 撤销
    'fullScreen' // 全屏
  ]
}

const router = useRouter()
const route = useRoute()

const labelPosition = ref('top')
const ui = useUserInfo()

const systemDynamicsInfo = useSystemDynamicsInfo()

const loading = ref(false)

type FormInstance = InstanceType<typeof ElForm>
interface typeOp {
  [key: string]: any
}
const props: any = defineProps({
  row: {
    type: Object,
    default: () => {}
  }
})

const currentType = ref('add')

let isFirst = true
const updateNoticeFormContent = (valueHtml) => {
  console.log(valueHtml)
  trendsForm.superviseSystemDynamicsContent = valueHtml
  if (isFirst) {
    isFirst = false
  } else {
    formRef.value!.validateField('superviseSystemDynamicsContent')
  }
}

const trendsForm: typeOp = reactive({
  superviseSystemDynamicsTitle: '',
  superviseSystemDynamicsContent: '',
  createUser: ui.value._userId,
  createUserName: ui.value.userName,
  superviseSystemDynamicsId: '',
  superviseId: ui.value.orgCode || ''
})

const formRef = ref<FormInstance>()

const rules = reactive({
  superviseSystemDynamicsTitle: [
    {
      required: true,
      message: '请输入系统动态标题',
      trigger: 'change'
    }
  ],
  superviseSystemDynamicsContent: [
    {
      required: true,
      message: '请输入内容',
      trigger: 'change'
    }
  ]
})

const emit = defineEmits(['cancel'])
const cancel = () => {
  emit('cancel', false)
}

function publishNow(formEl: FormInstance | undefined) {
  const tit = '是否立即发布系统动态？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        submitForm(formEl)
      }
      done()
    }
  })
}

async function submitForm(formEl: FormInstance | undefined, to?: Object) {
  console.log(trendsForm)
  if (!formEl) return

  await formEl.validate(async (valid) => {
    if (valid) {
      if (
        !trendsForm.superviseSystemDynamicsTitle &&
        !trendsForm.superviseSystemDynamicsContent
      )
        return ElMessage.warning('系统动态标题、内容不能为空！')

      if (currentType.value == 'add') {
        const res: any = await $API.post({
          url: '/systemDynamics/addSystemDynamics',
          data: {
            ...trendsForm
          }
        })
        if (res.code == 'success') ElMessage.success('新增成功！')
      } else if (currentType.value === 'edit') {
        const res: any = await $API.post({
          url: '/systemDynamics/editSystemDynamics',
          data: {
            ...trendsForm
          }
        })
        if (res.code == 'success') ElMessage.success('编辑成功！')
      }
      if (!to) {
        router.push('/systemManage/infoManage/index')
      }
      cancel()
    }
  })
}

onMounted(() => {
  console.log(systemDynamicsInfo.value, 'systemDynamicsInfo')

  if (route.query.currentType) {
    currentType.value = route.query.currentType as string
  }

  if (currentType.value == 'add') {
    systemDynamicsInfo.value = ''
  } else if (currentType.value == 'edit') {
    for (let key in systemDynamicsInfo.value) {
      if (
        trendsForm.hasOwnProperty(key) &&
        systemDynamicsInfo.value.hasOwnProperty(key)
      ) {
        // 将源对象的值赋值给目标对象的对应键
        trendsForm[key] = systemDynamicsInfo.value[key]
      }
    }
  }
  console.log(trendsForm, 'trendsForm')
})

onUnmounted(() => {})

const goBack = () => {
  router.go(-1)
}
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss" scoped>
:deep(.el-textarea .el-input__count) {
  bottom: -25px !important;
}

.add-dialog {
  &:deep(.el-dialog__header) {
    text-align: left;
  }
}

.formView {
  padding: 20px;
  background-color: #fff;

  :deep(.el-form-item .el-form-item__label) {
    width: 110px !important;
  }

  :deep(.publishTimeModel) {
    margin-top: 35vh;
  }

  :deep(.publishTimeModel .el-input__inner) {
    padding-left: 40px !important;
  }
}
</style>
