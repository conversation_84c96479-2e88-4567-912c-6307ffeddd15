<!-- infoManage -->
<template>
  <div class="w-full h-full flex flex-col infoManage">
    <div class="main_search">
      <header-item title="发布时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY-MM-DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>
      <div class="export action" style="margin-bottom: 0">
        <el-button
          type="primary"
          class="export-btn"
          @click="$router.push('/systemManage/infoManage/add')"
        >
          新增
        </el-button>
      </div>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span
                class="operate-item"
                track
                @click="handleDetails(scope.row)"
              >
                详情
              </span>
              <span class="operate-item" track @click="editHandle(scope.row)">
                编辑
              </span>
              <span class="operate-item" track @click="deleteHandle(scope.row)">
                删除
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
    <infoManageDetails
      :info="systemDynamicsInfo"
      v-model="isDetail"
    ></infoManageDetails>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, toRaw, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo, useSystemDynamicsInfo } from '@/store'
import { ElMessage, ElMessageBox } from 'element-plus'
import infoManageDetails from '@/views/systemManage/infoManage/details.vue'
import {
  querySystemDynamics,
  delSystemDynamics,
  getSystemDynamicsDetail
} from '@/common/comResponse/index'

const userInfo = useUserInfo()
const route: any = useRoute()
const router: any = useRouter()
const isDetail = ref(false)
const systemDynamicsInfo = useSystemDynamicsInfo()

const props = defineProps({
  disposeId: {
    type: String,
    default: ''
  },
  propSearch: {
    type: Object,
    default: {}
  }
})

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const searchFrom = reactive({
  superviseId: userInfo.value.orgCode,
  startTime: '', //开始时间
  endTime: '' //结束时间
})

// 离线时间
const timeAll: any = ref([])
// timeAll.value = $API.getDays(30)
// searchFrom.startTime = timeAll.value[0] + ' 00:00:00'
// searchFrom.endTime = timeAll.value[1] + ' 23:59:59'

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

/**
 * 点击详情按钮
 * @param item 当前点击的表格的行数据
 */

async function handleDetails(item: any) {
  const res: any = await getSystemDynamicsDetail({
    superviseSystemDynamicsId: item.superviseSystemDynamicsId
  })
  console.log('res', res)
  if (res && res.code == 'success') {
    systemDynamicsInfo.value = res.data
    isDetail.value = true
  } else {
    isDetail.value = false
    ElMessage.error(res.message)
  }
}

// 编辑
const editHandle = (item) => {
  console.log(item, 'item')

  systemDynamicsInfo.value = item
  router.push('/systemManage/infoManage/edit?currentType=edit')
}

/**
 * 删除通知通告
 */
const deleteHandle = (item) => {
  ElMessageBox({
    title: '删除',
    type: 'warning',
    message: '确认删除系统动态吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await delSystemDynamics({
          superviseSystemDynamicsId: item.superviseSystemDynamicsId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '删除成功！',
            type: 'success'
          })
          done()
          init()
        } else {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
          done()
        }
      } else {
        done()
      }
    }
  })
}

onMounted(() => {
  if (props.propSearch.operationResult) {
    timeAll.value = $API.getDays()
    searchFrom.startTime = timeAll.value[0] + ' 00:00:00'
    searchFrom.endTime = timeAll.value[1] + ' 23:59:59'
  }

  init()
  getTableColumns()
})

function timeChange(val: any) {
  searchFrom.startTime = ''
  searchFrom.endTime = ''
  if (val && val.length > 0) {
    searchFrom.startTime = val[0] + ' 00:00:00'
    searchFrom.endTime = val[1] + ' 23:59:59'
  }
  init()
}

function init() {
  getTableData()
}

async function getTableData() {
  loading.value = true
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await querySystemDynamics(params)
  if (res && res.code == 'success') {
    console.log(res, '获取系统动态列表')
    tableData.value = res.data.rows || []
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

function getTableColumns() {
  columns.value = [
    {
      title: '标题',
      key: 'superviseSystemDynamicsTitle',
      showOverflowTooltip: true
    },
    {
      title: '发布时间',
      key: 'createTime',
      showOverflowTooltip: true
    },
    {
      title: '创建人',
      key: 'createUserName',
      showOverflowTooltip: true
    }
  ]
}
</script>
<style lang="scss" scoped></style>
