<template>
  <el-dialog
    custom-class="system-dialog"
    v-model="modelValue"
    width="410px"
    destroy-on-close
    lock-scroll
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    center
    top="30vh"
  >
    <template #title>
      <div class="systemDynamics-bg"></div>
      <h3 class="systemDynamics-title">系统动态</h3>
    </template>
    <div class="content" v-html="info.superviseSystemDynamicsContent"></div>
    <div slot="footer" class="dialog-footer text-center mt-20px">
      <el-button type="primary" @click="close">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps({
  info: {
    type: Object,
    default: () => {}
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['update:modelValue'])

const close = () => {
  emits('update:modelValue', false)
}
</script>

<style lang="scss">
.system-dialog {
  position: relative;
  background: linear-gradient(0deg, #ffffff, #d1e8ff);
  border-radius: 12px;

  .systemDynamics-bg {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 261px;
    height: 42px;
    background-image: url('@/assets/image/ring.png');
    background-size: cover;
  }

  .systemDynamics-title {
    font-size: 18px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #0080ff;
  }

  .el-dialog__body {
    .content {
      height: 200px;
      overflow: auto;
      line-height: 1.8;
    }
  }
}
</style>
