<template>
  <div class="w-full h-full video-control" v-loading="loadingDiv">
    <div class="query-conditions-form">
      <header-item title="组织机构名称" :titleWidth="100">
        <el-input v-model="searchFrom.superviseName" placeholder="请输入组织机构名称" clearable class="flex-1 h-full"
          @input="superviseNameInputChange">
        </el-input>
      </header-item>
      <header-item title="上级组织机构">
        <OrgCas ref="orgCasRef" class="flex-1 h-full" v-model="searchFrom.superviseUnitPid" @change="orgChange" />
      </header-item>

      <div class="export">
        <el-space :size="10">
          <el-button type="primary" class="export-btn" :icon="Plus" @click="handleAdd">新增</el-button>
          <el-button type="primary" class="export-btn" @click="educe">
            导出
          </el-button>
        </el-space>
      </div>
    </div>
    <div class="w-full flex h-[calc(100%-100px)]">
      <div class="video-tree flex flex-col">
        <div class="mb-20px org-title pl-20px pt-20px">组织机构</div>
        <div class="flex-1 overflow-auto pr-8px pl-10px pt-0px pb-0px">
          <el-tree ref="treeRef" highlight-current :props="defaultProps" :data="treeData"
            @current-change="currentChangeTree" :indent="8" :filter-node-method="filterNode" :expand-on-click-node="false"
            default-expand-all node-key="superviseId" :current-node-key="userInfo.orgCode">
            <template #default="{ node, data }">
              <div style="width: 80%" :class="searchFrom.superviseUnitId == data.superviseId
                ? 'node-bg custom-tree-node  flex'
                : 'custom-tree-node flex p-5px'
                ">
                <svg-icon :name="userInfo.orgName == node.label ? 'buildings' : 'document'
                  " :size="20" class="inline-block mr-5px">
                </svg-icon>
                <div style="width: 80%">
                  <myTooltip :str="node.label" />
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="flex-1 flex calc-tree-width">
        <table-list @current-change="currentChange" @size-change="handleSizeChange" :data="tableData" v-loading="loading"
          :pageModel="pageModel" stripe>
          <el-table-column prop="superviseUnitName" label="机构名称" show-overflow-tooltip />
          <el-table-column prop="superviseUnitPname" label="上级机构" show-overflow-tooltip />
          <el-table-column prop="supervisedCount" label="已监管单位数量 (家)" />
          <el-table-column prop="updateTime" label="更新时间" show-overflow-tooltip />
          <el-table-column label="操作">
            <template #default="scope">
              <div class="table-operate">
                <span class="operate-item" track @click="handleEdit(scope.row)">编辑</span>
                <span class="operate-item" track :class="scope.row.superviseUnitPid === '' ? 'operate-disable' : ''
                  " @click="handleDelete(scope.row)">删除</span>
                <span class="operate-item" track @click="handleViewData(scope.row)">数据权限管理</span>
              </div>
            </template>
          </el-table-column>
        </table-list>
      </div>
    </div>
  </div>
  <popup-side v-model="popupShow" :popupTitle="detailTitle">
    <add-popup :currentType="currentType" :row="currentRow" :roleList="roleOptions" @cancel="cancel">
    </add-popup>
  </popup-side>
</template>

<script lang="ts" setup>
import addPopup from '@/components/systemManage/orgPopup.vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import { onMounted, reactive, ref, computed, watch, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { PageModel } from '@/types'
import { ElMessage, ElMessageBox } from 'element-plus'
import { exportFile } from '~/common/comResponse'
import OrgCas from '@/components/public/common-form/orgCas.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const userInfo = useUserInfo()
const defaultProps = {
  children: 'child',
  label: 'superviseName'
}
const searchFrom = reactive({
  serverStatus: '',
  superviseName: '',
  superviseUnitId: userInfo.value.orgCode,
  superviseUnitPid: ''
})
const treeData: any = ref([])

const videoList: any = ref([{}, {}, {}, {}])

const videoMap: any = {}

const searchStr = ref('')

const treeRef = ref()

const deviceAddressMap = {}

const cells = [
  {
    className: 'icon-sigongge',
    value: 4
  },
  {
    className: 'icon-jiugongge',
    value: 9
  }
]
const isActive: any = ref('0')
function hidden_button(val: any) {
  isActive.value = val
}
const cellActiveIndex = ref(0)

const cellValue = ref(cells[0].value)
const currentType = ref('')
const currentRow = ref({})
const popupShow: any = ref(false)
const detailTitle: any = ref('新增')
const orgCasRef = ref()
const loadingDiv: any = ref(false)
function cancel(param: any) {
  popupShow.value = param
  // loadingDiv.value = true
  orgCasRef.value.clearData()
  orgCasRef.value.getOptions()
  getTreeList()
  initData()
  // loadingDiv.value = false
}

function handleAdd() {
  currentType.value = 'add'
  popupShow.value = true
  detailTitle.value = '新增'
  currentRow.value = {}
}

// 导出
const educe = () => {
  const params = {
    ...searchFrom,
    status: '0',
    sysType: '1'
    // pageSize: pageModel.pageSize,
    // pageNo: pageModel.pageNo
  }
  const exportFields = [
    {
      key: 'superviseUnitName',
      title: '机构名称'
    },
    {
      key: 'superviseUnitPname',
      title: '上级机构'
    },
    {
      key: 'supervisedCount',
      title: '已监管单位数量 (家)'
    },
    {
      key: 'updateTime',
      title: '更新时间'
    }
  ]
  exportFile(
    {
      url: '/organize/exportSuperviseList',
      params,
      responseType: 'flow'
    },
    exportFields
  )
}
function handleEdit(row: any) {
  if (row.userStatus == '2') return
  currentType.value = 'edit'
  popupShow.value = true
  detailTitle.value = '编辑'
  currentRow.value = row
}
function changeStatus(val, status) {
  if (val.superviseUnitPid === '') return // 最高级别的组织机构,停用,删除按钮置灰,禁止操作
  const msg = status == '0' ? '启用成功' : '停用成功'
  const tit = status == '0' ? '确定启用吗？' : '确定停用吗？'
  const tits = status == '0' ? '启用' : '停用'
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        $API
          .post({
            url: '/organize/enableSuperviseUnit',
            params: {
              superviseUnitId: val.superviseUnitId,
              serverStatus: status
            }
          })
          .then((data: any) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (data && data.code == 'success') {
              done()
              ElMessage({
                message: msg,
                type: 'success'
              })
              initData()
            }
            // else {
            //     ElMessage({
            //         message: data.message,
            //         type: "error",
            //     });
            // }
          })
      } else {
        done()
      }
    }
  })
}

function handleDelete(val: any) {
  if (val.superviseUnitPid === '') return // 最高级别的组织机构,停用,删除按钮置灰,禁止操作
  const msg = '删除成功'
  const tit = '确定删除吗？'
  const tits = '删除'
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        $API
          .post({
            url: '/organize/delSuperviseUnit',
            params: {
              superviseUnitId: val.superviseUnitId
              // serverStatus : status,
            }
          })
          .then((data: any) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (data && data.code == 'success') {
              done()
              ElMessage({
                message: msg,
                type: 'success'
              })
              initData()
              getTreeList()
            } else {
              // ElMessage({
              //   message: data.message,
              //   type: 'error'
              // })
            }
          })
      } else {
        done()
      }
    }
  })
}
function handleViewData(row) {
  router.push({
    // path: '/systemManage/sys-organizational/supervisionObject2',
    name: 'systemManage-detail',
    query: {
      superviseUnitName: row.superviseUnitName,
      superviseUnitId: row.superviseUnitId
    }
  })
}

function superviseNameInputChange(val: any) {
  initData()
}
// function orgChange(val: any) {
//   if (val && val.length > 0) {
//     searchFrom.superviseUnitPid = val[0]
//   } else {
//     searchFrom.superviseUnitPid = ''
//   }
//   initData()
// }
function orgChange(val: any) {
  searchFrom.superviseUnitPid = ''
  if (val && val.length > 0) {
    if (val.length >= 1) searchFrom.superviseUnitPid = val[val.length - 1]
  }
  initData()
}
function serverStatusChange() {
  initData()
  treeData.value = []
  getTreeList()
}

function handleCellClick(item, index) {
  cellActiveIndex.value = index
  videoList.value = Array.from({ length: item.value }).map(() => ({}))
}

function currentChange(pageNo: number) {
  // if (node.level === 3) {
  //     const {
  //         video_url: url
  //     } = data.useInfo;
  //     if (!url) return ElMessage.warning('视频暂时无法播放');
  //     data._videoUrl = url;
  //     data._isFullScreen = false;
  //     addVideo(data);
  // }
  pageModel.pageNo = pageNo
  initData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  initData()
}

function addVideo(obj) {
  let i = 0,
    vl = videoList.value
  for (; i < vl.length; i++) {
    if (isEmpty(vl[i])) {
      vl[i] = obj
      videoMap[obj._videoUrl] = obj
      return
    }
  }
}

watch(searchStr, (val) => {
  treeRef.value!.filter(val)
})

function filterNode(value: string, data) {
  if (!value) return true
  return data.label.includes(value)
}

let tempData: any = null
function currentChangeTree(data, node) {
  // searchFrom.superviseName=data.superviseName
  searchFrom.superviseUnitId = data.superviseId
  initData()
}

// async function drop(e) {
//   const el: HTMLElement = e.currentTarget
//   const index: number = parseFloat(el.dataset.index!)
//   const item = videoList.value[index]
//   if (tempData.deviceId !== item.deviceId) {
//     videoList.value[index] = {}
//     await nextTick()
//     videoList.value[index] = tempData
//   }
// }

function dragover(e) {
  e.preventDefault()
  return true
}

function dragenter(e) {
  return true
}
//进入目标元素离开
function dragleave(e) {
  return true
}
async function getTreeList() {
  $API
    .post({
      url: '/organize/getSuperviseUnitTree',
      params: {
        superviseUnitId: userInfo.value.orgCode,
        serverStatus: searchFrom.serverStatus
      }
    })
    .then(async (res: any) => {
      if (res && res.code == 'success') {
        treeData.value = [res.data]
        console.log(treeData.value + 'treeData.value--------------')
      }
    })
}
function circulationTreeData(resData) {
  // let d = resData
  // if (!Array.isArray(resData)) {
  //   resData.children = resData.child
  //   resData.label = resData.superviseName
  // }
  resData.children.forEach((item, index) => {
    item.children = item.child
    item.label = item.superviseName
    circulationTreeData(item.child)
    // if (item.child && item.child.length > 0) {
    //   circulationTreeData(item.child)
    // }
  })

  // treeData.value.push(resData)
  return resData
}
function isEmpty(obj) {
  return Object.keys(obj).length === 0
}
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})
const loading: any = ref(false)
const tableData: any = ref()
const serverStatusObj: any = ref({
  '0': '启用',
  '1': '停用'
})
const serverStatusOptions = ref<any>([
  {
    value: '0',
    label: '启用'
  },
  {
    value: '1',
    label: '停用'
  }
])
function initData() {
  loading.value = true
  try {
    $API
      .post({
        url: '/organize/querySupervisePageList',
        params: Object.assign({
          ...searchFrom,
          pageSize: pageModel.pageSize,
          pageNo: pageModel.pageNo
        })
      })
      .then((res: any) => {
        loading.value = false
        tableData.value = res.data.rows
        pageModel.total = res.data.total
      })
  } catch (error) {
    loading.value = false
  }
}
onMounted(async () => {
  await initData()
  await getTreeList()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss" scoped>
.node-bg {
  padding: 5px;
  background: #edf6ff;
}

:deep(.el-main) {
  overflow: hidden !important;
}

:deep(.str-span) {
  padding-top: 3px;
}

.hidden_button {
  // background: #eeeeee;
  background: rgba(48, 147, 250, 0.1);
  color: #3e9bfa;
  border: 0;
}

.active {
  background: #3e9bfa;
  color: #eee;
}

.disabledBtn {
  color: #c0c4cc;
  cursor: not-allowed;
}

.video-control {
  .switch-gongge {
    i {
      margin-left: 10px;
      font-size: 20px;
      color: #cdeafd;
      cursor: pointer;
    }

    i.active {
      color: #448ef7;
    }
  }

  .video-tree {
    width: 300px;
    margin-right: 20px;
    // height: 100%;
    background: white;
    border-radius: 2px;
    // padding: 20px;
    box-sizing: border-box;

    .org-title {
      text-indent: 20px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #000000;
      padding-bottom: 15px;
      font-size: 16px;
      // border-bottom: 1px solid gray;
      border-bottom: 0.00521rem solid #e1e5e9;
    }

    :deep(.el-tree) {
      .el-tree-node__content {
        height: 40px;
        margin-top: 4px;
        border-radius: 6px;

        .el-tree-node__label {
          font-size: 16px;
        }

        .el-tree-node__expand-icon {
          font-size: 15px;

          svg {
            width: 15px;
            height: 15px;
          }
        }
      }
    }
  }

  .four-cell {
    .video-group_item {
      width: 50%;
      height: 50%;
    }
  }

  .nine-cell {
    .video-group_item {
      width: 33.33%;
      height: 33.33%;
    }
  }

  .video-group {
    background: white;
    padding: 6px;
    box-sizing: border-box;
    border-radius: 6px;

    .is-full-screen {
      position: absolute !important;
      z-index: 100;
      left: 0;
      top: 0;
    }

    .video-group_item {
      padding: 6px;
      box-sizing: border-box;

      .video-wrap {
        background: #333333;
        width: 100%;
        height: 100%;
        position: relative;

        .video-address {
          background: rgba(0, 0, 0, 0.5);
          color: white;
          height: 40px;
          line-height: 40px;
          padding: 0 20px;
          z-index: 10;

          svg {
            height: inherit;
            width: inherit;
          }
        }

        .full-screen-btn {
          background: white;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-items: center;
          position: absolute;
          right: 10px;
          bottom: 10px;
          cursor: pointer;
          border-radius: 4px;
        }
      }
    }
  }
}

.bottom-line {
  position: relative;
  top: 52px;
  width: 299px;
  border-bottom: 0.00521rem solid #e1e5e9;
}

::v-deep .table-list .el-table__body .el-table__row .el-table__cell {
  border-bottom: 1px solid #e5e7eb;
}
</style>
