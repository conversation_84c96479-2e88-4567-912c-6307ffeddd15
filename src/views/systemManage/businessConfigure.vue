<template>
  <div class="flex flex-col h-full w-full" v-loading="loading">
    <header class="h-48px w-full flex items-center justify-end mb-20px bg-white px-20px rounded-2px">
      <el-button type="primary" v-if="isEdit" @click="save">保存</el-button>
      <el-button v-if="isEdit" @click="getListData">取消</el-button>
      <!-- <el-button @click="reset">重置</el-button> -->
      <el-button type="primary" v-if="!isEdit" @click="isEdit = true" :icon="Edit">编辑</el-button>
    </header>

    <div class="flex-1 rcontent bg-white p-20px">
      <div class="title pb-20px">消息提醒设置:</div>
      <ul class="flex flex-col">
        <li>
          <span>火警提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.fireRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
        <li>
          <span>预警提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.warningRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
        <li>
          <span>动作提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.actionRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
        <li>
          <span>故障提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.faultRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
        <li>
          <span>隐患提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.hazardRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
        <li>
          <span>离线提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.offlineRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
        <li>
          <span>真警提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.realAlarmRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
        <li>
          <span>超期隐患提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.hazardTimeoutRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
        <li>
          <span>超期故障提醒</span>
          <el-radio-group :disabled="!isEdit" v-model="setting.faultTimeoutRemind">
            <el-radio label="1" size="large">开启</el-radio>
            <el-radio label="0" size="large">关闭</el-radio>
          </el-radio-group>
        </li>
      </ul>
      <div class="flex h-120px items-center">
        <div class="title">提醒提示音：</div>
        <custom-audio-player :notification_tone="configForm.notificationTone" />
        <el-upload :disabled="!isEdit" ref="uploadRef" class="upload-demo" action="" accept=".mp3,.wav,.m4a"
          :auto-upload="false" :limit="1" :on-change="handleUploadChange" :on-exceed="handleExceed"
          :show-file-list="false">
          <template #trigger>
            <el-button type="primary" :icon="Upload" class="mr-24px ml-24px" :disabled="!isEdit">点击上传</el-button>
          </template>
        </el-upload>
        <div style="color: #a8a8a8">
          <p>点击上传音效，支持MP3、WAV文件格式，大小限制在1M内,提示音</p>
          <p>仅在某类消息开启通知的情况下，有新消息时播放音效提醒。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import {
  ElMessage,
  UploadProps,
  UploadInstance,
  UploadRawFile,
  genFileId
} from 'element-plus'
import { Edit, Upload } from '@element-plus/icons-vue'
import { useUserInfo } from '@/store'
import $API from '~/common/api'
import customAudioPlayer from '~/components/systemManage/customAudioPlayer.vue'

const ui = useUserInfo()
const loading: any = ref(false)
const uploadRef = ref<UploadInstance>()
interface typeOp {
  [key: string]: any
}
let setting = reactive<typeOp>({
  realAlarmRemind: '0',
  faultRemind: '0',
  actionRemind: '0',
  warningRemind: '0',
  hazardRemind: '0',
  fireRemind: '0',
  offlineRemind: '0',
  hazardTimeoutRemind: '0'
})
let configForm = reactive<typeOp>({
  id: '',
  superviseUnitId: ui.value.orgCode,
  superviseUnitName: ui.value.orgName,
  file: null,
  notificationTone: '' //提示音频路径
})
const settingObj: any = ref({})
const configFormObj: any = ref({})

const handleUploadChange: UploadProps['onChange'] = (file: any, files: any) => {
  if (file.size >= 1024 * 1024) {
    ElMessage.error('文件大小不能超过1M！')
    uploadRef.value!.clearFiles()
    return false
  }
  configForm.file = file.raw
  const url = URL.createObjectURL(files[0].raw)
  configForm.notificationTone = url
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}

const isEdit = ref(false)

function cancel() {
  console.log('cancel')
}

function save(type?: string) {
  const obj = Object.assign({}, setting)
  for (let k in obj) {
    if (obj[k] === '0' || obj[k] === '1') {
      obj[k] = Number(obj[k])
    }
  }
  if (configForm.file) {
    configForm.notificationTone = ''
    uploadRef.value!.clearFiles()
  }
  const configObj = Object.assign({}, configForm)
  delete configObj.notificationTone
  let url = '/remindConf/saveOrUpdateRemind'
  loading.value = false
  const data = {
    ...obj,
    ...configObj
  }
  $API
    .formData({
      url,
      data: data
    })
    .then((res: any) => {
      loading.value = false
      if (res.code == 'success') {
        isEdit.value = false
        ElMessage({
          message: type === 'reset' ? '重置成功' : res.message,
          type: 'success'
        })
        getListData()
      } else {
        ElMessage({
          message: type === 'reset' ? '重置失败' : res.message,
          type: 'error'
        })
      }
    })
}

function reset() {
  configForm.notificationTone = JSON.parse(
    JSON.stringify(configFormObj.value.notificationTone)
  )
  configForm.file = null
  uploadRef.value!.clearFiles()
  for (const key in setting) {
    if (Object.prototype.hasOwnProperty.call(setting, key)) {
      setting[key] = '0'
    }
  }
  save('reset')
}

async function updateFormData(data: any) {
  configForm.id = data.id
  configForm.superviseUnitId = data.superviseUnitId
  configForm.notificationTone = data.notificationTone
    ? data.notificationTone
    : ui.value.notificationUrl
  const obj = Object.assign({}, data)
  delete obj.id
  delete obj.notificationTone
  delete obj.superviseUnitId
  delete obj.superviseUnitName
  await setFormData(obj, setting)
  configFormObj.value = Object.assign({}, configForm)
  settingObj.value = Object.assign({}, setting)
}

function setFormData(json: any, settingForm: any) {
  for (let k in json) {
    settingForm[k] = Number(json[k]) + ''
  }
}

function getListData() {
  loading.value = true
  $API
    .post({
      url: '/remindConf/findRemindConfig',
      params: {
        superviseUnitId: ui.value.orgCode
      }
    })
    .then((res: any) => {
      loading.value = false
      if (res && res.code == 'success') {
        updateFormData(res.data)
      } else {
        ElMessage({
          message: res.message,
          type: 'error'
        })
      }
      isEdit.value = false
    })
}

onMounted(() => {
  getListData()
})
</script>
<style lang="scss" scoped>
.title {
  color: #333333;
  font-size: 16px;
  font-family: 'Alibaba-PuHuiTi-Medium';
}

ul {
  align-items: flex-start;
  height: auto;

  li {
    display: flex;
    width: 340px;
    align-items: center;
    color: #666666;
    font-size: 16px;

    span {
      width: 32%;
      text-align: right;
      margin-right: 32px;
    }

    .el-radio-group {
      width: 51%;
    }

    :deep(.el-radio.el-radio--large .el-radio__inner) {
      height: 20px;
      width: 20px;
    }

    :deep(.el-radio__input.is-checked .el-radio__inner) {
      background-color: transparent;
    }

    :deep(.el-radio__input.is-disabled.is-checked .el-radio__inner) {
      border-color: #3093fa;
    }

    :deep(.el-radio__input.is-checked .el-radio__inner::after) {
      transform: translate(-50%, -50%) scale(3);
      background-color: #3093fa;
    }
  }
}

.upload-demo {
  position: relative;

  :deep(.el-upload-list) {
    position: absolute;
    left: -60px;
  }
}

.upload-btn {
  margin: auto 24px;
}
</style>
