<template>
  <div class="w-full h-full flex flex-col">
    <div class="flex-1 overflow-hidden">
      <table-list
        ref="tableListRef"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :show-overflow-tooltip="item.showOverflowTooltip"
        >
          <template #default="scope">
            {{ scope.row[item.key] || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <div class="table-operate">
              <span class="operate-item" track @click="preview(scope.row)"
                >预览</span
              >
              <span class="operate-item" track @click="downloadFile(scope.row)"
                >下载</span
              >
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
    <div class="contact-Us">
      <el-tooltip
        popper-class="tip"
        effect="dark"
        placement="bottom-end"
        :hide-after="500"
      >
        <template #content>
          <div class="px-15px pt-10px">
            <p>运维联系方式：</p>
            <p class="flex mb-10px mt-10px items-center">
              <img src="@/assets/image/phone.png" class="w-14px h-13px" />
              <span class="ml-8px">181-5606-7876</span>
            </p>
            <p class="flex mb-10px items-center">
              <img src="@/assets/image/phone.png" class="w-14px h-13px" />
              <span class="ml-8px">133-9151-9136</span>
            </p>
            <p class="flex mb-10px items-center">
              <img src="@/assets/image/phone.png" class="w-14px h-13px" />
              <span class="ml-8px">186-0551-8500</span>
            </p>
          </div>
        </template>
        <div class="inline-flex items-center">
          <img src="@/assets/image/phone.png" class="w-14px h-13px" />
          <div class="ml-8px">联系我们</div>
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserInfo } from '@/store'
import $API from '~/common/api'
import { downloadPdf } from '~/common/utils'
import config from '@/config/index'

const route = useRoute()
const router = useRouter()
const ui = useUserInfo()
const loading = ref<boolean>(false)
const pageModel = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  total: 0
})
const tableData = ref<any>([])
const columns = ref<any>([])

onMounted(() => {
  getcolumns()
  getTableData()
})

const preview = (row) => {
  if (row.superviseUserOperationUrl !== '') {
    console.log(config.base_host + row.superviseUserOperationUrl)
    window.open(config.base_host + row.superviseUserOperationUrl)
  }
}
const downloadFile = (row) => {
  if (row.superviseUserOperationUrl !== '') {
    downloadPdf(
      config.base_host + row.superviseUserOperationUrl,
      row.superviseUserOperationUrl,
      row.superviseUserOperationName
    )
  }
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
}

async function getTableData() {
  console.log(ui)
  try {
    const { pageNo, pageSize } = pageModel
    const res: any = await $API.post({
      url: '/operation/querySuperviseUserOperationList',
      params: {
        superviseId: ui.value.orgCode,
        pageNo,
        pageSize
      }
    })
    // -------------------
    res.data.rows.forEach((item) => (item.createTime = '2023-07-21 21:00:00'))
    // -------------------
    tableData.value = res.data.rows
    pageModel.total = res.data.total
  } catch (error) {}
}
const getcolumns = () => {
  columns.value = [
    {
      label: '名称',
      key: 'superviseUserOperationName',
      showOverflowTooltip: true
    }
    // {
    //   label: '创建时间',
    //   key: 'createTime',
    //   showOverflowTooltip: true
    // }
  ]
}
</script>
<style lang="scss" scoped>
.contact-Us {
  position: fixed;
  right: 20px;
  top: 57px;
  max-width: max-content;
  box-sizing: content-box;
  padding: 8px 20px;
  background: #ffffff;
  box-shadow: 1px 1px 5px 0px rgba(49, 49, 49, 0.1);
  border-radius: 4px;
  :deep(.tip img) {
    width: 13px;
    height: 14px;
  }
}
</style>
