<!-- 角色 -->
<template>
  <div class="w-full h-full flex flex-col">
    <div class="query-conditions-form-role">
      <header-item class="h-42px" title="角色名称" title-width-auto>
        <el-input v-model="queryParams.roleName" placeholder="请输入角色名称搜索" clearable @input="getTableData">
        </el-input>
      </header-item>
      <div class="export">
        <el-space :size="10">
          <el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
        </el-space>
      </div>
    </div>
    <div class="flex-1 overflow-hidden">
      <table-list @current-change="currentChange" @size-change="handleSizeChange" :data="tableData" v-loading="loading"
        :pageModel="pageModel" stripe>
        <el-table-column v-for="(item, index) in columns" :prop="item.key" :label="item.title"
          :show-overflow-tooltip="item.showOverflowTooltip" :key="index">
          <template #default="scope" v-if="item.key === 'action'">
            <div class="table-operate">
              <span :class="scope.row.isOperable == 0
                  ? 'operate-item'
                  : 'operate-disable1'
                " track @click="handleEdit(scope.row)">编辑</span>
              <span :class="scope.row.isOperable == 0
                  ? 'operate-item'
                  : 'operate-disable1'
                " track @click="handleAuthorization(scope.row)">授权</span>
              <span :class="scope.row.isOperable == 0
                  ? 'operate-item'
                  : 'operate-disable1'
                " track @click="handleDelete(scope.row)">删除</span>
            </div>
          </template>
          <template #default="scope" v-else>
            {{ scope.row[item.key] || '--' }}
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
  <popup-side v-model="popupShow" :popupTitle="detailTitle">
    <role-popup :currentType="currentType" :row="currentRow" @cancel="cancel">
    </role-popup>
  </popup-side>
  <popup-side v-model="popupShowAuth" :popupTitle="detailTitle">
    <auth-popup :currentType="currentType" :row="currentRow" @cancel="cancel">
    </auth-popup>
  </popup-side>
</template>

<script lang="ts" setup>
import rolePopup from '@/components/systemManage/rolePopup.vue'
import authPopup from '@/components/systemManage/authPopup.vue'
import { onMounted, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { serviceStatus } from '@/common/fixedOptions'
import { Plus, DocumentCopy } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo } from '@/store'
const ui = useUserInfo()
const router = useRouter()
const currentType = ref('')
const currentRow = ref({})
const popupShow: any = ref(false)
const popupShowAuth: any = ref(false)
const detailTitle: any = ref('')
const queryParams = reactive({})
const companyUnitOptions = ref<any>([])

const loading = ref<boolean>(false)
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})
const tableData = ref<any>([])
const columns = ref<any>([])

onMounted(() => {
  init()
})
function handleDelete(val: any) {
  if (val.isOperable != 0) return
  if (val.superviseUnitPid === '') return // 最高级别的组织机构,停用,删除按钮置灰,禁止操作
  const msg = '删除成功'
  const tit = '确定删除吗？'
  const tits = '删除'
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        $API
          .post({
            url: '/role/deleteRoleById',
            params: {
              roleId: val.id
            }
          })
          .then((data: any) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (data && data.code == 'success') {
              done()
              ElMessage({
                message: msg,
                type: 'success'
              })
              getTableData()
            } else {
              // ElMessage({
              //   message: data.message,
              //   type: 'error'
              // })
            }
          })
      } else {
        done()
      }
    }
  })
}
function handleAdd() {
  currentType.value = 'add'
  popupShow.value = true
  detailTitle.value = '新增'
  currentRow.value = {}
}
function handleAuthorization(row: any) {
  if (row.isOperable != 0) return
  currentType.value = 'authorization'
  popupShowAuth.value = true
  detailTitle.value = '授权'
  currentRow.value = row
}

function handleEdit(row: any) {
  if (row.isOperable != 0) return
  currentType.value = 'edit'
  popupShow.value = true
  detailTitle.value = '编辑'
  currentRow.value = row
}
function init() {
  getTableColumns()
  getTableData()
}
function cancel(param: any) {
  popupShow.value = param
  popupShowAuth.value = param
  getTableData()
}
function add() {
  router.push({
    path: '/fire-supervision/fire/fire-record/add'
  })
}

async function getTableData() {
  loading.value = true

  try {
    const url = '/role/findRolePagesByOrgCode'
    const params = {
      ...queryParams,
      superviseId: ui.value.orgCode,
      pageSize: pageModel.pageSize,
      pageNo: pageModel.pageNo
    }
    const res: any = await $API.post({ url, params })
    tableData.value = res.data.rows
    pageModel.total = res.data.total
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

// 翻页
function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

function getTableColumns() {
  columns.value = [
    {
      title: '角色名称',
      key: 'roleName',
      showOverflowTooltip: true
    },
    {
      title: '角色别名',
      key: 'roleAlias',
      showOverflowTooltip: true
    },
    {
      title: '备注',
      key: 'roleRemark',
      showOverflowTooltip: true
    },
    {
      title: '创建时间',
      key: 'createTime',
      showOverflowTooltip: true
    },
    {
      title: '操作',
      key: 'action'
    }
  ]
}
</script>
<style lang="scss" scoped>
.export {
  margin-bottom: 0px !important;
}
</style>
