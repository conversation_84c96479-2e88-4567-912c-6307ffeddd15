<template>
  <div class="w-full h-full video-control">
    <div class="bottom-line"></div>
    <div class="w-full flex h-full">
      <div class="video-tree flex flex-col">
        <div class="mb-20px org-title pl-20px pt-20px">组织机构</div>
        <div class="flex-1 overflow-auto pr-8px pl-10px pt-0px pb-0px">
          <el-tree ref="treeRef" highlight-current :props="defaultProps" :data="treeData"
            @current-change="currentChangeTree" :indent="8" :filter-node-method="filterNode" default-expand-all
            :expand-on-click-node="false" node-key="superviseId" :current-node-key="userInfo.orgCode">
            <template #default="{ node, data }">
              <div style="width: 80%" :class="searchFrom.orgCode == data.superviseId
                ? 'node-bg custom-tree-node p-5px flex'
                : 'custom-tree-node flex p-5px'
                ">
                <svg-icon :name="userInfo.orgName == node.label ? 'buildings' : 'document'
                  " :size="20" class="inline-block mr-5px">
                </svg-icon>
                <div style="width: 80%">
                  <myTooltip :str="node.label" />
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="flex-1 flex flex-col calc-tree-width h-full overflow-hidden">
        <div class="org-title2 bg-white p-20px">{{ userInfo.orgName }}</div>
        <div :class="advanced
          ? 'query-conditions-form-user h-105px'
          : 'query-conditions-form-user h-53px'
          ">
          <header-item :titleWidth="70" title="用户名">
            <el-input v-model="searchFrom.loginName" placeholder="请输入用户名" clearable class="flex-1 h-full"
              @input="getListData">
            </el-input>
          </header-item>

          <header-item title="手机号" :titleWidth="80">
            <el-input v-model="searchFrom.userTelphone" maxlength="11" placeholder="请输入手机号" clearable
              class="flex-1 h-full" @input="getListData" />
          </header-item>
          <header-item title="用户姓名" :titleWidth="80">
            <el-input v-model="searchFrom.userName" placeholder="请输入姓名" clearable class="flex-1 h-full"
              @input="getListData">
            </el-input>
          </header-item>

          <header-item :titleWidth="70" v-show="advanced" title="用户角色">
            <el-select v-model="searchFrom.roleId" placeholder="全部" clearable fit-input-width class="h-full"
              @change="getListData">
              <el-option v-for="item in roleOptions" :key="item.id" :title="item.roleName" :label="item.roleName"
                :value="item.id" />
            </el-select>
          </header-item>
          <header-item :titleWidth="70" v-show="advanced" :span="6" title="用户状态">
            <el-select v-model="searchFrom.userStatus" placeholder="全部" clearable class="h-full" @change="getListData">
              <el-option v-for="item in authStatusOptions" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </header-item>
          <div class="export">
            <el-button type="primary" class="export-btn" :icon="Plus" @click="handleAdd">新增</el-button>
          </div>
          <span class="mb-22px ml-8px cursor-pointer" style="color: #4187f9" track @click="advanced = !advanced">
            {{ advanced ? '收起' : '展开' }}
            <el-icon v-if="advanced" color="#4187F9">
              <ArrowUp />
            </el-icon>
            <el-icon v-else color="#4187F9">
              <ArrowDown />
            </el-icon>
          </span>
        </div>
        <div class='table-box h-[calc(100%-160px)]'>
          <table-list @current-change="currentChange" @size-change="handleSizeChange" :data="tableData"
            v-loading="loading" :pageModel="pageModel" stripe>
            <el-table-column prop="loginName" label="用户名" show-overflow-tooltip />
            <el-table-column prop="userTelphone" label="手机号" show-overflow-tooltip />
            <el-table-column prop="userName" label="用户姓名" show-overflow-tooltip />
            <el-table-column prop="roleName" label="用户角色" show-overflow-tooltip />
            <el-table-column prop="orgName" label="所属单位" show-overflow-tooltip />
            <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
            <el-table-column prop="lastLoginTime" label="最后登录时间" show-overflow-tooltip />
            <el-table-column prop="" label="用户状态">
              <template #default="scope">
                <div>
                  <span :class="scope.row.userAuthStatus == '0'
                    ? 'user-status'
                    : 'user-status-grey'
                    "></span>
                  {{
                    scope.row.userAuthStatus == '0'
                    ? '正常'
                    : scope.row.userAuthStatus == '1'
                      ? '已注销'
                      : scope.row.userAuthStatus == '2'
                        ? '已停用'
                        : '--'
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="" label="操作" min-width="240px">
              <template #default="scope">
                <div class="table-operate">
                  <span class="operate-item" track :class="{
                    'operate-disable-single': scope.row.userAuthStatus == '2'
                  }" @click="resetPasswd(scope.row)">重置密码</span>
                  <span class="operate-item" track @click="loginLog(scope.row)">登录日志</span>
                  <span class="operate-item" track :class="{
                    'operate-disable-single': scope.row.userAuthStatus == '2'
                  }" @click="viewDetalils(scope.row)">详情</span>
                  <span class="operate-item" track :class="{
                    'operate-disable-single': scope.row.userAuthStatus == '2'
                  }" @click="handleEdit(scope.row)">编辑</span>
                  <!-- <span class="operate-item" track
                                    :class="{ 'operate-disable-single': scope.row.userAuthStatus == '1' }"
                                    @click="logOff(scope.row)">注销</span> -->
                  <span class="operate-item" track v-if="['2', '0'].includes(scope.row.userAuthStatus)"
                    @click="lockOrUnlockUser(scope.row)">{{ scope.row.userAuthStatus == '0' ? '停用' : '启用' }}</span>
                  <!-- <span class="operate-item" track v-if="['1'].includes(scope.row.userAuthStatus)"
                                                @click="recover(scope.row)">恢复</span> -->
                </div>
              </template>
            </el-table-column>
          </table-list>

        </div>


      </div>
    </div>
  </div>
  <popup-side v-model="popupShow" :popupTitle="detailTitle">
    <add-popup :code="searchFrom.orgCode" :currentType="currentType" :row="currentRow" @cancel="cancel">
    </add-popup>
    <!-- :roleList="roleOptions" -->
  </popup-side>
  <popup-side v-model="popupShowLog" :popupTitle="detailTitle">
    <table-list class="login-log h-full" @current-change="currentChange1" @size-change="handleSizeChange1"
      :data="loginLogData" v-loading="loadingLog" :pageModel="pageModel1" :pagerCount="3" stripe>
      <el-table-column prop="userName" label="登录账号用户姓名" show-overflow-tooltip />
      <el-table-column prop="loginTime" label="登录时间" show-overflow-tooltip />
    </table-list>
  </popup-side>
</template>

<script lang="ts" setup>
import addPopup from '@/components/systemManage/addPopup.vue'
// import { Search } from '@element-plus/icons-vue';
import $API from '~/common/api'
import { onMounted, reactive, ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  DocumentCopy,
  Search,
  ArrowDown,
  ArrowUp
} from '@element-plus/icons-vue'
import { ROLE_GROUP } from '~/common/eventType'
// import func from '../../../vue-temp/vue-editor-bridge'
import { PageModel } from '@/types'
import { useUserInfo } from '@/store'
const ui = useUserInfo()
const advanced = ref<boolean>(true)
interface typeOp {
  [key: string]: any
}
const defaultProps = {
  children: 'child',
  label: 'superviseName'
}
const currentType = ref('')
const currentRow = ref({})
const popupShow: any = ref(false)
const popupShowLog: any = ref(false)
const detailTitle: any = ref('')
const roleOptions: any = ref<typeOp[]>([])
const authStatusOptions = [
  {
    id: 0,
    label: '正常'
  },
  // {
  //   id: 1,
  //   label: '已注销'
  // },
  {
    id: 2,
    label: '已停用'
  }
]
const userInfo = useUserInfo()

const treeData = ref([] as any[])

const videoList = ref([{}, {}, {}, {}] as any[])

const videoMap: any = {}

const searchStr = ref('')

const treeRef = ref()

const deviceAddressMap = {}

const cells = [
  {
    className: 'icon-sigongge',
    value: 4
  },
  {
    className: 'icon-jiugongge',
    value: 9
  }
]

const isActive: any = ref('0')
function hidden_button(val: any) {
  isActive.value = val
}
function handleAdd() {
  currentType.value = 'add'
  popupShow.value = true
  detailTitle.value = '新增账号'
  currentRow.value = {}
}
function handleEdit(row: any) {
  if (row.userAuthStatus == '1') return
  currentType.value = 'edit'
  popupShow.value = true
  detailTitle.value = '编辑账号'
  currentRow.value = row
}
function viewDetalils(row: any) {
  if (row.userAuthStatus == '1') return
  currentType.value = 'details'
  popupShow.value = true
  detailTitle.value = '详情'
  currentRow.value = row
}
function loginLog(row: any) {
  currentType.value = 'log'
  detailTitle.value = '登录日志'
  popupShowLog.value = true
  currentRow.value = row
  getLoginLogData()
}
function changeStatus(val, status) {
  const msg = status == '1' ? '启用成功' : '停用成功'
  const tit = status == '1' ? '确定启用吗？' : '确定停用吗？'
  const tits = status == '1' ? '启用' : '停用'
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        $API
          .post({
            url: '/duty/plan/enable',
            params: {
              planId: val.planId,
              planState: status
            }
          })
          .then((data: any) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (data && data.code == 'success') {
              done()
              ElMessage({
                message: msg,
                type: 'success'
              })
              // initData();
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
      } else {
        done()
      }
    }
  })
}
function resetPasswd(data: any) {
  if (data.userAuthStatus == '1') return
  ElMessageBox.confirm('确定重置密码?', '重置密码', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    $API
      .post({
        url: '/userManage/resetPassword',
        params: {
          userId: data.id,
          updateUserId: userInfo.value.userId
        }
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          ElMessage({
            message: '密码重置成功！',
            type: 'success'
          })
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      })
  })
}

function logOff(data: any) {
  if (data.userAuthStatus == '1') return
  // if (data.userStatus == "2") return
  ElMessageBox.confirm('确定注销?', '注销用户', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    $API
      .post({
        url: '/userManage/updateUserState',
        params: {
          flag: 1,
          userId: data.id,
          updateUserId: ui.value.id,
          superviseId: searchFrom.orgCode
        }
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          ElMessage({
            message: '注销成功！',
            type: 'success'
          })
          getListData()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      })
  })
}

function lockOrUnlockUser(data: any) {
  const flag = data.userAuthStatus == '0' ? '停用' : '启用'
  const opertationCodeMapping = {
    停用: 2,
    启用: 4
  }
  ElMessageBox.confirm(`确定${flag}?`, `${flag}用户`, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    $API
      .post({
        url: '/userManage/updateUserState',
        params: {
          flag: opertationCodeMapping[flag],
          userId: data.id,
          updateUserId: ui.value.id,
          superviseId: searchFrom.orgCode
        }
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          ElMessage({
            message: `${flag}成功！`,
            type: 'success'
          })
          getListData()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      })
  })
}

function recover(data: any) {
  const flag = '恢复'
  ElMessageBox.confirm(`确定${flag}?`, `${flag}用户`, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    $API
      .post({
        url: '/userManage/updateUserState',
        params: {
          flag: 3,
          userId: data.id,
          updateUserId: ui.value.id,
          superviseId: searchFrom.orgCode
        }
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          ElMessage({
            message: `${flag}成功！`,
            type: 'success'
          })
          getListData()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      })
  })
}
function cancel(param: any) {
  popupShow.value = param
  getListData()
}
const cellActiveIndex = ref(0)

const cellValue = ref(cells[0].value)

function handleCellClick(item, index) {
  cellActiveIndex.value = index
  videoList.value = Array.from({ length: item.value }).map(() => ({}))
}

async function currentChangeTree(data, node) {
  searchFrom.orgCode = data.superviseId
  roleOptions.value = await getRoleList()
  getListData()
}

watch(searchStr, (val) => {
  treeRef.value!.filter(val)
})

function filterNode(value: string, data) {
  if (!value) return true
  return data.label.includes(value)
}

let tempData: any = null

function getTreeList() {
  $API
    .post({
      url: '/organize/getSuperviseUnitTree',
      params: {
        superviseUnitId: ui.value.orgCode,
        serverStatus: '0'
      }
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        treeData.value = [res.data]
      }
    })
}

const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})
const pageModel1: PageModel = reactive({
  pageNo: 1,
  pageSize: 30,
  total: 0
})
const searchFrom = reactive({
  clntType: '4',
  loginName: '',
  orgCode: ui.value.orgCode,
  roleId: '',
  userName: '',
  userStatus: '',
  userTelphone: ''
})
const loading: any = ref(false)
const loadingLog: any = ref(false)
const tableData: any = ref()
const loginLogData: any = ref()
function getListData() {
  loading.value = true
  $API
    .post({
      url: '/userManage/findUserListByParams',
      params: Object.assign({
        ...searchFrom,
        pageSize: pageModel.pageSize,
        pageNo: pageModel.pageNo
      })
    })
    .then((res: any) => {
      loading.value = false
      tableData.value = res.data.rows
      pageModel.total = res.data.total
    })
}
function getLoginLogData() {
  loadingLog.value = true
  $API
    .post({
      url: '/userManage/queryUserLog',
      params: Object.assign({
        clntType: '4',
        loginName: currentRow.value.loginName,
        orgCode: searchFrom.orgCode,
        pageSize: pageModel1.pageSize,
        pageNo: pageModel1.pageNo
      })
    })
    .then((res: any) => {
      loadingLog.value = false
      loginLogData.value = res.data.rows
      pageModel1.total = res.data.total
    })
}

async function getRoleList() {
  const result: any = await $API.post({
    url: '/userManage/querySysRoles',
    params: {
      clntType: '4',
      orgCode: searchFrom.orgCode,
      // orgCode: ui.value.orgCode,
      sysType: '1'
    }
  })
  if (result && result.code == 'success') {
    return result.data.filter((item) => item.id !== ROLE_GROUP.MANAGE_ADMIN)
  } else {
    ElMessage({
      message: result.message,
      type: 'error'
    })
  }
  return []
}
onMounted(async () => {
  roleOptions.value = await getRoleList()
  getTreeList()
  getListData()
})

function currentChange(pageNo: number) {
  console.log('改变后的页码：' + pageNo)
  pageModel.pageNo = pageNo
  getListData()
}
function currentChange1(pageNo: number) {
  console.log('改变后的页码：' + pageNo)
  pageModel1.pageNo = pageNo
  getLoginLogData()
}
// 翻页
function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getListData()
}
function handleSizeChange1(pageSize: number) {
  pageModel1.pageNo = 1
  pageModel1.pageSize = pageSize
  getLoginLogData()
}
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss" scoped>
:deep(.str-span) {
  padding-top: 3px;
}

.node-bg {
  background: #edf6ff;
  border-radius: 6px;
}

:deep(.login-log.table-list .table-list_wrap) {
  position: relative;
  padding: 0 0 0.28646rem 0;
}

.user-status-grey {
  margin-bottom: 0.01042rem;
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  border-radius: 50%;
  background: #bfbfbf;
}

.user-status {
  margin-bottom: 0.01042rem;
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  border-radius: 50%;
  background: #52c41a;
}

// .org-title1 {
//   text-indent: 20px;
//   font-size: 16px;
//   padding-bottom: 15px;
// }
.org-title2 {
  text-indent: 20px;
  font-size: 16px;
  padding-bottom: 15px;
  border-bottom: 0.00521rem solid #e1e5e9;
}

.bottom-line {
  position: relative;
  top: 52px;
  width: 299px;
  border-bottom: 0.00521rem solid #e1e5e9;
}

.hidden_button {
  background: rgba(48, 147, 250, 0.1);
  color: #3e9bfa;
  border: 0;
}

.active {
  background: #3e9bfa;
  color: #eee;
}

.video-control {
  .switch-gongge {
    i {
      margin-left: 10px;
      font-size: 20px;
      color: #cdeafd;
      cursor: pointer;
    }

    i.active {
      color: #448ef7;
    }
  }

  .video-tree {
    width: 300px;
    margin-right: 20px;
    height: 100%;
    background: white;
    border-radius: 6px;
    // padding: 20px;
    box-sizing: border-box;

    .org-title {
      text-indent: 20px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #000000;
      padding-bottom: 15px;
      font-size: 16px;
      // border-bottom: 1px solid gray;
      // border-bottom: 0.00521rem solid #e1e5e9;
    }

    //  .org-title {
    //     padding-bottom: 15px;
    //     border-bottom: 0.00521rem solid #E1E5E9;
    // }
    :deep(.el-tree) {
      .el-tree-node__content {
        height: 40px;
        border-radius: 6px;
        margin-top: 4px;

        .el-tree-node__label {
          font-size: 16px;
        }

        .el-tree-node__expand-icon {
          font-size: 15px;

          svg {
            width: 15px;
            height: 15px;
          }
        }
      }
    }
  }

  .four-cell {
    .video-group_item {
      width: 50%;
      height: 50%;
    }
  }

  .nine-cell {
    .video-group_item {
      width: 33.33%;
      height: 33.33%;
    }
  }

  .video-group {
    background: white;
    padding: 6px;
    box-sizing: border-box;
    border-radius: 6px;

    .is-full-screen {
      position: absolute !important;
      z-index: 100;
      left: 0;
      top: 0;
    }

    .video-group_item {
      padding: 6px;
      box-sizing: border-box;

      .video-wrap {
        background: #333333;
        width: 100%;
        height: 100%;
        position: relative;

        .video-address {
          background: rgba(0, 0, 0, 0.5);
          color: white;
          height: 40px;
          line-height: 40px;
          padding: 0 20px;
          z-index: 10;

          svg {
            height: inherit;
            width: inherit;
          }
        }

        .full-screen-btn {
          background: white;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-items: center;
          position: absolute;
          right: 10px;
          bottom: 10px;
          cursor: pointer;
          border-radius: 4px;
        }
      }
    }
  }
}

::v-deep .table-list .el-table__body .el-table__row .el-table__cell {
  border-bottom: 1px solid #e5e7eb;
}

// :deep(.table-list .table-list_wrap) {
//   padding: 0 0 0.28646rem 0;
// }
</style>
