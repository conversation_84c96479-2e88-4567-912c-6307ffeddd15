<template>
  <el-button
    :loading="loading"
    class="mb-30px mt-40px"
    type="primary"
    @click="showFlg = !showFlg"
    >测试</el-button
  >
  <div class="detail-box">
    <!-- <unitDetail v-if="showFlg" @close="close"></unitDetail> -->
    <!-- <linkage></linkage> -->

    <unitDetail
      :pointData="pointData"
      v-if="showFlg"
      @close="close"
    ></unitDetail>
  </div>
</template>

<script lang="ts" setup>
import unitDetail from '@/components/dwfbYzt/unitPointDetail.vue'
import linkage from '../components/gisPage/floorTip/linkage.vue'
import { ref, computed, onMounted } from 'vue'
import { useUserInfo, useCounterStore } from '~/store'
import $API from '~/common/api'
const showFlg = ref<Boolean>(false)
const listData: any = ref()
const pointData: any = ref({
  eventSourceId: '6310738ce4b09242ad422f8d',
  ownerType: 0,
  serviceModelName: '全托管模式',
  fireStateStr: '待处置',
  standardInfo: '',
  operationState: 0,
  ownerId: 'AHHF_QHHFY_20180408',
  operationStateStr: '待处置',
  deviceId: '6310738ce4b09242ad422f8d',
  deviceName: '独立式电气火灾监控装置',
  floorAreaImg: 'AHHF_QHHFY_20180408/001/U001/1.jpg',
  floorId: 'AHHF_QHHFY_20180408_001_U001',
  reachState: 0,
  produceInfo:
    '{"brand": "河南立安", "model": "hnla", "brand_id": "1560462899751985153", "model_id": "6305d63be4b02c1dcaf704b5"}',
  isAerialMap: 1,
  eventDesc: '剩余电流预警',
  opFeedbackStatus: 0,
  unitTypeName: '企业单位',
  opFeedbackTime: '',
  monitoringInfo: '',
  unitPointY: 3710320.81,
  serviceModelCode: 3,
  unitPointX: 13055357.53,
  resetTime: '',
  longitude: 13055365.482825866,
  eventId: '',
  tocNoticeState: 0,
  cardInfo: '',
  unitName: '清华大学合肥公共安全研究院',
  eventSourceType: 0,
  deviceTypeName: '独立式电气火灾监控装置',
  eventType: 2,
  manufacturerCode: 'hnla',
  tocNoticeTime: '',
  unitAddress: '经开区习友路5999号',
  buildingName: '指挥中心大楼（1号楼）',
  deviceClassification: 3,
  disposeInstanceId: 398981,
  warningRank: 3,
  fireState: 0,
  deviceTypePid: '23000000',
  deviceTypeId: '23050000',
  videoLatitude: '',
  laMake: '',
  twoCode: '',
  latitude: 3710224.426027195,
  houseNumber: '',
  subCenterName: '总部（合肥）运营中心',
  deviceTime: '2022-11-25 11:01:02',
  disposeWorkflowKey: 'tanzer_warn_event_flow_chart',
  fireDisposeTime: '',
  mapZ: 0,
  mapY: 3710248.9958410673,
  mapX: 13055395.15797717,
  deviceNum: '202209011435003',
  unitType: 0,
  fireResult: 0,
  deviceAddress: '楼梯口',
  installInfo: '{"install_date": "2022-09-01"}',
  eventTime: '2022-11-25 11:01:02',
  deviceTypePname: '消防物联网独立式消防设备',
  deviceOnlyId: '7d3c015efd1384d2e347af421271635e',
  unitId: 'AHHF_QHHFY_20180408',
  laPoint: '',
  opNoticeStateStr: '待告警',
  timeInterval: 0,
  floorName: '1层',
  fireResultStr: '待核警',
  subordinateUnits: '',
  operationResult: 0,
  disposeState: 0,
  operationResultStr: '待核警',
  videoLongitude: '',
  subCenterCode: '340100YYZX201805230001',
  resetState: 0,
  channelNum: '',
  updateTime: '2022-10-14 09:38:44',
  disposeFinishTime: '',
  disposeId: '1595976067639353346',
  opNoticeState: 0,
  buildingId: 'AHHF_QHHFY_20180408_001',
  createTime: '2022-09-01 16:55:40',
  disturbState: 1,
  videoMapZ: 0,
  videoMapY: '',
  videoMapX: '',
  lastEventTime: '2023-02-07 16:51:33',
  useInfo: '',
  priorityEventType: '2',
  keyPartId: '',
  lastDeviceTime: '2023-02-07 16:51:33',
  laLoop: ''
})
function submit() {
  showFlg.value = true
}
function close() {
  showFlg.value = false
}
onMounted(async () => {})
</script>

<style>
.detail-box {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 4;
}
</style>
