<!-- details -->
<template>
  <div class="details h-full" v-loading="loading" v-if="!isWithdraw">
    <div class="item">
      <h3>待办标题：</h3>
      <div class="mb-15px mt-8px">
        {{ info.pendingTitle }}
      </div>
      <!-- <ul>
        <li class="line-item">
          <span class="left-content">待办接收时间：</span>
          <span class="right-content"> {{ info.createTime }} </span>
        </li>
        <li class="line-item">
          <span class="left-content">待办截止时间：</span>
          <span class="right-content"> {{ info.endTime }} </span>
        </li>
      </ul> -->
      <h3>待办内容：</h3>
      <div
        class="pendingContent mb-15px mt-8px"
        v-html="info.pendingContent"
      ></div>
    </div>
    <div class="item">
      <h3>待办完成情况：</h3>
      <div class="completeState mt-8px">
        <span v-for="(itemC, indexC) in info.completedUnit" :key="indexC">
          <el-icon color="#67C23A" :size="16"><CircleCheckFilled /></el-icon>
          <span class="mx-2px">{{ itemC }}</span>
        </span>
      </div>
      <div class="notCompletedUnit">
        <span v-for="(itemN, indexN) in info.notCompletedUnit" :key="indexN">
          <el-icon color="#ff4545" :size="16"><WarningFilled /></el-icon>
          <span class="mx-2px">{{ itemN }}</span>
        </span>
      </div>
    </div>
  </div>
  <noData v-else labelTitle="发布者已删除内容，无法查看！"></noData>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import {
  queryPendingDetailsById,
  queryPendingStatusById
} from '@/common/comResponse/index'
import { CircleCheckFilled, WarningFilled } from '@element-plus/icons-vue'
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const isWithdraw = ref(false)
const info: any = ref({
  notCompletedUnit: [],
  completedUnit: []
})
const loading = ref(false)
const getPendingDetailsById = async () => {
  loading.value = true
  const request1: any = queryPendingDetailsById({
    pendingId: props.id
  })
  const request2: any = queryPendingStatusById({
    pendingId: props.id
  })
  Promise.all([request1, request2])
    .then((res) => {
      if (res && res.length > 0) {
        info.value = { ...res[0] }
        if (info.value.publishStatus == -1) isWithdraw.value = true
        if (res[1].notCompletedUnit)
          info.value.notCompletedUnit = res[1].notCompletedUnit
        if (res[1].completedUnit)
          info.value.completedUnit = res[1].completedUnit
      }
      console.log(res, '******res********', info.value)
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  getPendingDetailsById()
})
</script>

<style lang="scss" scoped>
.details {
  .item {
    h3 {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      line-height: 22px;
    }
    > div {
      line-height: 22px;
    }

    > ul .line-item > span {
      color: #777;
      font-size: 12px;
    }

    > .pendingContent {
      margin-bottom: 20px;
    }
    .completeState,
    .notCompletedUnit {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      > span {
        // width: 25%;
        padding: 5px;
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>
