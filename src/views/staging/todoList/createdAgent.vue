<!-- createdAgent -->
<template>
  <div class="w-full h-full flex flex-col createdAgent">
    <div class="main_search">
      <!-- <header-item title="状态">
        <equipmentStatus
          :options="pendingStateOptions"
          v-model="searchFrom.pendingState"
          @change="init"
        />
      </header-item> -->

      <header-item title="创建时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY-MM-DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>
      <div class="export action" style="margin-bottom: 0">
        <el-button
          type="primary"
          class="export-btn"
          @click="$router.push('/staging/todoList/add')"
        >
          新增待办
        </el-button>
      </div>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span
                class="operate-item"
                track
                @click="handleDetails(scope.row)"
              >
                详情
              </span>
              <span
                v-if="scope.row.publishStatus == '1'"
                class="operate-item"
                track
                @click="withdrawHandle(scope.row)"
              >
                撤回
              </span>
              <span
                v-if="scope.row.publishStatus == '0'"
                class="operate-item"
                track
                @click="editHandle(scope.row)"
              >
                编辑
              </span>
              <span
                v-if="scope.row.publishStatus == '0'"
                class="operate-item"
                track
                @click="deleteHandle(scope.row)"
              >
                删除
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
    <popup-side v-model="addPopupShow" popupTitle="创建待办" @close="close">
      <addAgent ref="addAgentRef" @cancel="addAgentCancel"></addAgent>
      <template #footer>
        <div class="popup-side-footer">
          <el-button @click="addPopupShow = false">取消</el-button>
          <el-button type="primary" :loading="btnLoading" @click="save">
            确认
          </el-button>
        </div>
      </template>
    </popup-side>

    <popup-side v-model="popupShow" popupTitle="待办详情" @close="close">
      <agencyDetails :id="id"></agencyDetails>
      <template #footer>
        <div class="popup-side-footer">
          <el-button @click="popupShow = false">关闭</el-button>
        </div>
      </template>
    </popup-side>
  </div>
</template>

<script lang="ts" setup>
import equipmentStatus from '~/components/public/common-form/equipmentStatusSelect.vue'
import addAgent from '@/views/staging/todoList/add.vue'
import agencyDetails from '@/views/staging/todoList/details.vue'

import { onMounted, ref, reactive, toRaw, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '@/common/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import { PageModel } from '@/types'
import { useUserInfo, useTodoListInfo } from '@/store'
import {
  queryPendingByCreate,
  withdrawPending,
  delPending
} from '@/common/comResponse/index'

const userInfo = useUserInfo()
const route: any = useRoute()
const router: any = useRouter()
const todoListInfo = useTodoListInfo()

const props = defineProps({
  disposeId: {
    type: String,
    default: ''
  },
  propSearch: {
    type: Object,
    default: {}
  }
})

const pendingStateOptions = ref<any>([
  {
    label: '未完成',
    value: '0'
  },
  {
    label: '已完成',
    value: '1'
  }
])

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const chargeId: any = ref('') // 分管单位id
const btnLoading: any = ref(false)

const searchFrom = reactive({
  // pendingState: '', // 状态
  orderFields: '',
  createEndTime: '', //结束时间
  createStartTime: '', //开始时间
  createUserId: userInfo.value.userId
})

// 离线时间
const timeAll: any = ref([])
// timeAll.value = $API.getDays(30)
// searchFrom.createStartTime = timeAll.value[0] + ' 00:00:00'
// searchFrom.createEndTime = timeAll.value[1] + ' 23:59:59'

const popupShow = ref(false)
const addPopupShow = ref(false)

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

function close() {
  popupShow.value = false
}

function addAgentCancel(bool: boolean) {
  addPopupShow.value = bool
  init()
}

/**
 * 点击详情按钮
 * @param item 当前点击的表格的行数据
 */
const id: any = ref('')
function handleDetails(item: any) {
  console.log(item, 'item')
  id.value = item.pendingId
  popupShow.value = true
}

/**
 * 撤回
 */
const withdrawHandle = (item) => {
  ElMessageBox({
    title: '',
    type: 'warning',
    message: '撤回后其他用户无法查看该内容，您可以在草稿箱进行编辑修改',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await withdrawPending({
          pendingId: item.pendingId,
          createUserId: item.createUserId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '撤回成功！',
            type: 'success'
          })
          done()
          init()
        } else {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
          done()
        }
      } else {
        done()
      }
    }
  })
}

// 编辑
const editHandle = (item) => {
  todoListInfo.value = item
  router.push(
    '/staging/todoList/edit?currentType=edit&source=createdNotification'
  )
}

/**
 * 删除待办通告
 */
const deleteHandle = (item) => {
  ElMessageBox({
    title: '删除',
    type: 'warning',
    message: '确认删除待办吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await delPending({
          pendingId: item.pendingId,
          createUserId: item.createUserId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '删除成功！',
            type: 'success'
          })
          done()
          init()
        } else {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
          done()
        }
      } else {
        done()
      }
    }
  })
}

onMounted(() => {
  init()
  getTableColumns()
})

function timeChange(val: any) {
  searchFrom.createEndTime = ''
  searchFrom.createStartTime = ''
  if (val && val.length > 0) {
    searchFrom.createStartTime = val[0] + ' 00:00:00'
    searchFrom.createEndTime = val[1] + ' 23:59:59'
  }
  init()
}

const addAgentRef: any = ref(null)

const save = () => {
  console.log(addAgentRef.value)
  addAgentRef.value.submitForm()
}

function init() {
  getTableData()
}

async function getTableData() {
  loading.value = true
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await queryPendingByCreate(params)
  if (res && res.code == 'success') {
    console.log(res, '获取收到的待办列表')
    tableData.value = res.data.rows || []
    tableData.value.forEach((item: any) => {
      const currentPendingState = pendingStateOptions.value.find(
        (el) => el.value && el.value == item.pendingState
      )
      if (currentPendingState) item.pendingStateStr = currentPendingState.label
    })
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

const add = () => {}

function getTableColumns() {
  columns.value = [
    {
      title: '待办标题',
      key: 'pendingTitle',
      showOverflowTooltip: true
    },
    {
      title: '执行单位',
      key: 'execUnits',
      showOverflowTooltip: true
    },
    {
      title: '创建时间',
      key: 'createTime',
      showOverflowTooltip: true
    },
    {
      title: '截止时间',
      key: 'endTime',
      showOverflowTooltip: true
    },
    {
      title: '发布状态',
      key: 'publishStatusStr',
      showOverflowTooltip: true
    }
  ]
}
</script>
<style lang="scss" scoped></style>
