<!-- receiveAgent -->
<template>
  <div class="w-full h-full flex flex-col receiveAgent">
    <div class="main_search">
      <header-item title="状态">
        <equipmentStatus
          :options="pendingStateOptions"
          v-model="searchFrom.pendingState"
          @change="init"
        />
      </header-item>

      <header-item title="接收时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY-MM-DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="item.key === 'pendingStateStr'">
              <el-switch
                v-model="scope.row.pendingState"
                :inactive-value="1"
                :active-value="2"
                @click="
                  switchChange(scope.row.pendingState, scope.row.pendingId)
                "
              />
              <span
                class="ml-10px"
                :class="scope.row.pendingState == '2' ? 'green' : 'red'"
              >
                {{ scope.row.pendingState == '2' ? '已完成' : '未完成' }}
              </span>
            </template>
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span
                class="operate-item"
                track
                @click="handleDetails(scope.row)"
              >
                详情
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
    <popup-side v-model="popupShow" popupTitle="待办详情" @close="close">
      <agencyDetails :id="id"></agencyDetails>
      <template #footer>
        <div class="popup-side-footer">
          <el-button @click="popupShow = false">关闭</el-button>
        </div>
      </template>
    </popup-side>
  </div>
</template>

<script lang="ts" setup>
import equipmentStatus from '~/components/public/common-form/equipmentStatusSelect.vue'
import agencyDetails from '@/views/staging/todoList/details.vue'

import { onMounted, ref, reactive, computed, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo, updatePopupShow } from '@/store'
import {
  queryPendingByReceive,
  updatePendingState
} from '@/common/comResponse/index'

const userInfo = useUserInfo()
const route: any = useRoute()

const id: any = ref('')
if (route.query.pendingId) {
  id.value = route.query.pendingId
}

const pendingStateOptions = ref<any>([
  {
    label: '未完成',
    value: '1'
  },
  {
    label: '已完成',
    value: '2'
  }
])

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const searchFrom = reactive({
  pendingState: '', // 处置状态
  publishStartTime: '', //接收时间-开始时间
  publishEndTime: '', //接收时间-结束时间
  superviseId: userInfo.value.orgCode // 接口请求把分管单位id赋值给该参数
})

const timeAll: any = ref([])
// timeAll.value = $API.getDays(30)
// searchFrom.publishStartTime = timeAll.value[0] + ' 00:00:00'
// searchFrom.publishEndTime = timeAll.value[1] + ' 23:59:59'

const popupShow = updatePopupShow()

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

function close() {
  popupShow.value = false
}

const switchChange = async (val, pendingId) => {
  console.log(val, '**********')
  console.log(pendingId)
  const res: any = await updatePendingState({
    pendingId: pendingId,
    pendingState: val,
    superviseId: userInfo.value.orgCode
  })
  console.log(res)
}

/**
 * 点击详情按钮
 * @param item 当前点击的表格的行数据
 */

function handleDetails(item: any) {
  console.log(item, 'item')
  id.value = item.pendingId
  popupShow.value = true
}

onMounted(() => {
  init()
  getTableColumns()
})

onUnmounted(() => {
  popupShow.value = false
})

function timeChange(val: any) {
  searchFrom.publishStartTime = ''
  searchFrom.publishEndTime = ''
  if (val && val.length > 0) {
    searchFrom.publishStartTime = val[0] + ' 00:00:00'
    searchFrom.publishEndTime = val[1] + ' 23:59:59'
  }
  init()
}

function init() {
  getTableData()
}

async function getTableData() {
  loading.value = true
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await queryPendingByReceive(params)
  if (res && res.code == 'success') {
    console.log(res, '获取收到的待办列表')
    tableData.value = res.data.rows || []
    tableData.value.forEach((item: any) => {
      const currentPendingState = pendingStateOptions.value.find(
        (el) => el.value && el.value == item.pendingState
      )
      if (currentPendingState) item.pendingStateStr = currentPendingState.label
    })
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

function getTableColumns() {
  columns.value = [
    {
      title: '待办标题',
      key: 'pendingTitle',
      showOverflowTooltip: true
    },
    {
      title: '接收时间',
      key: 'publishTime',
      showOverflowTooltip: true
    },
    {
      title: '截止时间',
      key: 'endTime',
      showOverflowTooltip: true
    },
    {
      title: '是否完成',
      key: 'pendingStateStr',
      showOverflowTooltip: true
    }
  ]
}
</script>
<style lang="scss" scoped>
.green {
  color: #52c41a;
}

.red {
  color: #f5222d;
}
</style>
