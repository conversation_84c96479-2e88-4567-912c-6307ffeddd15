<template>
  <div class="formView h-full">
    <h3 class="text-16px font-bold mb-20px">
      {{ currentType === 'edit' ? '编辑' : '创建' }}待办
    </h3>
    <el-form
      ref="formRef"
      :model="todoListForm"
      :rules="rules"
      :label-width="95"
      :label-position="labelPosition"
      class="bg-white w-full"
    >
      <el-row class="!mb-0px">
        <el-col :span="7">
          <el-form-item label="待办标题" :label-width="95" prop="pendingTitle">
            <el-input
              v-model="todoListForm.pendingTitle"
              maxlength="50"
              placeholder="请输入待办标题"
              autocomplete="off"
              clearable
              show-word-limit
              class="flex-1 h-full"
            />
          </el-form-item>
          <el-form-item label="待办执行人" :label-width="95" prop="execUnits">
            <organizationalStructureTree
              v-model="execUnits"
              screen="part"
              class="flex-1 h-full organizationalStructureTree"
              collapse-tags
              collapse-tags-tooltip
              @get-checked-nodes="getCheckedNodes"
              @removeCheckedNodes="removeCheckedNodes"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="!mb-0px">
        <el-col :span="7">
          <el-form-item label="待办截止时间" :label-width="120" prop="endTime">
            <el-date-picker
              style="width: 100%"
              v-model="todoListForm.endTime"
              type="date"
              clearable
              value-format="YYYY-MM-DD"
              placeholder="选择待办截止时间"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-dialog
        v-model="dialogFormVisible"
        title="选择发布时间"
        width="30%"
        align-center
        destroy-on-close
        custom-class="publishTimeModel"
      >
        <el-form-item label="" prop="publishTime">
          <el-date-picker
            style="width: 100%"
            v-model="todoListForm.publishTime"
            type="datetime"
            clearable
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择发送时间"
            :disabled="todoListForm.isNowPublish == '1'"
          />
        </el-form-item>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="regularlyPosted(formRef)">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
      <el-row>
        <el-col :span="24" class="h-full">
          <el-form-item
            label="待办内容"
            prop="pendingContent"
            class="pendingContent"
          >
            <rich-editor
              :uploadUrl="'/pending/uploadFile'"
              :containerStyle="{ width: '100%' }"
              :editorStyle="{ height: '220px' }"
              @updateNoticeFormContent="updateNoticeFormContent"
              :default-value="todoListForm.pendingContent"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="justify-between">
        <div></div>
        <div>
          <el-button @click="goBack" ref="cancelBtnRef">取消</el-button>
          <el-button
            v-if="$route.query.source !== 'createdNotification'"
            type="primary"
            :loading="draftLoading"
            plain
            @click="saveDraft(formRef)"
          >
            存为草稿
          </el-button>
          <el-button
            type="primary"
            :loading="publishLoading"
            plain
            @click="dialogFormVisible = true"
          >
            定时发布
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="publishNow(formRef)"
          >
            立即发布
          </el-button>
        </div>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import {
  reactive,
  ref,
  onMounted,
  onUnmounted,
  watch,
  computed,
  nextTick
} from 'vue'
import type { ElForm } from 'element-plus'
import $API from '~/common/api'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import { useUserInfo, useTodoListInfo } from '@/store'
import equipmentStatus from '~/components/public/common-form/equipmentStatusSelect.vue'
import organizationalStructureTree from '@/components/public/common-form/organizationalStructureTree.vue'
import richEditor from '@/components/public/wangeditor.vue'
import { ElMessage, ElMessageBox, ElDatePicker } from 'element-plus'

const router = useRouter()
const route = useRoute()

const labelPosition = ref('top')
const ui = useUserInfo()

const todoListInfo = useTodoListInfo()

const draftLoading = ref(false)
const publishLoading = ref(false)
const loading = ref(false)

const isPublish = ref(false)

type FormInstance = InstanceType<typeof ElForm>
interface typeOp {
  [key: string]: any
}
const props: any = defineProps({
  row: {
    type: Object,
    default: () => {}
  }
})

const currentType = ref('add')

let isFirst = true
const updateNoticeFormContent = (valueHtml) => {
  console.log(valueHtml)
  todoListForm.pendingContent = valueHtml
  if (isFirst) {
    isFirst = false
  } else {
    formRef.value!.validateField('pendingContent')
  }
}
const execUnits = ref([])
console.log($API.nextHalfHour())

const todoListForm: typeOp = reactive({
  pendingTitle: '',
  pendingContent: '',
  endTime: '',
  publishTime: $API.nextHalfHour(),
  createTime: '',
  createUserId: ui.value.userId,
  createUserName: ui.value.userName,
  pendingId: '',
  execUnits: [],
  isNowPublish: '0',
  publishStatus: 0,
  updateTime: '',
  updateUserId: ui.value.userId,
  updateUserName: ui.value.userName
})

const getCheckedNodes = (checkedAllNodes) => {
  console.log(checkedAllNodes, 'checkedAllNodes')
  execUnits.value = checkedAllNodes
}

const removeCheckedNodes = (removeId) => {
  // console.log(removeId, 'removeId')
  const index = execUnits.value.findIndex((i: any) => i.unitId == removeId)
  execUnits.value.splice(index, 1)
}

const formRef = ref<FormInstance>()

// const isRequired = computed(() => {
//   return todoListForm.isNowPublish == '0'
// })

const isRequired = computed(() => {
  return todoListForm.publishStatus != -1
})

const rules = reactive({
  pendingTitle: [
    {
      required: true,
      message: '请输入待办标题',
      trigger: 'change'
    }
  ],
  execUnits: [
    {
      required: true,
      message: '请选择待办执行人',
      trigger: 'change'
    }
  ],
  endTime: [
    {
      required: true,
      message: '请选择待办截止时间',
      trigger: 'change'
    }
  ],
  publishTime: [
    {
      required: isRequired,
      message: '请选择发送时间',
      trigger: 'change'
    }
  ],
  pendingContent: [
    {
      required: true,
      message: '请输入内容',
      trigger: 'change'
    }
  ]
})

function timeChange(val: any) {
  console.log(val)
  todoListForm.createEndTime = ''
  todoListForm.createStartTime = ''
  if (val && val.length > 0) {
    todoListForm.createStartTime = val[0] + ' 00:00:00'
    todoListForm.createEndTime = val[1] + ' 23:59:59'
  }
}

const emit = defineEmits(['cancel'])
const cancel = () => {
  emit('cancel', false)
}

watch(
  () => execUnits.value,
  (val) => {
    todoListForm.execUnits = execUnits.value
  },
  {
    deep: true
  }
)
const dialogFormVisible = ref(false)
// 定时发布
function regularlyPosted(formEl: FormInstance | undefined) {
  if (todoListForm.publishTime) dialogFormVisible.value = false
  isPublish.value = true
  todoListForm.publishStatus = 0
  submitForm(formEl, publishLoading.value, '0')
}

function saveDraft(formEl: FormInstance | undefined) {
  const tit = '是否将该内容存为草稿？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        isPublish.value = true
        todoListForm.publishStatus = -1
        submitForm(formEl, draftLoading.value, '0', 1)
      } else {
        todoListForm.publishStatus = 0
        submitForm(formEl, draftLoading.value, '0', -1)
      }

      done()
    }
  })
}

function publishNow(formEl: FormInstance | undefined) {
  const tit = '是否立即发布待办内容？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        isPublish.value = true
        todoListForm.publishStatus = 1
        submitForm(formEl, loading.value, '1')
      } else {
        todoListForm.isNowPublish = '0'
      }
      done()
    }
  })
}

/*
 * isDraftType 1: 确认 -1: 取消
 * isNowPublish 1: 立即发布 0: 不立即发布
 */
async function submitForm(
  formEl: FormInstance | undefined,
  load: Boolean,
  isNowPublish?: String | Number,
  isDraftType?: Number,
  to?: Object
) {
  todoListForm.createTime = todoListForm.createTime
    ? todoListForm.createTime
    : $API.today()
  todoListForm.updateTime = $API.today()

  todoListForm.updateTime = todoListForm.updateTime
    ? todoListForm.updateTime
    : $API.today()

  console.log(todoListForm)
  if (!formEl) return

  let isEmpty = false

  await formEl.validate(async (valid) => {
    if (valid || isDraftType) {
      if (isDraftType) formEl.clearValidate()
      if (isDraftType === -1) return

      if (
        !todoListForm.pendingTitle &&
        !todoListForm.pendingContent &&
        todoListForm.execUnits.length == 0 &&
        isDraftType === 1
      ) {
        isEmpty = true
        isPublish.value = false
        return ElMessage.warning(
          '待办标题、待办执行人、待办内容不能全部都为空！'
        )
      }

      load = true
      todoListForm.isNowPublish = isNowPublish
      let publishTime = todoListForm.publishTime
      if (todoListForm.isNowPublish == '0') {
        const nowTime = Date.now()
        const timeStamp = new Date(todoListForm.publishTime).getTime()
        if (nowTime >= timeStamp) {
          todoListForm.publishTime = ''
          return ElMessage.warning('当前发送时间早于当前时间,请重新选择时间！')
        }
      } else if (todoListForm.isNowPublish == '1') {
        publishTime = $API.today()
      }

      if (currentType.value == 'add') {
        const res: any = await $API.post({
          url: '/pending/addPending',
          data: {
            ...todoListForm,
            publishTime
          }
        })
        if (res.code == 'success') {
          ElMessage.success('新增成功！')
          if (!to) {
            let active = isDraftType === 1 ? 3 : 1
            router.push(
              '/staging/todoList/index?isShowBack=1&jumpAddress=/staging/index&active=' +
                active
            )
          }
        }
      } else if (currentType.value === 'edit') {
        const res: any = await $API.post({
          url: '/pending/editPending',
          data: {
            ...todoListForm,
            publishTime
          }
        })
        if (res.code == 'success') {
          ElMessage.success('编辑成功！')
          if (!to) {
            let active = isDraftType === 1 ? 3 : 1
            router.push(
              '/staging/todoList/index?isShowBack=1&jumpAddress=/staging/index&active=' +
                active
            )
          }
        }
      }

      load = false
      cancel()
    }
  })

  return isEmpty
}

onMounted(() => {
  if (route.query.currentType) {
    currentType.value = route.query.currentType as string
  }

  if (currentType.value == 'add') {
    todoListInfo.value = ''
  } else if (currentType.value == 'edit') {
    for (let key in todoListInfo.value) {
      // 编辑的时候还是取当前账号的id和名称,更新时间取最新的时间
      if (
        todoListForm.hasOwnProperty(key) &&
        !['updateTime', 'updateUserId', 'updateUserName'].includes(key) &&
        todoListInfo.value.hasOwnProperty(key)
      ) {
        // 将源对象的值赋值给目标对象的对应键
        todoListForm[key] = todoListInfo.value[key]
        if (route.query.isDrafts === 'true') {
          todoListForm.publishTime = $API.nextHalfHour()
        }
      }
    }

    execUnits.value = todoListInfo.value.execUnitList
    // console.log(todoListInfo)
    // console.log(todoListForm)
  }
  console.log(todoListForm, 'todoListForm')
})

onUnmounted(() => {})

const cancelBtnRef = ref()
const goBack = () => {
  isPublish.value = true
  if (route.query.isDrafts === 'true' && route.query.currentType === 'edit') {
    router.push('/staging/todoList/index?isShowBack=1&jumpAddress=/staging/index&active=3')
  } else exitCommonHandle(null)
  cancelBtnRef.value.ref.blur()
}

const exitCommonHandle = (next, to: any = '') => {
  const tit = '即将离开当前页面,请确认是否将内容存为草稿？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showClose: false,
    closeOnClickModal: false,
    closeOnPressEscape: false,
    type: 'warning',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        todoListForm.publishStatus = -1
        const flag = await submitForm(
          formRef.value,
          draftLoading.value,
          '0',
          1,
          to
        )
        done()
        if (!flag && next) next()
      } else {
        done()
        if (next) next()
        else router.push('/staging/todoList/index?isShowBack=1&jumpAddress=/staging/index&active=1')
      }
    }
  }).catch(() => {
  })
}

onBeforeRouteLeave((to, from, next) => {
  if (!isPublish.value) {
    exitCommonHandle(next, to)
  } else {
    next()
  }
})

// 时间限制
const disabledDate = (date) => {
  const today = new Date()
  const currentYear = today.getFullYear()
  const currentMonth = today.getMonth()
  const currentDate = today.getDate()
  return (
    date.getFullYear() < currentYear ||
    (date.getFullYear() === currentYear && date.getMonth() < currentMonth) ||
    (date.getFullYear() === currentYear &&
      date.getMonth() === currentMonth &&
      date.getDate() < currentDate)
  )
}
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss" scoped>
:deep(.el-textarea .el-input__count) {
  bottom: -25px !important;
}

.add-dialog {
  &:deep(.el-dialog__header) {
    text-align: left;
  }
}

.formView {
  padding: 20px;
  background-color: #fff;

  :deep(.el-form-item .el-form-item__label) {
    width: 110px !important;
  }

  :deep(.publishTimeModel) {
    margin-top: 35vh;
  }

  :deep(.publishTimeModel .el-input__inner) {
    padding-left: 40px !important;
  }
}
</style>
