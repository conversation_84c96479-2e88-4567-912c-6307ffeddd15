<!-- drafts -->
<template>
  <div class="w-full h-full flex flex-col drafts">
    <div class="main_search">
      <header-item title="创建时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY-MM-DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span class="operate-item" track @click="editHandle(scope.row)">
                编辑
              </span>
              <span class="operate-item" track @click="deleteHandle(scope.row)">
                删除
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, ref, reactive, toRaw, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo, useTodoListInfo } from '@/store'
import { queryPendingDraft, delPending } from '@/common/comResponse/index'

const userInfo = useUserInfo()
const route: any = useRoute()
const router: any = useRouter()

const props = defineProps({
  disposeId: {
    type: String,
    default: ''
  },
  propSearch: {
    type: Object,
    default: {}
  }
})

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const searchFrom = reactive({
  createUserId: userInfo.value.userId,
  createStartTime: '', //开始时间
  createEndTime: '' //结束时间
})

// 离线时间
const timeAll: any = ref([])
// timeAll.value = $API.getDays(30)
// searchFrom.createStartTime = timeAll.value[0] + ' 00:00:00'
// searchFrom.createEndTime = timeAll.value[1] + ' 23:59:59'

const popupShow = ref(false)

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

function close() {
  popupShow.value = false
}

const todoListInfo = useTodoListInfo()
// 编辑
const editHandle = (item) => {
  todoListInfo.value = item
  router.push('/staging/todoList/edit?currentType=edit&isDrafts=true&active=3')
}

function deleteHandle(item) {
  const tit = '删除后无法恢复,确认是否删除？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await delPending({
          pendingId: item.pendingId,
          createUserId: item.createUserId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '删除成功！',
            type: 'success'
          })
          done()
          init()
        }
      } else {
        done()
      }
    }
  })
}

onMounted(() => {
  if (props.propSearch.operationResult) {
    timeAll.value = $API.getDays()
    searchFrom.createStartTime = timeAll.value[0] + ' 00:00:00'
    searchFrom.createEndTime = timeAll.value[1] + ' 23:59:59'
  }

  init()
  getTableColumns()
})

function timeChange(val: any) {
  searchFrom.createStartTime = ''
  searchFrom.createEndTime = ''
  if (val && val.length > 0) {
    searchFrom.createStartTime = val[0] + ' 00:00:00'
    searchFrom.createEndTime = val[1] + ' 23:59:59'
  }
  init()
}

function init() {
  getTableData()
}

async function getTableData() {
  loading.value = true
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await queryPendingDraft(params)
  if (res && res.code == 'success') {
    console.log(res, '获取草稿箱的列表')
    tableData.value = res.data.rows || []
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

const add = () => {}

function getTableColumns() {
  columns.value = [
    {
      title: '待办标题',
      key: 'pendingTitle',
      showOverflowTooltip: true
    },
    {
      title: '执行单位',
      key: 'execUnits',
      showOverflowTooltip: true
    },
    {
      title: '创建时间',
      key: 'createTime',
      showOverflowTooltip: true
    },
    {
      title: '截止时间',
      key: 'endTime',
      showOverflowTooltip: true
    },
    {
      title: '发布状态',
      key: 'publishStatusStr',
      showOverflowTooltip: true
    }
  ]
}
</script>
<style lang="scss" scoped></style>
