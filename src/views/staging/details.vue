<!-- details -->
<template>
  <div class="details h-full" v-if="!isWithdraw">
    <div class="header">
      <div class="title">
        {{ detailsInfo.title || '--' }}
      </div>
      <div class="subTitle">
        <span class="mr-20px">
          发布时间： {{ detailsInfo.publishTime || '--' }}
        </span>
        <span v-if="source == 'information'">
          来源： {{ detailsInfo.source || '--' }}
        </span>
        <span v-else-if="source == 'notice'">
          {{ detailsInfo.noticeTypeStr }}
        </span>
      </div>
    </div>
    <div class="content" v-html="detailsInfo.content"></div>
  </div>
  <noData v-else labelTitle="发布者已删除内容，无法查看！"></noData>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  queryInformationById,
  queryNoticeDetailsById
} from '@/common/comResponse/index'

const route: any = useRoute()
const source = ref('') // 来自于哪个页面
const id = ref('')
const infoCategory = ref('0')
const isDrafts = ref('')
const isWithdraw = ref(false)

const detailsInfo: any = ref({
  title: '',
  publishTime: '',
  source: '',
  content: ''
})

const getInformationDetails = async () => {
  const res: any = await queryInformationById({
    infoId: id.value,
    informationCategory: infoCategory.value
  })
  if (res && res.code == 'success') {
    // detailsInfo.value = res.data || {}
    if (isDrafts.value !== 'drafts' && res.data.publishStatus == -1) {
      isWithdraw.value = true
    }
    detailsInfo.value.title = res.data.informationTitle
    // 草稿箱发布时间显示--
    detailsInfo.value.publishTime =
      res.data.publishStatus != '-1' ? res.data.publishTime : '--'
    detailsInfo.value.source = res.data.informationSource
    detailsInfo.value.content = res.data.informationContent
  }
}

const getNoticeDetailsById = async () => {
  const res: any = await queryNoticeDetailsById({
    noticeId: id.value
  })
  if (res && res.code == 'success') {
    // detailsInfo.value = res.data || {}
    if (res.data.publishStatus == -1) isWithdraw.value = true
    detailsInfo.value.title = res.data.noticeName
    detailsInfo.value.publishTime = res.data.publishTime
    detailsInfo.value.source = res.data.updateUserName
    detailsInfo.value.content = res.data.noticeContent
    switch (res.data.noticeType) {
      case 0:
        detailsInfo.value.noticeTypeStr = '教育培训'
        break
      case 1:
        detailsInfo.value.noticeTypeStr = '隐患整改'
        break
      case 2:
        detailsInfo.value.noticeTypeStr = '安全检查'
        break
      case 3:
        detailsInfo.value.noticeTypeStr = '风险提示'
        break
      case 4:
        detailsInfo.value.noticeTypeStr = '其他'
        break
    }
  }
}

console.log(route.query)
if (route.query.source) source.value = route.query.source
if (route.query.infoId) id.value = route.query.infoId
if (route.query.noticeId) id.value = route.query.noticeId
if (route.query.infoCategory) infoCategory.value = route.query.infoCategory
if (route.query.isDrafts) isDrafts.value = route.query.isDrafts
console.log(isDrafts.value === 'drafts')

onMounted(() => {
  if (source.value == 'information') getInformationDetails()
  if (source.value == 'notice') getNoticeDetailsById()
})
</script>

<style lang="scss" scoped>
.details {
  background-color: #fff;
  padding: 20px;
  overflow: auto;

  .header {
    text-align: center;
    margin-bottom: 30px;

    .title {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      margin-bottom: 17px;
    }

    .subTitle {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #666666;
    }
  }

  .content {
    line-height: 2;

    :deep(> p) {
      text-indent: 2em;

      img {
        margin: auto;
      }
    }
  }
}
</style>
