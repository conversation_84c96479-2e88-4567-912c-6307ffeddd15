<!-- createdNotification -->
<template>
  <div class="w-full h-full flex flex-col createdNotification">
    <div class="main_search">
      <header-item title="通知类型">
        <equipmentStatus
          class="w-full"
          :options="noticeTypeOptions"
          v-model="searchFrom.noticeType"
          @change="init"
        />
      </header-item>

      <header-item title="发布状态">
        <equipmentStatus
          :options="publishStatusOptions"
          v-model="searchFrom.publishStatus"
          @change="init"
        />
      </header-item>

      <header-item title="发布时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY-MM-DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>
      <div class="export action" style="margin-bottom: 0">
        <el-button
          type="primary"
          class="export-btn"
          @click="$router.push('/staging/notice/add')"
        >
          新增通知
        </el-button>
      </div>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span
                class="operate-item"
                track
                @click="handleDetails(scope.row)"
              >
                详情
              </span>
              <span
                v-if="scope.row.publishStatus == '1'"
                class="operate-item"
                track
                @click="withdrawHandle(scope.row)"
              >
                撤回
              </span>
              <span
                v-if="scope.row.publishStatus == '0'"
                class="operate-item"
                track
                @click="editHandle(scope.row)"
              >
                编辑
              </span>
              <span
                v-if="scope.row.publishStatus == '0'"
                class="operate-item"
                track
                @click="deleteHandle(scope.row)"
              >
                删除
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
</template>

<script lang="ts" setup>
import equipmentStatus from '~/components/public/common-form/equipmentStatusSelect.vue'

import { onMounted, ref, reactive, toRaw, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo, useNoticeInfo } from '@/store'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  queryNoticeByCreate,
  delNotice,
  withdrawNotice
} from '@/common/comResponse/index'

const userInfo = useUserInfo()
const route: any = useRoute()
const router: any = useRouter()

const noticeInfo = useNoticeInfo()

const props = defineProps({
  disposeId: {
    type: String,
    default: ''
  },
  propSearch: {
    type: Object,
    default: {}
  }
})

const noticeTypeOptions = ref([
  {
    label: '教育培训',
    value: '0'
  },
  {
    label: '隐患整改',
    value: '1'
  },
  {
    label: '安全检查',
    value: '2'
  },
  {
    label: '风险提示',
    value: '3'
  },
  {
    label: '其他',
    value: '4'
  }
])

const publishStatusOptions = ref([
  {
    label: '待发布',
    value: '0'
  },
  {
    label: '已发布',
    value: '1'
  }
])

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const chargeId: any = ref('') // 分管单位id
const btnLoading: any = ref(false)

const searchFrom = reactive({
  createUserId: userInfo.value.userId,
  noticeType: '',
  publishStatus: '',
  orderFields: '',
  publishStartTime: '', //开始时间
  publishEndTime: '' //结束时间
})

// 离线时间
const timeAll: any = ref([])
// timeAll.value = $API.getDays(30)
// searchFrom.publishStartTime = timeAll.value[0] + ' 00:00:00'
// searchFrom.publishEndTime = timeAll.value[1] + ' 23:59:59'

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

/**
 * 点击详情按钮
 * @param item 当前点击的表格的行数据
 */

function handleDetails(item: any) {
  console.log(item, 'item')
  router.push(
    '/staging/notice/noticeDetails?source=notice&noticeId=' +
      item.noticeId +
      '&isShowBack=1'
  )
}

/**
 * 撤回
 */
const withdrawHandle = (item) => {
  ElMessageBox({
    title: '',
    type: 'warning',
    message: '撤回后其他用户无法查看该内容，您可以在草稿箱进行编辑修改',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await withdrawNotice({
          noticeId: item.noticeId,
          createUserId: item.createUserId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '撤回成功！',
            type: 'success'
          })
          done()
          init()
        } else {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
          done()
        }
      } else {
        done()
      }
    }
  })
}

// 编辑
const editHandle = (item) => {
  noticeInfo.value = item
  router.push(
    '/staging/notice/edit?currentType=edit&source=createdNotification'
  )
}

/**
 * 删除通知通告
 */
const deleteHandle = (item) => {
  ElMessageBox({
    title: '删除',
    type: 'warning',
    message: '确认删除通知吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await delNotice({
          noticeId: item.noticeId,
          createUserId: item.createUserId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '删除成功！',
            type: 'success'
          })
          done()
          init()
        } else {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
          done()
        }
      } else {
        done()
      }
    }
  })
}

onMounted(() => {
  if (props.propSearch.operationResult) {
    timeAll.value = $API.getDays()
    searchFrom.publishStartTime = timeAll.value[0] + ' 00:00:00'
    searchFrom.publishEndTime = timeAll.value[1] + ' 23:59:59'
  }

  init()
  getTableColumns()
})

function timeChange(val: any) {
  searchFrom.publishStartTime = ''
  searchFrom.publishEndTime = ''
  if (val && val.length > 0) {
    searchFrom.publishStartTime = val[0] + ' 00:00:00'
    searchFrom.publishEndTime = val[1] + ' 23:59:59'
  }
  init()
}

function init() {
  getTableData()
}

async function getTableData() {
  loading.value = true
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await queryNoticeByCreate(params)
  if (res && res.code == 'success') {
    console.log(res, '获取创建的通知列表')
    tableData.value = res.data.rows || []
    tableData.value.forEach((item: any) => {
      item.publishRangeStr = item.publishRange.map((i) => i.unitName).join('，')
    })
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

const add = () => {}

function getTableColumns() {
  columns.value = [
    {
      title: '通知类型',
      key: 'noticeTypeStr',
      showOverflowTooltip: true
    },
    {
      title: '通知标题',
      key: 'noticeName',
      showOverflowTooltip: true
    },
    {
      title: '发送范围',
      key: 'publishRangeStr',
      showOverflowTooltip: true
    },
    {
      title: '发布时间',
      key: 'publishTime',
      showOverflowTooltip: true
    },
    {
      title: '发布状态',
      key: 'publishStatusStr',
      showOverflowTooltip: true
    },
    {
      title: '创建人',
      key: 'createUserName',
      showOverflowTooltip: true
    }
  ]
}
</script>
<style lang="scss" scoped></style>
