<template>
  <div class="w-full h-full flex flex-col notice">
    <el-tabs v-model="editableTabsValue" class="tabs h-full" @tab-click="handleClick">
      <el-tab-pane class="h-full" v-for="item in editableTabs" :key="item.value" :label="item.label" :name="item.value">
        <template v-if="item.value === editableTabsValue">
          <div class="w-full h-full">
            <component :is="tabs[item.key]" class="tab"></component>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>
    <div class="goBack" v-if="$route.query.isYZT">
      <el-button plain class="back" @click="$router.go(-1)"> 返回 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import createdNotification from '@/views/staging/notice/createdNotification.vue'
import receiveNotification from '@/views/staging/notice/receiveNotification.vue'
import drafts from '@/views/staging/notice/drafts.vue'
import { useRouter, useRoute, onBeforeRouteUpdate } from 'vue-router'
const route = useRoute()
const router = useRouter()
const editableTabsValue: any = ref(2)
if (route.query.active) editableTabsValue.value = Number(route.query.active)

const editableTabs: any = ref([
  { label: '我创建的通知', value: 1, key: 'createdNotification' },
  { label: '我收到的通知', value: 2, key: 'receiveNotification' },
  { label: '草稿箱', value: 3, key: 'drafts' }
])
const currentIcon: any = ref('1')
const tabs = {
  receiveNotification,
  createdNotification,
  drafts
}
const handleClick = (tab: TabsPaneContext, event: Event) => {
  // router.replace(`/staging/notice/index?active=${tab.paneName}`)
}
</script>
<style lang="scss" scoped>
.notice {
  .goBack {
    position: absolute;
    right: 40px;
    top: 8px;
  }

  :deep(.tabs .el-tabs__nav-scroll) {
    background-color: #fff;
    height: 48px;
    padding: 6px 48px 0;
  }

  :deep(.tabs .el-tabs__content) {
    height: calc(100% - 58px);
    overflow: auto;

    .el-tab-pane {
      height: 100%;
    }
  }

  :deep(.el-tabs__nav-wrap::after) {
    background-color: unset;
  }

  :deep(.tabs .popup-side .el-tabs__nav-scroll) {
    //詳情TAB 样式穿透
    padding: 0;
  }

  .monitor-tabs {
    position: absolute;
    right: 40px;
    top: 10px;
    z-index: 9;

    .iconfont {
      font-size: 18px;
      padding: 6px;
    }

    .icon-wrap {
      height: 28px;
      width: 28px;
      border: 1px solid #ccc;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      background-color: #ffffff;
      color: #333333;
    }

    .icon-wrap.active {
      background-color: #1890ff;
      border-color: transparent;

      .iconfont {
        color: #ffffff;
      }
    }
  }
}
</style>
