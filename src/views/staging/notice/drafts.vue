<!-- drafts -->
<template>
  <div class="w-full h-full flex flex-col drafts">
    <div class="main_search">
      <header-item title="通知类型">
        <equipmentStatus
          class="w-full"
          :options="noticeTypeOptions"
          v-model="searchFrom.noticeType"
          @change="init"
        />
      </header-item>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span class="operate-item" track @click="editHandle(scope.row)">
                编辑
              </span>
              <span class="operate-item" track @click="deleteHandle(scope.row)">
                删除
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
</template>

<script lang="ts" setup>
import equipmentStatus from '~/components/public/common-form/equipmentStatusSelect.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, ref, reactive, toRaw, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo, useNoticeInfo } from '@/store'
import { queryNoticeByDrafts, delNotice } from '@/common/comResponse/index'

const userInfo = useUserInfo()
const route: any = useRoute()
const router: any = useRouter()

const props = defineProps({
  disposeId: {
    type: String,
    default: ''
  },
  propSearch: {
    type: Object,
    default: {}
  }
})

const noticeTypeOptions = ref([
  {
    label: '教育培训',
    value: '0'
  },
  {
    label: '隐患整改',
    value: '1'
  },
  {
    label: '安全检查',
    value: '2'
  },
  {
    label: '风险提示',
    value: '3'
  },
  {
    label: '其他',
    value: '4'
  }
])

const publishStatusOptions = ref([
  {
    label: '待发布',
    value: '0'
  },
  {
    label: '已发布',
    value: '1'
  }
])

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const chargeId: any = ref('') // 分管单位id
const btnLoading: any = ref(false)

const searchFrom = reactive({
  createUserId: userInfo.value.userId,
  noticeType: '',
  createStartTime: '', //开始时间
  createEndTime: '' //结束时间
})

// 离线时间
const timeAll: any = ref([])
// timeAll.value = $API.getDays(30)
// searchFrom.createStartTime = timeAll.value[0] + ' 00:00:00'
// searchFrom.createEndTime = timeAll.value[1] + ' 23:59:59'

const popupShow = ref(false)
const addPopupShow = ref(false)

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

function close() {
  popupShow.value = false
}

const noticeInfo = useNoticeInfo()
// 编辑
const editHandle = (item) => {
  noticeInfo.value = item
  router.push('/staging/notice/edit?currentType=edit&isDrafts=true&active=3')
}

function deleteHandle(item) {
  const tit = '删除后无法恢复,确认是否删除？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await delNotice({
          noticeId: item.noticeId,
          createUserId: item.createUserId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '删除成功！',
            type: 'success'
          })
          done()
          init()
        }
      } else {
        done()
      }
    }
  })
}

onMounted(() => {
  if (props.propSearch.operationResult) {
    timeAll.value = $API.getDays()
    searchFrom.createStartTime = timeAll.value[0] + ' 00:00:00'
    searchFrom.createEndTime = timeAll.value[1] + ' 23:59:59'
  }

  init()
  getTableColumns()
})

function timeChange(val: any) {
  searchFrom.createStartTime = ''
  searchFrom.createEndTime = ''
  if (val && val.length > 0) {
    searchFrom.createStartTime = val[0] + ' 00:00:00'
    searchFrom.createEndTime = val[1] + ' 23:59:59'
  }
  init()
}

function init() {
  getTableData()
}

async function getTableData() {
  loading.value = true
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await queryNoticeByDrafts(params)
  if (res && res.code == 'success') {
    console.log(res, '获取草稿箱的列表')
    tableData.value = res.data.rows || []
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

const add = () => {}

function getTableColumns() {
  columns.value = [
    {
      title: '通知类型',
      key: 'noticeTypeStr',
      showOverflowTooltip: true
    },
    {
      title: '通知标题',
      key: 'noticeName',
      showOverflowTooltip: true
    },
    {
      title: '发送范围',
      key: 'publishRangeStr',
      showOverflowTooltip: true
    },
    {
      title: '发布状态',
      key: 'publishStatusStr',
      showOverflowTooltip: true
    },
    {
      title: '创建人',
      key: 'createUserName',
      showOverflowTooltip: true
    }
  ]
}
</script>
<style lang="scss" scoped></style>
