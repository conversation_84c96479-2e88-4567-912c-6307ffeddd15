<!-- receiveNotification -->
<template>
  <div class="w-full h-full flex flex-col receiveNotification">
    <div class="main_search">
      <header-item title="通知类型">
        <equipmentStatus
          class="w-full"
          :options="noticeTypeOptions"
          v-model="searchFrom.noticeType"
          @change="init"
        />
      </header-item>

      <header-item title="发布时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY-MM-DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span
                class="operate-item"
                track
                @click="handleDetails(scope.row)"
              >
                详情
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
</template>

<script lang="ts" setup>
import equipmentStatus from '~/components/public/common-form/equipmentStatusSelect.vue'

import { onMounted, ref, reactive, toRaw, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo } from '@/store'
import { queryNoticeByReceive } from '@/common/comResponse/index'

const userInfo = useUserInfo()
const route: any = useRoute()
const router: any = useRouter()

const props = defineProps({
  disposeId: {
    type: String,
    default: ''
  },
  propSearch: {
    type: Object,
    default: {}
  }
})

const noticeTypeOptions = ref([
  {
    label: '教育培训',
    value: '0'
  },
  {
    label: '隐患整改',
    value: '1'
  },
  {
    label: '安全检查',
    value: '2'
  },
  {
    label: '风险提示',
    value: '3'
  },
  {
    label: '其他',
    value: '4'
  }
])

const publishStatusOptions = ref([
  {
    label: '待发布',
    value: '0'
  },
  {
    label: '已发布',
    value: '1'
  }
])

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const chargeId: any = ref('') // 分管单位id
const btnLoading: any = ref(false)

const searchFrom = reactive({
  superviseId: userInfo.value.orgCode,
  noticeType: '',
  startTime: '', //开始时间
  endTime: '' //结束时间
})

// 离线时间
const timeAll: any = ref([])
// timeAll.value = $API.getDays(30)
// searchFrom.startTime = timeAll.value[0] + ' 00:00:00'
// searchFrom.endTime = timeAll.value[1] + ' 23:59:59'

const popupShow = ref(false)
const addPopupShow = ref(false)

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

function close() {
  popupShow.value = false
}

/**
 * 点击详情按钮
 * @param item 当前点击的表格的行数据
 */
const deviceInfo: any = ref({})
function handleDetails(item: any) {
  console.log(item, 'item')
  deviceInfo.value = item
  popupShow.value = true
  router.push(
    '/staging/notice/noticeDetails?source=notice&noticeId=' +
      item.noticeId +
      '&isShowBack=1'
  )
}

onMounted(() => {
  if (props.propSearch.operationResult) {
    timeAll.value = $API.getDays()
    searchFrom.startTime = timeAll.value[0] + ' 00:00:00'
    searchFrom.endTime = timeAll.value[1] + ' 23:59:59'
  }

  init()
  getTableColumns()
})

function timeChange(val: any) {
  searchFrom.startTime = ''
  searchFrom.endTime = ''
  if (val && val.length > 0) {
    searchFrom.startTime = val[0] + ' 00:00:00'
    searchFrom.endTime = val[1] + ' 23:59:59'
  }
  init()
}

function init() {
  getTableData()
}

async function getTableData() {
  loading.value = true
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await queryNoticeByReceive(params)
  if (res && res.code == 'success') {
    console.log(res, '获取创建的通知列表')
    tableData.value = res.data.rows || []
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

const add = () => {}

function getTableColumns() {
  columns.value = [
    {
      title: '通知类型',
      key: 'noticeTypeStr',
      showOverflowTooltip: true
    },
    {
      title: '通知标题',
      key: 'noticeName',
      showOverflowTooltip: true
    },
    {
      title: '发布时间',
      key: 'publishTime',
      showOverflowTooltip: true
    },
    {
      title: '通知状态',
      key: 'noticeStateStr',
      showOverflowTooltip: true
    },
    {
      title: '创建人',
      key: 'createUserName',
      showOverflowTooltip: true
    }
  ]
}
</script>
<style lang="scss" scoped></style>
