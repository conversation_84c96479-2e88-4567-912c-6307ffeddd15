<!-- 工作台 -->
<template>
  <div class="staging h-full" v-loading="loading">2</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserInfo, updatePopupShow } from '@/store'
import $API from '@/common/api'
import {
  queryDecisionWithDataList,
  queryInformationList,
  queryPendingByReceive,
  queryNoticeByReceive,
  querySystemDynamics,
  getSystemDynamicsDetail,
} from '@/common/comResponse/index'
import { setPageJsonQueryParamsAdapter } from '@/commonTypes/jumpDataStatisticsQuery'
const userInfo = useUserInfo()
const isShow = updatePopupShow()

// userInfo.value.roleNames = '集团总公司,消防安全管理人,安技部'
const router = useRouter()
const queryParams = ref<any>({})
const chargeId = ref<string>('') // 分管单位id
const timeRange = ref<any>([])

const loading = ref(false)

const loginTime = ref('')
function getLoginLogData() {
  $API
    .post({
      url: '/userManage/queryUserLog',
      params: Object.assign({
        clntType: '4',
        loginName: userInfo.value.loginName,
        orgCode: userInfo.value.orgCode,
        pageSize: 1,
        pageNo: 2,
      }),
    })
    .then((res: any) => {
      if (res.data && res.code == 'success' && res.data.rows.length != 0) {
        loginTime.value = res.data.rows[0].loginTime || ''
      }
    })
}

const selectOption = ref<any>([
  {
    value: '1',
    label: '防火巡查',
  },
  {
    value: '2',
    label: '消控室值班',
  },
  {
    value: '3',
    label: '报警复核',
  },
  {
    value: '4',
    label: '大规模屏蔽',
  },
  {
    value: '5',
    label: '大规模故障',
  },
  {
    value: '6',
    label: '高频故障',
  },
  {
    value: '7',
    label: '高频报警',
  },
])

const fomartType = (val: any) => {
  let arr: Array<any> = selectOption.value.filter((i) => {
    return i.value == val
  })
  if (arr.length > 0) {
    return arr[0].label
  }
  return '--'
}

const decisionList: any = ref([])
async function getDecisionList() {
  const res: any = await queryDecisionWithDataList({
    superviseId: userInfo.value.orgCode,
    pageNo: 1,
    pageSize: 5,
  })

  if (res && res.code == 'success') {
    console.log(res, '数据决策列表')

    res.data.rows.forEach((i) => {
      i.decisionTypeStr = fomartType(i.decisionType)
    })
    decisionList.value = res.data.rows || []
  }
}

const toDetails = (item) => {
  sessionStorage.setItem('JC_item', JSON.stringify(item))
  router.push(
    '/dataIntelligence/dataDecision/details?alarmId=' + item.alarmId + '&decisionType=' + item.decisionType + ''
  )
}

const informationList: any = ref([])
const infoCategory = ref('')
const infomationLoading = ref(false)
const getInformationList = async () => {
  infomationLoading.value = true
  try {
    const res: any = await queryInformationList({
      superviseId: userInfo.value.orgCode,
      createUserId: userInfo.value.userId, // 创建人ID
      // infoCategory: infoCategory.value, // （0:消防热点资讯 1:中外运消防资讯）
      pageNo: 1,
      pageSize: 5,
    })
    console.log(res, '获取消防资讯列表')
    infomationLoading.value = false
    if (res && res.code == 'success') {
      res.data.rows.forEach((item) => (item.id = item.id || item.informationId))
      informationList.value = res.data.rows || []
    }
  } catch (error) {
    infomationLoading.value = false
    informationList.value = []
  }
}

const messageList: any = ref([])
const getMessageList = async () => {
  const res: any = await $API.post({
    url: '/message/getMessageInfoList',
    params: {
      superviseId: userInfo.value.orgCode,
      pageNo: 1,
      pageSize: 3,
      msgType: '0',
      userId: userInfo.value.userId,
    },
  })
  console.log(res, '获取消息中心列表')
  if (res && res.code == 'success') {
    messageList.value = res.data.rows || []
  }
}

const goDetail = (item) => {
  console.log('==========item:', item)
  let eventType
  if (item.messageType == '106') eventType = 4
  else if (item.messageType == '102') eventType = 1
  else if (item.messageType == '111') eventType = 3
  if (eventType) {
    router.push({
      path: '/fireRemoteManage/eventHanding',
      query: {
        ...setPageJsonQueryParamsAdapter({
          eventType: eventType,
          disposeId: item.businessId,
          userId: userInfo.value.userId,
        }),
        isShowBack: 1,
      },
    })
  }
}

const pendingByReceiveList: any = ref([])
const getPendingByReceiveList = async () => {
  const res: any = await queryPendingByReceive({
    superviseId: userInfo.value.orgCode,
    pageNo: 1,
    pageSize: 3,
  })
  console.log(res, '获取待办事项列表')
  if (res && res.code == 'success') {
    pendingByReceiveList.value = res.data.rows || []
  }
}

const handleDetails = (o) => {
  isShow.value = true
  router.push('/staging/todoList/index?active=2&&isShowBack=1&pendingId=' + o.pendingId)
}

const noticeByReceiveList: any = ref([])
const getNoticeByReceiveList = async () => {
  const res: any = await queryNoticeByReceive({
    superviseId: userInfo.value.orgCode,
    pageNo: 1,
    pageSize: 3,
  })
  console.log(res, '获取通知通告列表')
  if (res && res.code == 'success') {
    noticeByReceiveList.value = res.data.rows || []
  }
}

// 系统动态
const systemDynamicsList: any = ref([])
const getSystemDynamicsList = async () => {
  const res: any = await querySystemDynamics({
    superviseId: userInfo.value.orgCode,
    pageNo: pageNo.value,
    pageSize: 3,
  })
  console.log(res, '获取系统动态列表')
  if (res && res.code == 'success') {
    // systemDynamicsList.value = res.data.rows || []
    let rows = res.data.rows
    if (pageNo.value === 1) {
      systemDynamicsList.value = rows
    } else {
      systemDynamicsList.value = systemDynamicsList.value.concat(rows)
    }
    total.value = res.data.total
  }
  systemDynamicsLoading.value = false
}

const noMore = computed(() => {
  // console.log('systemDynamicsList的值:', systemDynamicsList.value.length)
  // console.log('total的值:', total.value)
  return systemDynamicsList.value.length >= total.value
})
const disabled = computed(() => {
  // console.log('systemDynamicsLoading的值:', systemDynamicsLoading.value)
  // console.log('noMore的值:', noMore.value)
  return systemDynamicsLoading.value || noMore.value
})

const pageNo = ref(1)
const total = ref(0)
const systemDynamicsLoading = ref(false)
const load = () => {
  console.log('加载更多...')
  systemDynamicsLoading.value = true
  pageNo.value++
  getSystemDynamicsList()
}

const isDetail = ref(false)
const systemDynamicsInfo = ref({})
const toSystemDetails = async (item) => {
  try {
    const res: any = await getSystemDynamicsDetail({
      superviseSystemDynamicsId: item.superviseSystemDynamicsId,
    })
    console.log('res', res)
    if (res && res.code == 'success') {
      systemDynamicsInfo.value = res.data
      isDetail.value = true
    }
  } catch (error) {
    pageNo.value = 1
    getSystemDynamicsList()
    isDetail.value = false
  }
}

const isRefresh = ref(true)

const init = () => {
  const arr = [
    getLoginLogData(),
    getDecisionList(),
    getInformationList(),
    getPendingByReceiveList(),
    getMessageList(),
    getNoticeByReceiveList(),
    getSystemDynamicsList(),
  ]
  loading.value = true
  Promise.all(arr).finally(() => {
    loading.value = false
    isRefresh.value = false
  })
}

onMounted(() => {
  queryParams.value = {}
  chargeId.value = ''
  timeRange.value = []
  init()
})
</script>

<style lang="scss" scoped>
.staging {
  display: grid;
  grid-template-rows: auto auto 1fr;

  &-rows {
    min-height: 222px;

    .business-card {
      background-image: url(@/assets/image/bj.png);
      background-size: cover;

      :deep(.el-card__body) {
        height: 100% !important;
      }

      &-content {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .avatar {
          flex: 46%;
          text-align: right;
          padding-right: 18px;
        }

        .right {
          flex: 54%;

          :deep(.userName .str-span span) {
            font-size: 28px;
          }

          .role {
            display: flex;
            flex-wrap: wrap;

            > span {
              display: inline-block;
              padding: 5px 15px;
              color: #0080ff;
              background-color: #ebf5ff;
              margin-right: 10px;
              margin-bottom: 10px;
            }
          }

          .login-time {
            color: #a6a6a6;
          }
        }
      }
    }

    .box-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: Microsoft YaHei;
        font-weight: 700;
        color: #333333;

        > span {
          font-size: 16px;
        }

        .el-button {
          height: auto;
          padding: 6px 10px 4px 10px;
          font-size: 14px;
          color: #0080ff;
          border: 1px solid #0080ff;
        }

        .infomationGroup {
          display: inline-flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;

          > span {
            margin: 0 10px;
            cursor: pointer;
          }

          > span.active {
            color: #409eff;
          }

          > i {
            cursor: default;
            color: rgb(153, 153, 153);
          }
        }
      }

      :deep(.el-card__header) {
        height: 56px;
        line-height: 56px;
        padding: 0 20px;
      }

      :deep(.el-card__body) {
        height: calc(100% - 56px);
        padding: 0;
      }

      .content {
        position: relative;
        height: 100%;
        // display: flex;
        display: grid;
        grid-template-rows: repeat(auto-fill, minmax(45px, auto));
        flex-direction: column;
        overflow: auto;

        .item {
          flex: 1;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          padding: 0 20px;
          cursor: pointer;
          // max-height: 52px;
          // line-height: 40px;
          border-bottom: 1px solid #eee;

          > span:not(.ranking) {
            flex: 1;
          }

          > .title:not(.ranking) {
            flex: 2;
          }

          > .time {
            text-align: center;
          }

          .ranking {
            width: 24px;
            height: 24px;
            line-height: 24px;
            margin-right: 6px;
            text-align: center;
            color: #0080ff;
            background: #edf6ff;
            border-radius: 4px;
          }

          .el-button.is-link {
            border-color: transparent;
            color: var(--el-button-text-color);
            background: transparent;
            padding: 2px;
            height: auto;

            :deep(> span) {
              color: #333;
            }
          }

          &:hover {
            background-color: #fafafa;

            :deep(.el-button.is-link > span) {
              color: #0080ff;
            }
          }
        }

        .search {
          align-self: start;
          margin-top: 14px;
          width: 100%;
          display: flex;
          align-items: center;
        }

        :deep(.input-with-select input) {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }

        :deep(.search button) {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }

        :deep(.empty) {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          padding: 0;

          .el-empty__description {
            margin-top: 0px;
          }
        }
      }

      .content.five {
        grid-template-rows: repeat(5, minmax(40px, auto));
      }
      .content.systemDynamics {
        display: flex;
        height: 140px;
        .item {
          padding: 15px 20px;
          max-height: 55px;
        }
      }
    }

    :deep(.knowledge-base .el-input-group__append) {
      // background-color: unset;
      button.el-button {
        // background-color: #0080ff;
        // color: #ffffff;
        // border-color: #0080ff;
      }
    }
  }
}
</style>
