<!-- createdInformation -->
<template>
  <div class="w-full h-full flex flex-col createdInformation">
    <div class="main_search">
      <header-item title="资讯标题">
        <el-input
          v-model="searchFrom.informationTitle"
          placeholder="请输入资讯标题"
          clearable
          @change="init"
        />
      </header-item>
      <!-- <header-item title="资讯来源">
        <equipmentStatus
          class="w-full"
          :options="informationSourceOptions"
          v-model="searchFrom.informationSource"
          @change="init"
        />
      </header-item> -->
      <header-item title="发布时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY-MM-DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>
      <header-item title="发布状态">
        <equipmentStatus
          class="w-full"
          :options="publishStatusOptions"
          v-model="searchFrom.publishStatus"
          @change="init"
        />
      </header-item>
      <div class="export action" style="margin-bottom: 0">
        <el-button
          type="primary"
          class="export-btn"
          @click="$router.push('/staging/information/add')"
        >
          新增资讯
        </el-button>
      </div>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span
                class="operate-item"
                track
                @click="handleDetails(scope.row)"
              >
                详情
              </span>
              <span
                v-if="scope.row.publishStatus == '1'"
                class="operate-item"
                track
                @click="withdrawHandle(scope.row)"
              >
                撤回
              </span>
              <span
                v-if="scope.row.publishStatus == '0'"
                class="operate-item"
                track
                @click="editHandle(scope.row)"
              >
                编辑
              </span>
              <span
                v-if="scope.row.publishStatus == '0'"
                class="operate-item"
                track
                @click="deleteHandle(scope.row)"
              >
                删除
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo, useInformationInfo } from '@/store'
import equipmentStatus from '~/components/public/common-form/equipmentStatusSelect.vue'
import {
  queryInformationByCreate,
  getInformationSourceList,
  delInformation,
  withdrawInformation
} from '@/common/comResponse/index'
import { ElMessage, ElMessageBox } from 'element-plus'

const userInfo = useUserInfo()
const informationInfo = useInformationInfo()
const route: any = useRoute()
const router: any = useRouter()

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const searchFrom = reactive({
  createUserId: userInfo.value.userId, // 创建人ID
  informationSource: '', // 资讯来源
  orderFields: '', // 排序字段 例：publishTime:desc ','隔开Default value : publishTime:desc
  informationType: '', // 资讯类型
  informationTitle: '', // 资讯标题
  publishStartTime: '', //发布时间-开始时间
  publishEndTime: '', //发布时间-结束时间
  publishStatus: '' // 发布状态（0:待发布 1:已发布）
})

const timeAll: any = ref([])

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

const publishStatusOptions = ref([
  {
    label: '待发布',
    value: '0'
  },
  {
    label: '已发布',
    value: '1'
  }
])

const informationSourceOptions = ref<any>([])

/**
 * 点击详情按钮
 * @param item 当前点击的表格的行数据
 */

function handleDetails(item: any) {
  console.log(item, 'item')
  router.push({
    path: '/staging/information/informationDetails',
    query: {
      source: 'information',
      infoId: item.informationId,
      infoCategory: '1',
      isShowBack: '1'
    }
  })
}

/**
 * 撤回
 */
const withdrawHandle = (item) => {
  ElMessageBox({
    title: '',
    type: 'warning',
    message: '撤回后其他用户无法查看该内容，您可以在草稿箱进行编辑修改',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await withdrawInformation({
          informationId: item.informationId,
          createUserId: item.createUserId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '撤回成功！',
            type: 'success'
          })
          done()
          init()
        } else {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
          done()
        }
      } else {
        done()
      }
    }
  })
}

// 编辑
const editHandle = (item) => {
  informationInfo.value = item
  router.push(
    '/staging/information/edit?currentType=edit&source=createdInformation'
  )
}

/**
 * 删除通知通告
 */
const deleteHandle = (item) => {
  ElMessageBox({
    title: '删除',
    type: 'warning',
    message: '确认删除资讯吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        const res: any = await delInformation({
          informationId: item.informationId,
          createUserId: item.createUserId
        })
        console.log(res, 'res')
        instance.confirmButtonLoading = false
        instance.confirmButtonText = '确定'
        if (res && res.code == 'success') {
          ElMessage({
            message: '删除成功！',
            type: 'success'
          })
          done()
          // getInformationSourceOptions()
          init()
        } else {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
          done()
        }
      } else {
        done()
      }
    }
  })
}

function timeChange(val: any) {
  searchFrom.publishStartTime = ''
  searchFrom.publishEndTime = ''
  if (val && val.length > 0) {
    searchFrom.publishStartTime = val[0] + ' 00:00:00'
    searchFrom.publishEndTime = val[1] + ' 23:59:59'
  }
  init()
}

async function getTableData() {
  loading.value = true
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await queryInformationByCreate(params)
  if (res && res.code == 'success') {
    tableData.value = res.data.rows || []
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

function getTableColumns() {
  columns.value = [
    {
      title: '资讯标题',
      key: 'informationTitle',
      showOverflowTooltip: true
    },
    {
      title: '资讯来源',
      key: 'informationSource',
      showOverflowTooltip: true
    },
    {
      title: '创建人',
      key: 'createUserName',
      showOverflowTooltip: true
    },
    {
      title: '发布时间',
      key: 'publishTime',
      showOverflowTooltip: true
    },
    {
      title: '发布状态',
      key: 'publishStatusStr',
      showOverflowTooltip: true
    }
  ]
}

function init() {
  getTableData()
}

const getInformationSourceOptions = async () => {
  informationSourceOptions.value = await getInformationSourceList({
    superviseId: userInfo.value.orgCode,
    createUserId: userInfo.value.userId, // 创建人ID
    infoCategory: '1'
  })
}

onBeforeMount(() => {
  // getInformationSourceOptions()
})

onMounted(() => {
  init()
  getTableColumns()
})
</script>
<style lang="scss" scoped>
.createdInformation .form_item {
  flex: 20%;
}
</style>
