<!-- receiveInformation -->
<template>
  <div class="w-full h-full flex flex-col receiveInformation">
    <div class="main_search">
      <header-item title="资讯类别">
        <equipmentStatus
          class="w-full"
          :options="infoCategoryOptions"
          v-model="searchFrom.infoCategory"
          @change="infoCategoryChange"
        />
      </header-item>
      <header-item title="资讯标题">
        <el-input
          v-model="searchFrom.informationTitle"
          placeholder="请输入资讯标题"
          clearable
          @change="init"
        />
      </header-item>
      <!-- <header-item title="资讯来源">
        <equipmentStatus
          class="w-full"
          :options="informationSourceOptions"
          v-model="searchFrom.informationSource"
          @change="init"
        />
      </header-item> -->
      <header-item title="发布时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY-MM-DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>
    </div>
    <div class="table-list flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #default="scope">
            <template v-if="!scope.row[item.key] && scope.row[item.key] !== 0">
              --
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <div class="table-operate">
              <span
                class="operate-item"
                track
                @click="handleDetails(scope.row)"
              >
                详情
              </span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, onBeforeMount, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '@/common/api'
import { PageModel } from '@/types'
import { useUserInfo } from '@/store'
import equipmentStatus from '~/components/public/common-form/equipmentStatusSelect.vue'
import {
  queryInformationList,
  getInformationSourceList
} from '@/common/comResponse/index'

const userInfo = useUserInfo()
const route: any = useRoute()
const router: any = useRouter()

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const searchFrom = reactive({
  superviseId: userInfo.value.orgCode,
  infoCategory: '', // （0:消防热点资讯 1:中外运消防资讯）
  informationSource: '', // 资讯来源
  informationTitle: '',
  releaseStartTime: '', //发布时间-开始时间
  releaseEndTime: '' //发布时间-结束时间
})

const timeAll: any = ref([])

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

const infoCategoryOptions = ref([
  {
    label: '全部',
    value: ''
  },
  {
    label: '消防热点资讯',
    value: '0'
  },
  {
    label: '中国外运消防资讯',
    value: '1'
  }
])

const informationSourceOptions = ref<any>([])

/**
 * 点击详情按钮
 * @param item 当前点击的表格的行数据
 */

function handleDetails(item: any) {
  console.log(item, 'item')
  router.push({
    path: '/staging/information/informationDetails',
    query: {
      source: 'information',
      infoId: item.informationId,
      infoCategory: item.informationCategory,
      isShowBack: '1'
    }
  })
}

function timeChange(val: any) {
  searchFrom.releaseStartTime = ''
  searchFrom.releaseEndTime = ''
  if (val && val.length > 0) {
    searchFrom.releaseStartTime = val[0]
    searchFrom.releaseEndTime = val[1]
  }
  init()
}

const formatReleaseTime = () => {
  // 资讯类别为中外运消防资讯的时候时间传参必须精确到时分秒,消防热点资讯时间精确到日期
  if (searchFrom.infoCategory == '1') {
    searchFrom.releaseStartTime =
      timeAll.value && timeAll.value.length > 0
        ? timeAll.value[0] + ' 00:00:00'
        : ''
    searchFrom.releaseEndTime =
      timeAll.value && timeAll.value.length > 0
        ? timeAll.value[1] + ' 23:59:59'
        : ''
  } else {
    searchFrom.releaseStartTime =
      timeAll.value && timeAll.value.length > 0 ? timeAll.value[0] : ''
    searchFrom.releaseEndTime =
      timeAll.value && timeAll.value.length > 0 ? timeAll.value[1] : ''
  }
}

async function getTableData() {
  loading.value = true
  await formatReleaseTime()
  const params = {
    ...searchFrom,
    pageSize: pageModel.pageSize,
    pageNo: pageModel.pageNo
  }
  const res: any = await queryInformationList(params)
  if (res && res.code == 'success') {
    tableData.value = res.data.rows || []
    pageModel.total = res.data.total || 0
  }
  loading.value = false
}

function currentChange(pageNo: number) {
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

function getTableColumns() {
  columns.value = [
    {
      title: '资讯标题',
      key: 'informationTitle',
      showOverflowTooltip: true
    },
    {
      title: '资讯来源',
      key: 'informationSource',
      showOverflowTooltip: true
    },
    {
      title: '发布时间',
      key: 'publishTime',
      showOverflowTooltip: true
    }
  ]
}

const infoCategoryChange = () => {
  searchFrom.informationSource = ''
  // getInformationSourceOptions()
  init()
}

function init() {
  getTableData()
}

const getInformationSourceOptions = async () => {
  informationSourceOptions.value = await getInformationSourceList({
    superviseId: userInfo.value.orgCode,
    createUserId: userInfo.value.userId, // 创建人ID
    infoCategory: searchFrom.infoCategory
  })
}

onBeforeMount(() => {
  // getInformationSourceOptions()
})

onMounted(() => {
  if (route.query.infoCategory)
    searchFrom.infoCategory = route.query.infoCategory
  init()
  getTableColumns()
})
</script>
<style lang="scss" scoped></style>
