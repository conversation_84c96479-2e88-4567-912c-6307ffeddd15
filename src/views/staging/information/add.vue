<template>
  <div class="formView h-full">
    <h3 class="text-16px font-bold mb-20px">
      {{ currentType === 'edit' ? '编辑' : '创建' }}资讯
    </h3>
    <el-form
      ref="formRef"
      :model="informationForm"
      :rules="rules"
      :label-width="95"
      :label-position="labelPosition"
      class="bg-white w-full"
    >
      <el-row class="!mb-0px">
        <el-col :span="7">
          <el-form-item
            label="资讯标题"
            :label-width="95"
            prop="informationTitle"
          >
            <el-input
              v-model="informationForm.informationTitle"
              maxlength="50"
              placeholder="请输入资讯标题"
              autocomplete="off"
              clearable
              show-word-limit
              class="flex-1 h-full"
            />
          </el-form-item>
          <el-form-item label="资讯来源" prop="informationSource">
            <el-input
              v-model="informationForm.informationSource"
              maxlength="20"
              placeholder="请输入资讯来源"
              autocomplete="off"
              clearable
              show-word-limit
              class="flex-1 h-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-dialog
        v-model="dialogFormVisible"
        title="选择发布时间"
        width="30%"
        align-center
        destroy-on-close
        custom-class="publishTimeModel"
      >
        <el-form-item label="" prop="publishTime">
          <el-date-picker
            style="width: 100%"
            v-model="informationForm.publishTime"
            type="datetime"
            clearable
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择发送时间"
            :disabled="informationForm.isNowPublish == '1'"
          />
        </el-form-item>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="regularlyPosted(formRef)">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
      <el-row>
        <el-col :span="24" class="h-full">
          <el-form-item
            label="资讯内容"
            prop="informationContent"
            class="informationContent"
          >
            <rich-editor
              :upload-url="'/information/uploadFile'"
              :containerStyle="{ width: '100%' }"
              :editorStyle="{ height: '220px' }"
              @updateNoticeFormContent="updateNoticeFormContent"
              :default-value="informationForm.informationContent"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="justify-between">
        <div></div>
        <div>
          <el-button @click="goBack" ref="cancelBtnRef">取消</el-button>
          <el-button
            v-if="$route.query.source !== 'createdInformation'"
            type="primary"
            :loading="draftLoading"
            plain
            @click="saveDraft(formRef)"
          >
            存为草稿
          </el-button>
          <el-button
            type="primary"
            :loading="publishLoading"
            plain
            @click="dialogFormVisible = true"
          >
            定时发布
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="publishNow(formRef)"
          >
            立即发布
          </el-button>
        </div>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, onUnmounted, computed } from 'vue'
import $API from '~/common/api'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import { useUserInfo, useInformationInfo } from '@/store'
import richEditor from '@/components/public/wangeditor.vue'
import { ElMessage, ElMessageBox, ElDatePicker } from 'element-plus'
import type { ElForm } from 'element-plus'
type FormInstance = InstanceType<typeof ElForm>
interface typeOp {
  [key: string]: any
}
const props: any = defineProps({
  row: {
    type: Object,
    default: () => {}
  }
})

const router = useRouter()
const route = useRoute()
const isPublish = ref(false)

const labelPosition = ref('top')
const ui = useUserInfo()

const informationInfo = useInformationInfo()

const draftLoading = ref(false)
const publishLoading = ref(false)
const loading = ref(false)

const currentType = ref('add')

let isFirst = true
const updateNoticeFormContent = (valueHtml) => {
  console.log(valueHtml)
  informationForm.informationContent = valueHtml
  if (isFirst) {
    isFirst = false
  } else {
    formRef.value!.validateField('informationContent')
  }
}

console.log($API.nextHalfHour())

const informationForm: typeOp = reactive({
  informationTitle: '',
  informationSource: '',
  informationContent: '',
  informationType: '',
  publishTime: $API.nextHalfHour(),
  createTime: '',
  createUserId: ui.value.userId,
  createUserName: ui.value.userName,
  informationId: '',
  isNowPublish: '0',
  publishStatus: 0,
  updateTime: '',
  superviseId: ui.value.orgCode,
  updateUserId: ui.value.userId,
  updateUserName: ui.value.userName
})

const formRef = ref<FormInstance>()

const isRequired = computed(() => {
  return informationForm.publishStatus != -1
})

const rules = reactive({
  informationTitle: [
    {
      required: true,
      message: '请输入资讯标题',
      trigger: 'change'
    }
  ],
  informationSource: [
    {
      required: true,
      message: '请输入资讯来源',
      trigger: 'change'
    }
  ],

  publishTime: [
    {
      required: isRequired,
      message: '请选择发送时间',
      trigger: 'change'
    }
  ],
  informationContent: [
    {
      required: true,
      message: '请输入内容',
      trigger: 'change'
    }
  ]
})

const emit = defineEmits(['cancel'])
const cancel = () => {
  emit('cancel', false)
}

const dialogFormVisible = ref(false)
// 定时发布
function regularlyPosted(formEl: FormInstance | undefined) {
  if (informationForm.publishTime) dialogFormVisible.value = false
  informationForm.publishStatus = 0
  isPublish.value = true
  submitForm(formEl, publishLoading.value, '0')
}

function saveDraft(formEl: FormInstance | undefined) {
  const tit = '是否将该内容存为草稿？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        isPublish.value = true
        informationForm.publishStatus = -1
        submitForm(formEl, draftLoading.value, '0', 1)
      } else {
        informationForm.publishStatus = 0
        submitForm(formEl, draftLoading.value, '0', -1)
      }

      done()
    }
  })
}

function publishNow(formEl: FormInstance | undefined) {
  const tit = '是否立即发布资讯内容？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        isPublish.value = true
        informationForm.publishStatus = 1
        submitForm(formEl, loading.value, '1')
      } else {
        informationForm.isNowPublish = '0'
      }
      done()
    }
  })
}

/*
 * isDraftType 1: 确认 -1: 取消
 * isNowPublish 1: 立即发布 0: 不立即发布
 */
async function submitForm(
  formEl: FormInstance | undefined,
  load: Boolean,
  isNowPublish?: String | Number,
  isDraftType?: Number,
  to?: Object
) {
  informationForm.createTime = informationForm.createTime
    ? informationForm.createTime
    : $API.today()
  informationForm.updateTime = $API.today()

  informationForm.updateTime = informationForm.updateTime
    ? informationForm.updateTime
    : $API.today()

  console.log(informationForm)
  if (!formEl) return

  let isEmpty = false

  await formEl.validate(async (valid) => {
    if (valid || isDraftType) {
      if (isDraftType) formEl.clearValidate()
      if (isDraftType === -1) return
      if (
        !informationForm.informationTitle &&
        !informationForm.informationSource &&
        !informationForm.informationContent &&
        isDraftType === 1
      ) {
        isPublish.value = false
        isEmpty = true
        return ElMessage.warning('资讯标题、资讯来源、资讯内容不能全部都为空！')
      }

      load = true
      informationForm.isNowPublish = isNowPublish
      let publishTime = informationForm.publishTime
      if (informationForm.isNowPublish == '0') {
        const nowTime = Date.now()
        const timeStamp = new Date(informationForm.publishTime).getTime()
        if (nowTime >= timeStamp) {
          informationForm.publishTime = ''
          return ElMessage.warning('当前发送时间早于当前时间,请重新选择时间！')
        }
      } else if (informationForm.isNowPublish == '1') {
        publishTime = $API.today()
      }

      if (currentType.value == 'add') {
        const res: any = await $API.post({
          url: '/information/addInformation',
          data: {
            ...informationForm,
            publishTime
          }
        })
        if (res.code == 'success') ElMessage.success('新增成功！')
      } else if (currentType.value === 'edit') {
        const res: any = await $API.post({
          url: '/information/editInformation',
          data: {
            ...informationForm,
            publishTime
          }
        })
        if (res.code == 'success') ElMessage.success('编辑成功！')
      }

      if (!to) {
        let active = isDraftType === 1 ? 3 : 1
        router.push(
          '/staging/information/index?isShowBack=1&jumpAddress=/staging/index&active=' +
            active
        )
      }
      load = false
      cancel()
    }
  })
  return isEmpty
}

onMounted(() => {
  if (route.query.currentType) {
    currentType.value = route.query.currentType as string
  }

  if (currentType.value == 'add') {
    informationInfo.value = ''
  } else if (currentType.value == 'edit') {
    for (let key in informationInfo.value) {
      // 编辑的时候还是取当前账号的id和名称,更新时间取最新的时间
      if (
        informationForm.hasOwnProperty(key) &&
        !['updateTime', 'updateUserId', 'updateUserName'].includes(key) &&
        informationInfo.value.hasOwnProperty(key)
      ) {
        // 将源对象的值赋值给目标对象的对应键
        informationForm[key] = informationInfo.value[key]
        if (route.query.isDrafts === 'true') {
          informationForm.publishTime = $API.nextHalfHour()
        }
      }
    }
  }
  console.log(informationForm, 'informationForm')
})

onUnmounted(() => {})
const cancelBtnRef = ref()
const goBack = () => {
  isPublish.value = true
  if (route.query.isDrafts === 'true' && route.query.currentType === 'edit') {
    router.push(
      '/staging/information/index?isShowBack=1&jumpAddress=/staging/index&active=3'
    )
  } else exitCommonHandle(null)
  cancelBtnRef.value.ref.blur()
}

const exitCommonHandle = (next, to: any = '') => {
  const tit = '即将离开当前页面,请确认是否将内容存为草稿？'
  const tits = ''
  ElMessageBox({
    title: tits,
    message: tit,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showClose: false,
    closeOnClickModal: false,
    closeOnPressEscape: false,
    type: 'warning',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        informationForm.publishStatus = -1
        const flag = await submitForm(
          formRef.value,
          draftLoading.value,
          '0',
          1,
          to
        )
        done()
        if (!flag && next) next()
      } else {
        done()
        if (next) next()
        else
          router.push(
            '/staging/information/index?isShowBack=1&jumpAddress=/staging/index&active=1'
          )
      }
    }
  }).catch(() => {})
}

onBeforeRouteLeave((to, from, next) => {
  if (!isPublish.value) {
    exitCommonHandle(next, to)
  } else {
    next()
  }
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss" scoped>
:deep(.el-textarea .el-input__count) {
  bottom: -25px !important;
}

.add-dialog {
  &:deep(.el-dialog__header) {
    text-align: left;
  }
}

.formView {
  padding: 20px;
  background-color: #fff;

  :deep(.el-form-item .el-form-item__label) {
    width: 110px !important;
  }
  :deep(.publishTimeModel) {
    margin-top: 35vh;
  }

  :deep(.publishTimeModel .el-input__inner) {
    padding-left: 40px !important;
  }
}
</style>
