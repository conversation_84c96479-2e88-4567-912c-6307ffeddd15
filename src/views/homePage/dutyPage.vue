<template>
  <div class="w-full h-full duty-page overflow-y-scroll">
    <div class="box-broder" v-if="offlineList.length > 0">
      <div class="info-tip">
        <el-carousel direction="vertical" class="w-full h-full" indicator-position="none">
          <el-carousel-item
            v-for="(item, index) in offlineList"
            :key="index"
            class="flex items-center"
            track
            @click="handleTipClick(item)"
          >
            <img src="@/assets/image/dutyPage/Tip.png" alt="" class="mr-10px w-22px h-22px" />
            <span class="text-16px ml-10px cursor-pointer"
              >{{ item.deviceTypeName }}{{ item.description }}（离线时间：{{ item.offLineTime }}）</span
            >
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
    <div class="w-full h-60px flex box-border px-20px">
      <!-- <div class="switch-dispose flex h-full">
                <div v-for="item in buttons" :key="item.value" @click="changeActive(item)"
                :class="{active: item.value === activeValue}">
                    <button class="w-full h-full text-18px">{{item.label}}</button>
                </div>
            </div> -->
      <div class="w-full box-border relative">
        <el-tabs v-model="activeValue">
          <el-tab-pane v-for="item in buttons" :key="item.value" :label="item.label" :name="item.value"> </el-tab-pane>
        </el-tabs>
        <span class="theme-color text-14px cursor-pointer absolute view-more" track @click="router.push('/treatment')"
          >查看更多</span
        >
      </div>
      <!-- <div class="flex-1 pr-20px box-broder">
                <div class="info-tip" v-if="offlineList.length > 0">
                    <el-carousel direction="vertical" class="w-full h-full" indicator-position="none">
                        <el-carousel-item  v-for="(item,index) in offlineList" :key="index" class="flex items-center" @click="handleTipClick(item)">
                            <img src="@/assets/image/dutyPage/Tip.png" alt="" class="mr-10px w-22px h-22px"/>
                            <span class="text-16px ml-10px cursor-pointer">{{item.deviceTypeName}}{{item.description}}（离线时间：{{item.offLineTime}}）</span>
                        </el-carousel-item>
                    </el-carousel>
                </div>
            </div> -->
    </div>
    <div
      class="w-full flex flex-col h-[calc(100%-60px)]"
      :class="offlineList.length > 0 ? 'h-[calc(100%-120px)]' : 'h-[calc(100%-60px)]'"
    >
      <div class="w-full min-h-[calc(100%-400px)] box-border">
        <div class="w-full h-full flex overflow-hidden">
          <div class="min-h-full w-3/5 pr-20px box-border">
            <fire-safety-manage></fire-safety-manage>
          </div>
          <div class="min-h-full bg-white flex-1 p-20px box-border monitor-wrap">
            <div class="flex justify-between items-center mb-20px">
              <div class="block-title">实时监测</div>
              <span
                track
                @click="showExplain = true"
                class="theme-color cursor-pointer py-6px px-10px text-14px inline-block border-1px border-solid"
                style="border-color: #1890ff; border-radius: 4px"
                >查看监测项说明 ></span
              >
            </div>
            <ul class="monitor-list clear-both">
              <li
                class="relative cursor-pointer flex flex-col justify-center p-30px box-border"
                v-for="(item, index) in monitorList"
                :key="index"
                track
                @click="handleClick(item)"
              >
                <div class="flex items-center justify-between">
                  <div :class="[item.className]"></div>
                  <p class="text-20px">{{ item.label }}({{ item.unit }})</p>
                </div>
                <p class="text-40px text-right">{{ item.count }}</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <popup-side popup-title="监测项说明" v-model="showExplain">
    <monitorInfo></monitorInfo>
  </popup-side>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { EVENT_TYPE } from '@/common/eventType'
import disposeList from '~/components/dutyPage/disposeList.vue'
import fireSafetyManage from '~/components/dutyPage/fireSafetyManage.vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'
import { isNineType, setTempData } from '@/common/utils'
import { useRouter } from 'vue-router'
import monitorInfo from '@/components/public/monitorInfo.vue'

const userInfo = useUserInfo()

const router = useRouter()

const floorViewRef = ref()

const buttons = ref([
  { label: '待处置', value: '0' },
  { label: '处置记录', value: '1' },
])

const monitorMap = ref({
  [EVENT_TYPE.ALARM]: {
    label: '火警',
    unit: '起',
    count: 0,
    className: 'fire',
  },
  [EVENT_TYPE.WARING]: {
    label: '预警',
    unit: '起',
    count: 0,
    className: 'warning',
  },
  [EVENT_TYPE.ACTION]: {
    label: '动作',
    unit: '起',
    count: 0,
    className: 'action',
  },
  [EVENT_TYPE.FAULT]: {
    label: '故障',
    unit: '起',
    count: 0,
    className: 'fault',
  },
  [EVENT_TYPE.HIDDEN_DANGER]: {
    label: '隐患',
    unit: '处',
    count: 0,
    className: 'hidden-danger',
  },
  [EVENT_TYPE.OFFLINE]: {
    label: '离线',
    unit: '起',
    count: 0,
    className: 'offline',
  },
})

Object.keys(monitorMap.value).forEach((key) => {
  monitorMap.value[key].type = Number(key)
})

const activeValue = ref(buttons.value[0].value)

const offlineList = ref([] as any[])

const showExplain = ref(false)

const monitorList = computed(() => {
  const list: any[] = [],
    m = monitorMap.value as any
  Object.keys(m).forEach((type) => list.push(m[type]))
  return list
})

function handleClick(item) {
  setTempData({
    eventType: item.type,
  })

  router.push('/monitor/real-time-monitor')
}

function handleRowClick(item: any) {
  floorViewRef.value.showFloor(item)
}

function statisticDeviceEventNums() {
  const ui = userInfo.value
  let isNine = isNineType(ui)
  let params = {
    unitId: ui.unitId,
    unitType: '3',
  }
  // if(isNine){
  //     params.unitType = '3'
  // }else{
  //     params.unitType = ui.unitType
  // }

  $API
    .post({
      url: '/deviceEvent/statisticDeviceEventNums',
      params: params,
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        const d = res.data,
          m = monitorMap.value

        Object.keys(m).forEach((type) => {
          const item = m[type],
            t = type

          if (t == EVENT_TYPE.ALARM) {
            item.count = d.alarmNum
          } else if (t == EVENT_TYPE.ACTION) {
            item.count = d.movingNum
          } else if (t == EVENT_TYPE.FAULT) {
            item.count = d.troubleNum
          } else if (t == EVENT_TYPE.HIDDEN_DANGER) {
          } else if (t == EVENT_TYPE.OFFLINE) {
            item.count = d.offlineNum
          } else if (t == EVENT_TYPE.WARING) {
            item.count = d.warningNum
          }
        })
      }
    })
}

function handleTipClick(item) {
  const obj = {
    tempId: item.deviceId,
    eventType: EVENT_TYPE.OFFLINE,
  }

  setTempData(obj)
  router.push('/monitor/real-time-monitor')
}

function getTransDeviceOfflineList() {
  const ui = userInfo.value
  $API
    .post({
      url: '/deviceEvent/getTransDeviceOfflineList',
      params: {
        ownerId: ui.unitId,
        ownerType: ui.unitType,
      },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        offlineList.value = res.data
      }
    })
}

function changeActive(item) {
  activeValue.value = item.value
}

onMounted(() => {
  // statisticDeviceEventNums();
  // getTransDeviceOfflineList();
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
@mixin monitor-bg($name) {
  height: 50px;
  width: 50px;
  background-image: url('@/assets/image/homePage/' + $name + '.png');
  background-size: 100%;
  background-repeat: no-repeat;
}

.duty-page {
  .el-tabs__nav-wrap::after {
    opacity: 0.3;
  }

  .view-more {
    // right: 54px;
    right: 0;
    top: 20%;
  }

  .layout-top {
    border-radius: 10px 10px 10px 10px;
    flex: 0 0 400px;
  }

  .info-tip {
    font-size: 14px;
    color: #ff4d4f;
    height: 50px;
    line-height: 50px;
    border-radius: 10px;
    background: rgba(255, 77, 79, 0.1);
    box-sizing: border-box;
    padding-left: 20px;
    margin-bottom: 5px;

    .el-carousel {
      .el-carousel__container {
        height: 100%;
      }
    }
  }

  .switch-dispose {
    > div {
      position: relative;
      width: 200px;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1;
        background: #eefdff;
        border-bottom: none;
        border-radius: 10px 10px 0px 0;
        transform: scaleY(1.2) perspective(16px) rotateX(4deg);
        transform-origin: bottom left;
        /* 控制倾斜的方向 */
      }

      button {
        position: relative;
        z-index: 2;
        left: -10px;
        font-family: 'Alibaba-PuHuiTi-Medium';
      }
    }

    .active {
      &::after {
        background: white;
      }

      button {
        color: #1890ff;
      }
    }
  }

  .map-wrapper {
    box-shadow: 0 0 10px rgba(26, 148, 254, 0.5);
    border-radius: 10px;
    overflow: hidden;
  }

  .monitor-wrap {
    border-radius: 10px;

    .monitor-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      li {
        width: 33.3%;
        text-align: center;
        color: white;
        float: left;
        background-size: 100% 100%;
        // height: 130px;
        // background: linear-gradient(to left, #8DDCFF, #58CCFF);
        background-image: url(@/assets/image/block-bg.png);
        background-size: 100%;
        border-radius: 24px;
        // margin-bottom: 20px;
      }

      .fire {
        @include monitor-bg('fire');
      }

      .hidden-danger {
        @include monitor-bg('hidden-danger');
      }

      .action {
        @include monitor-bg('action');
      }

      .offline {
        @include monitor-bg('offline');
      }

      .fault {
        @include monitor-bg('fault');
      }

      .warning {
        @include monitor-bg('warning');
      }
    }
  }
}
</style>
