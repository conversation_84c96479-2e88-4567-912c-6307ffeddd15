<template>
  <div class="w-full principal-page h-full">
    <!-- <header-component></header-component>
    <safety-exponent></safety-exponent>
    <safety-manage-workable></safety-manage-workable>
    <safety-manage-echarts></safety-manage-echarts> -->
  </div>
</template>

<script lang="ts" setup>
// import headerComponent from '~/components/mainPage/headerComponent.vue'
// import safetyExponent from '~/components/mainPage/safetyExponent.vue';
// import safetyManageWorkable from '~/components/mainPage/safetyManageWorkable.vue';
// import safetyManageEcharts from "~/components/mainPage/safetyManageEcharts.vue";
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.principal-page {
  padding: 0 20px;
  box-sizing: border-box;
  position: relative;
  top: -20px;
  // background: pink;
  .global-header {
    // width: calc(100% - 50px);
    padding-right: 0;
  }
}
</style>