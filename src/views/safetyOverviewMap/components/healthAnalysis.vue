<template>
  <div class="health-list">
    <div v-for="(item, index) in healthItems" :key="index" class="health-item" :class="{ 'odd-bg': index % 2 === 0 }">
      <div class="item-content">
        <div class="item-icon">
          <img :src="getIconType(index)" alt="icon" class="icon-img" />
          <span class="score">{{ item.score }}</span>
        </div>
        <div class="item-details">
          <div class="name-progress">
            <span class="item-name">{{ item.name }}</span>
            <div class="progress-container">
              <div class="progress-bar" :style="{ width: item.percentage + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

// 使用 @/assets 别名引入图片
import zuanshiIcon from '@/assets/image/safeMap/zuanshi.png'
import huangjinIcon from '@/assets/image/safeMap/huangjin.png'
import baiyinIcon from '@/assets/image/safeMap/baiyin.png'

const healthItems = ref([
  { name: '唐能光伏运维中心光储电站', score: 98, percentage: 98 },
  { name: '林西运维中心唐源光储电站', score: 95, percentage: 95 },
  { name: '旗运维中心风储电站', score: 93, percentage: 93 },
  { name: '晟源光储电站', score: 90, percentage: 90 },
  { name: '陶尔海光储电站', score: 88, percentage: 88 },
  { name: '海勒斯风储电站', score: 85, percentage: 85 },
  { name: '兴盛二期光伏配建储能电站', score: 82, percentage: 82 },
  // { name: '海勒斯风储电站', score: 80, percentage: 80 },
])

const getIconType = (index: number) => {
  if (index < 3) return zuanshiIcon
  if (index < 6) return huangjinIcon
  return baiyinIcon
}
</script>

<style scoped>
.health-item {
  padding: 0px 16px;
  margin-bottom: 5px;
}

.odd-bg {
  background: rgba(8, 139, 255, 0.08);
  border-radius: 4px;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-icon {
  position: relative;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.score {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.item-details {
  flex: 1;
  min-width: 0;
}

.name-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.item-name {
  display: flex;
  align-items: center;
  width: 172px;
  height: 20px;
  color: #fff;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.progress-container {
  position: relative;
  height: 8px;
  background: #0f2940;
  overflow: hidden;
  width: 50%;
}

.progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #088bff 0%, rgba(8, 139, 255, 0.1) 100%);
}
</style>
