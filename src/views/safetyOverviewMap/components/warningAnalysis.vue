<template>
  <div class="h-full w-full flex flex-col">
    <div class="flex-1 w-full h-full">
      <AlarmTrendChart :warningAnalysisData="warningAnalysisData" class="w-full h-full" />
    </div>
    <div class="flex-1 w-full h-full">
      <AlarmTypeChart :warningAnalysisData="warningAnalysisData" class="w-full h-full" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import AlarmTrendChart from './AlarmTrendChart.vue'
import AlarmTypeChart from './AlarmTypeChart.vue'

defineProps({
  warningAnalysisData: {
    type: Object,
    default: () => ({}),
  },
})
</script>
