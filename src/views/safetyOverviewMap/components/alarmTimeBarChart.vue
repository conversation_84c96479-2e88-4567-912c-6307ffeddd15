<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts" setup>
import { ref, Ref, watch, nextTick } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'

const props = defineProps({
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: '100%',
  },
  alarmTimeBarData: {
    type: Object as () => any,
    default: () => {},
  },
})
const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>)
const echarts = useECharts(chartRef as Ref<HTMLDivElement>).echarts

// 模拟数据
const xAxisData = ref([])

// 图表数据
const seriesData = ref([
  {
    name: '消防火警次数',
    data: [],
    key: 'alarmTrend',
    lineColor: '#35FFF4',
  },
  {
    name: '消防预警次数',
    data: [],
    key: 'warnTrend',
    lineColor: '#FFC233',
  },
  {
    name: '消防故障次数',
    data: [],
    key: 'faultTrend',
    lineColor: '#FF6B6B',
  },
  {
    name: '消防隐患次数',
    data: [],
    key: 'hazardTrend',
    lineColor: '#A162E8',
  },
  {
    name: '储能预警次数',
    data: [],
    key: 'storageWarnTrend',
    lineColor: '#4CAF50',
  },
])

// 初始化图表
const initChart = () => {
  setOptions({
    backgroundColor: 'transparent',
    grid: {
      left: '2%',
      right: '2%',
      top: '35%',
      bottom: '5%',
      containLabel: true,
    },
    toolbox: {
      show: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)',
          width: 1,
          type: 'solid',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    legend: {
      data: seriesData.value?.map((item) => item.name),
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
      },
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 10,
      top: 0,
      left: '10px',
      itemStyle: {
        borderWidth: 0,
      },
      icon: 'rect',
    },
    xAxis: {
      type: 'category',
      data: xAxisData.value,
      boundaryGap: true,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          width: 1,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 14,
        //  interval: 1,
        interval: Math.max(0, Math.floor(xAxisData.value.length / 6)), // 确保至少显示一个刻度
        margin: 10,
        formatter: function (value: string, index: number) {
          // 只显示第0个和最后一个，以及中间的4个等分点
          const total = xAxisData.value.length - 1
          if (index === 0 || index === total || (index % Math.ceil(total / 5) === 0 && index < total)) {
            return value
          }
          return ''
        },
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed',
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 14,
      },
    },
    series: seriesData.value?.map((item) => ({
      name: item.name,
      type: 'bar',
      barWidth: 6,
      barGap: '30%',
      barCategoryGap: '40%',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: item.lineColor },
          { offset: 1, color: item.lineColor.replace(')', ', 0.5)').replace('rgb', 'rgba') },
        ]),
      },
      emphasis: {
        itemStyle: {
          color: item.lineColor,
        },
      },
      data: item.data,
    })),
  })
}

// 监听数据变化
watch(
  () => props.alarmTimeBarData,
  (newVal) => {
    if (newVal?.dataY) {
      xAxisData.value = newVal.dataX || xAxisData.value

      // 更新 seriesData 中的数据
      seriesData.value = seriesData.value.map((seriesItem) => {
        if (newVal.dataY[seriesItem.key]) {
          return {
            ...seriesItem,
            data: newVal.dataY[seriesItem.key] || [],
          }
        }
        return seriesItem
      })

      nextTick(() => {
        initChart()
      })
    }
  },
  { immediate: true, deep: true }
)
</script>
