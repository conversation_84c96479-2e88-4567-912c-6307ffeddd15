export const data = [
  { value: 500, name: '超前预警' },
  { value: 310, name: '火警' },
  { value: 274, name: '隐患' },
  { value: 400, name: '告警' },
  { value: 310, name: '预警' },
  { value: 274, name: '三合一' },
  { value: 400, name: '四合一' },
]
// 求和
export function sumValues(arr: { value: number; name: string }[]): number {
  return arr.reduce((acc, curr) => acc + curr.value, 0)
}

export const getLineData = (() => {
  const category: any[] = []
  let dottedBase = +new Date()
  const lineData: any[] = []
  const barData: any[] = []

  for (let i = 0; i < 20; i++) {
    const date = new Date((dottedBase += 1000 * 3600 * 24))
    category.push([date.getFullYear(), date.getMonth() + 1, date.getDate()].join('-'))
    const b = Math.random() * 200
    const d = Math.random() * 200
    barData.push(b)
    lineData.push(d + b)
  }
  return { barData, category, lineData }
})()
