<template>
  <div ref="chartRef" :style="{ width, height }"></div>
</template>

<script lang="ts" setup>
import { Ref, ref, watch, nextTick } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import type { EChartsOption } from 'echarts'

const props = withDefaults(
  defineProps<{
    width?: string
    height?: string
    alarmTypePieData: Record<string, any>
  }>(),
  {
    width: '100%',
    height: '100%',
    alarmTypePieData: () => ({}),
  }
)

const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>)

const chartSeries = [
  { name: '消防火警次数', key: 'alarmNum', color: '#3AA0FF' },
  { name: '消防预警次数', key: 'warnNum', color: '#36CBCB' },
  { name: '消防故障次数', key: 'faultNum', color: '#4DCB73' },
  { name: '消防隐患次数', key: 'hazardNum', color: '#FAD337' },
  { name: '储能预警次数', key: 'storageWarnNum', color: '#F2637B' },
]

interface ChartDataItem {
  name: string
  value: number
  itemStyle: {
    color: string
  }
}

const initChart = () => {
  if (!chartRef.value) return

  const chartData: ChartDataItem[] = chartSeries.map((item) => ({
    name: item.name,
    value: Number(props.alarmTypePieData?.[item.key] || 0),
    itemStyle: { color: item.color },
  }))

  const total = chartData.reduce((sum, item) => sum + (item.value || 0), 0)

  const option: EChartsOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item' as const,
      formatter: (params: any) => {
        const data = params as { name: string; value: number; percent: number }
        return `${data.name}: ${data.value} (${data.percent}%)`
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    legend: {
      orient: 'vertical' as const,
      right: '5%',
      top: '10%',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 16,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        rich: {
          name: {
            width: 70,
            color: 'rgba(255, 255, 255, 0.8)',
            padding: [0, 20, 0, 10],
          },
          value: {
            // color: '#35FFF4',
          },
          percent: {
            color: 'rgba(255, 255, 255, 1)',
            padding: [0, 20, 0, 0],
          },
        },
      },
      formatter: (name: string) => {
        const item = chartData.find((d) => d.name === name)
        if (!item) return name
        const percent = total > 0 ? ((item.value / total) * 100).toFixed(2) : '0'
        return `{name|${name}}{percent|${percent}%}{value|${item.value}个}`
      },
    },
    toolbox: {
      show: false,
    },
    series: [
      {
        name: '告警类型',
        type: 'pie',
        center: ['25%', '50%'] as [string, string],
        radius: ['50%', '80%'],
        left: 0,
        data: chartData,
        avoidLabelOverlap: false,
        label: { show: false },
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 0,
            shadowOffsetX: 0,
            shadowColor: 'transparent',
          },
        },
        itemStyle: {
          borderRadius: 1,
          borderColor: 'transparent',
          borderWidth: 0,
        },
      },
    ],
  }
  setOptions(option as EChartsOption, { isEmpty: total === 0 })
}

watch(
  () => props.alarmTypePieData,
  () => {
    nextTick(() => {
      initChart()
    })
  },
  { deep: true, immediate: true }
)
</script>

<style scoped>
:deep(.echarts) {
  width: 100%;
  height: 100%;
}

:deep(.echarts div) {
  width: 100%;
  height: 100%;
}
</style>
