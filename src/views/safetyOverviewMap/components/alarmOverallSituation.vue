<template>
  <div class="alarm-overall-situation">
    <!-- 左侧：消防安全和储能安全 -->
    <div class="left-section">
      <div class="safety-card fire-safety">
        <div class="safety-header">
          <div class="safety-title">消防安全</div>
        </div>
      </div>
      <div class="safety-card energy-safety">
        <div class="safety-header">
          <div class="safety-title">储能安全</div>
        </div>
      </div>
    </div>

    <!-- 右侧：报警统计 -->
    <div class="right-section">
      <div class="alarm-stats">
        <div class="safety-content">
          <div
            v-for="(item, index) in alarmStatsData"
            :key="'stat-' + index"
            :class="[
              'alarm-stat-item',
              item.type,
              {
                'fire-alarm': item.type === 'fire' || item.type === 'serious',
                'normal-alarm': item.type !== 'fire' && item.type !== 'serious',
              },
            ]"
          >
            <div class="stat-icon">
              <img :src="item.icon" :alt="item.name" />
            </div>
            <div class="stat-info">
              <div class="stat-value">
                {{ item.value }}
                <!-- <span class="stat-unit">{{ item.unit }}</span> -->
              </div>
              <div class="stat-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const store = useUserInfo().value

const fireIcon = new URL('@/assets/image/safeMap/fire.png', import.meta.url).href
const warningIcon = new URL('@/assets/image/safeMap/waring.png', import.meta.url).href

// 右侧报警统计数据
const alarmStatsData = ref([
  {
    name: '火警',
    value: 0,
    unit: '个',
    icon: fireIcon,
    type: 'fire',
  },
  {
    name: '预警',
    value: 0,
    unit: '个',
    icon: warningIcon,
    type: 'warning',
  },
  {
    name: '故障',
    value: 0,
    unit: '个',
    icon: warningIcon,
    type: 'fault',
  },
  {
    name: '隐患',
    value: 0,
    unit: '个',
    icon: warningIcon,
    type: 'hidden-danger',
  },
  {
    name: '离线',
    value: 0,
    unit: '个',
    icon: warningIcon,
    type: 'offline',
  },
  {
    name: '严重预警',
    value: 0,
    unit: '个',
    icon: warningIcon,
    type: 'serious',
  },
])

const getAlarmStats = async () => {
  const res = await $API.post({
    url: '/safetyOverview/getAlarmSituation',
    data: {
      superviseId: store.orgCode,
      unitId: store.unitId,
    },
  })
  alarmStatsData.value[0].value = res.data.fireSafety.alarmNum || alarmStatsData.value[0].value
  alarmStatsData.value[1].value = res.data.fireSafety.warnNum || alarmStatsData.value[1].value
  alarmStatsData.value[2].value = res.data.fireSafety.faultNum || alarmStatsData.value[2].value
  alarmStatsData.value[3].value = res.data.fireSafety.hazardNum || alarmStatsData.value[3].value
  alarmStatsData.value[4].value = res.data.storageSafety.warnAllNum || alarmStatsData.value[4].value
  alarmStatsData.value[5].value = res.data.storageSafety.warnSeriousNum || alarmStatsData.value[5].value
}

onMounted(() => {
  getAlarmStats()
})
</script>

<style lang="scss" scoped>
.alarm-overall-situation {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

.left-section {
  display: flex;
  flex-direction: column;
  width: 125px;
  margin-right: 10px;
  .safety-card {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;

    .safety-header {
      height: 100%;
      position: relative;
      z-index: 2;

      .safety-title {
        width: 100%;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        position: absolute;
        top: 35%;
        left: 100%;
        transform: translate(-50%, -50%);
      }
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-size: cover;
      background-position: center;
      opacity: 0.8;
      z-index: 1;
    }
  }
}

.right-section {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  .safety-card {
    background: rgba(255, 255, 255, 0.1);
    margin-bottom: 16px;
    .safety-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .safety-title {
        color: #fff;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .alarm-stats {
    .safety-content {
      display: flex;
      flex-wrap: wrap;
    }
  }
}

/* 报警统计项样式 */
.alarm-stat-item {
  width: 78px;
  height: 62px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 14px;
  box-sizing: border-box;

  .stat-icon {
    display: none;
  }

  .stat-info {
    text-align: center;
    .stat-name {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 4px;
    }

    .stat-value {
      font-size: 20px;
      font-weight: bold;
      color: #fff;

      .stat-unit {
        font-size: 14px;
        margin-left: 2px;
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
  .safety-content {
    flex: 1;
  }
  &.fire,
  &.serious {
    background-image: url('@/assets/image/safeMap/fire.png');
  }

  &.warning,
  &.fault,
  &.hidden-danger,
  &.offline,
  &.normal-alarm {
    background-image: url('@/assets/image/safeMap/waring.png');
  }
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 10px;
  box-sizing: border-box;
  transition: all 0.3s;
}

.fire-safety {
  &::before {
    background-image: url('@/assets/image/safeMap/fireSafety.png');
    background-repeat: no-repeat;
    height: 48px;
    width: 125px;
    background-size: 100% 100%;
  }
}

.energy-safety {
  &::before {
    background-image: url('@/assets/image/safeMap/energyStorageSafety.png');
    background-repeat: no-repeat;
    height: 48px;
    width: 125px;
    background-size: 100% 100%;
  }
}
</style>
