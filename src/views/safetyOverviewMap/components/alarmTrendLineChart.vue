<template>
  <div ref="chartRef" :style="{ height, width }"></div>
  <!-- 暂无数据 -->
</template>
<script lang="ts" setup>
import { ref, Ref, watch, nextTick } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'

const props = defineProps({
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: '100%',
  },
  alarmTrendLineData: {
    type: Object as () => any,
    default: () => {},
  },
})

const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>)
const echarts = useECharts(chartRef as Ref<HTMLDivElement>).echarts

const xAxisData = ref([])

// 图表数据
const seriesData = ref([
  {
    name: '消防火警次数',
    data: [12, 14, 11, 18, 15, 23, 21, 18, 16, 19, 21, 17, 15, 14, 16, 19, 22, 24, 26, 25, 23, 21, 19, 17],
    lineColor: 'rgba(255, 0, 31, 1)',
    key: 'alarmTrend',
    areaColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(255, 0, 31, 0.1)' },
      { offset: 1, color: 'rgba(255, 73, 53, 0.1)' },
    ]),
  },
  {
    name: '消防预警次数',
    key: 'warnTrend',
    data: [8, 10, 12, 15, 13, 18, 20, 17, 15, 14, 16, 18, 16, 14, 12, 14, 16, 19, 21, 20, 18, 17, 15, 13],
    lineColor: 'rgba(132, 0, 255, 1)',
    areaColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(132, 0, 255, 0)' },
      { offset: 1, color: 'rgba(255, 180, 53, 0)' },
    ]),
  },
  {
    name: '消防故障次数',
    key: 'faultTrend',
    data: [5, 7, 6, 9, 8, 12, 15, 13, 11, 10, 12, 14, 13, 11, 10, 9, 11, 13, 15, 14, 13, 12, 10, 9],
    lineColor: 'rgba(245, 155, 34, 1)',
    areaColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(255, 213, 150, 0)' },
      { offset: 1, color: 'rgba(255, 181, 53, 0)' },
    ]),
  },
  {
    name: '消防隐患次数',
    key: 'hazardTrend',
    data: [10, 12, 9, 11, 13, 16, 18, 15, 13, 12, 14, 16, 15, 13, 11, 12, 14, 17, 19, 18, 16, 15, 13, 11],
    lineColor: 'rgba(255, 255, 0, 1)',
    areaColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(255, 255, 0, 0)' },
      { offset: 1, color: 'rgba(255, 255, 0, 0)' },
    ]),
  },
  {
    name: '储能预警次数',
    key: 'storageWarnTrend',
    data: [3, 5, 4, 7, 6, 9, 11, 10, 8, 7, 9, 12, 11, 9, 8, 7, 9, 11, 13, 12, 11, 10, 8, 7],
    lineColor: 'rgba(20, 97, 234, 1)',
    areaColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(53, 255, 244, 0.1)' },
      { offset: 1, color: 'rgba(53, 255, 244, 0.1)' },
    ]),
  },
])

// 初始化图表
const initChart = () => {
  setOptions(
    {
      backgroundColor: 'transparent',
      grid: {
        left: '2%',
        right: '2%',
        top: '35%',
        bottom: '5%',
        containLabel: true,
      },
      toolbox: {
        show: false,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
            width: 1,
            type: 'solid',
          },
        },
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        borderWidth: 0,
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
      },
      legend: {
        data: seriesData.value?.map((item) => item.name),
        textStyle: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 12,
        },
        itemWidth: 12,
        itemHeight: 12,
        top: 0,
        left: '10px',
        itemStyle: {
          borderWidth: 0,
        },
        icon: 'rect',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData.value, // x轴数据
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 14,
          interval: 2, // 间隔显示标签
          margin: 10,
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 14,
        },
      },
      series: seriesData.value?.map((item) => ({
        name: item.name,
        type: 'line',
        smooth: true,
        showSymbol: false,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          width: 1.5,
          color: item.lineColor,
        },
        itemStyle: {
          color: item.lineColor,
          borderColor: '#fff',
          borderWidth: 1,
        },
        areaStyle: {
          color: item.areaColor,
        },
        data: item.data,
      })),
    },
    { isEmpty: true }
  )
}

// 监听数据变化
watch(
  () => props.alarmTrendLineData,
  (newVal) => {
    if (!newVal) return

    // 更新 X 轴数据
    if (newVal.dataX) {
      xAxisData.value = newVal.dataX
    }

    // 更新系列数据
    if (newVal.dataY && typeof newVal.dataY === 'object') {
      seriesData.value = seriesData.value.map((seriesItem) => {
        if (newVal.dataY[seriesItem.key]) {
          return {
            ...seriesItem,
            data: newVal.dataY[seriesItem.key] || [],
          }
        }
        return seriesItem
      })
    }

    // 确保在下一个 tick 更新图表
    nextTick(() => {
      initChart()
    })
  },
  { immediate: true, deep: true }
)
</script>
