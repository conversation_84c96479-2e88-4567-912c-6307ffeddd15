<template>
  <div>
    <div ref="chartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onUnmounted, reactive } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import * as echarts from 'echarts/core'
import { ECharts } from 'echarts/core'

const props = withDefaults(
  defineProps<{
    width?: string
    height?: string
    warningAnalysisData: Record<string, any>
  }>(),
  {
    width: '100%',
    height: '100%',
    warningAnalysisData: () => ({}),
  }
)

// 图表引用
const chartRef = ref<HTMLElement | null>(null)
const { setOptions } = useECharts(chartRef as any)

const xAxisData = ref([])

// 告警趋势数据
const trendData = reactive({
  // 柱状图数据（左侧Y轴）
  barData: [],
  // 折线图数据（右侧Y轴）
  lineData: [],
})

// 初始化图表
const initChart = () => {
  setOptions({
    backgroundColor: 'transparent',
    toolbox: {
      show: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      padding: [8, 12],
    },
    legend: {
      data: ['日总SOC（%）', '日总有功功率（kw）'],
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
      },
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 10,
      top: 0,
      left: 'center',
      icon: 'rect',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData.value,
        axisPointer: {
          type: 'shadow',
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        min: 0,
        axisLabel: {
          formatter: '{value}',
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
          },
        },
      },
      {
        type: 'value',
        name: '百分比',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}',
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '日总SOC（%）',
        type: 'bar',
        barWidth: 10,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(20, 97, 234, 1)' },
            { offset: 1, color: 'rgba(20, 97, 234, 0)' },
          ]),
        },
        data: trendData.barData,
      },
      {
        name: '日总有功功率（kw）',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: '#FFC233',
        },
        itemStyle: {
          color: '#FFC233',
          borderColor: '#fff',
          borderWidth: 1,
        },
        data: trendData.lineData,
      },
    ],
  })
}

// 监听窗口大小变化
const handleResize = () => {
  if (chartRef.value) {
    const instance = (echarts as any).getInstanceByDom(chartRef.value as HTMLElement) as ECharts | undefined
    if (instance) {
      instance.resize()
    }
  }
}

watch(
  () => props.warningAnalysisData,
  (newVal) => {
    xAxisData.value = newVal.dataX
    trendData.barData = newVal.dataY.socTrend || []
    trendData.lineData = newVal.dataY.powerTrend || []
    initChart()
    window.addEventListener('resize', handleResize)
  }
)

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  initChart,
})
</script>
