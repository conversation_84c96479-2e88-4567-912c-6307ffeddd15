<template>
  <div class="w-full h-full">
    <div class="top">
      <cardTtile title="报警总体情况">
        <div class="text-[#6CC4EB] cursor-pointer" @click="handleMoreInfo">更多信息 ></div>
      </cardTtile>
      <div class="h-140px">
        <!-- 报警总体情况 Alarm overall situation-->
        <AlarmOverallSituation></AlarmOverallSituation>
      </div>
    </div>
    <div class="flex">
      <el-select class="!w-240px mr-20px big-screen-select" v-model="energyValue" placeholder="请选择电站">
        <el-option v-for="item in energyOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker class="!w-240px big-screen-select" v-model="dateValue" type="daterange" placeholder="请选择时间">
      </el-date-picker>
      <el-button type="primary" class="big-screen-button !w-50px ml-20px" @click="handleSearch">查询</el-button>
    </div>
    <div class="alarm-trend-analysis">
      <cardTtile title="报警趋势分析"> </cardTtile>
      <div class="h-150px">
        <alarmTrendLineChart :alarmTrendLineData="alarmTrendLineData" />
      </div>
    </div>
    <div class="alarm-type-analysis">
      <cardTtile title="报警类型分析"> </cardTtile>
      <div class="h-160px relative">
        <alarmTypePieChart :alarmTypePieData="alarmTypePieData" />
      </div>
    </div>
    <div class="alarm-time-analysis">
      <cardTtile title="报警时间段分析"> </cardTtile>
      <div class="h-150px">
        <alarmTimeBarChart :alarmTimeBarData="alarmTimeBarData" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import cardTtile from '@/components/public/cardTtile.vue'
import AlarmOverallSituation from './components/alarmOverallSituation.vue'
import alarmTrendLineChart from './components/alarmTrendLineChart.vue'
import alarmTypePieChart from './components/alarmTypePieChart.vue'
import alarmTimeBarChart from './components/alarmTimeBarChart.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const userInfo = useUserInfo().value

// 电站选项
const energyOptions = ref([
  {
    value: '1',
    label: '唐能光伏运维中心光储电站',
  },
])

const energyValue = ref('')
const dateValue = ref([])
// 跳转“安全监测一张图”
const handleMoreInfo = () => {
  router.push('/overview/safetyMonitoringMap')
}

const handleSearch = () => {
  console.log('查询')
}
const loading = ref(false)

// 报警趋势分析
const alarmTrendLineData = ref({})
// 报警类型分析
const alarmTypePieData = ref<Record<string, any>>({})
// 报警时间段分析
const alarmTimeBarData = ref({})

// 报警趋势分析
const getAlarmTrend = async () => {
  try {
    const { data } = await $API.post({
      url: '/safetyOverview/getAlarmTrendAnalysis',
      data: {
        superviseId: userInfo.orgCode,
        unitId: energyValue.value || userInfo.unitId,
        startTime: dateValue.value[0] || '',
        endTime: dateValue.value[1] || '',
      },
    })
    alarmTrendLineData.value = data || {}
  } catch (e) {
    console.error('请求报警趋势数据异常:', e)
  }
}

// 报警类型分析
const getAlarmType = async () => {
  loading.value = true
  try {
    const { data } = await $API.post({
      url: '/safetyOverview/getAlarmDeviceTypeAnalysis',
      data: {
        superviseId: userInfo.orgCode,
        unitId: energyValue.value || userInfo.unitId,
        startTime: dateValue.value[0] || '',
        endTime: dateValue.value[1] || '',
      },
    })
    alarmTypePieData.value = data || {}
  } catch (e) {
    console.error('请求报警类型数据异常:', e)
  } finally {
    loading.value = false
  }
}

// 报警时间段分析
const getAlarmTime = async () => {
  try {
    const { data } = await $API.post({
      url: '/safetyOverview/getAlarmTimeSlotAnalysis',
      data: {
        superviseId: userInfo.orgCode,
        unitId: energyValue.value || userInfo.unitId,
        startTime: dateValue.value[0] || '',
        endTime: dateValue.value[1] || '',
      },
    })
    alarmTimeBarData.value = data || {}
  } catch (e) {
    console.error('请求报警时间段数据异常:', e)
  }
}

const init = async () => {
  try {
    loading.value = true
    await Promise.all([getAlarmTrend(), getAlarmType(), getAlarmTime()])
  } catch (e) {
    console.error('初始化数据异常:', e)
  } finally {
    loading.value = false
  }

  // 设置定时刷新，1小时刷新一次
  const refreshInterval = 1000 * 60 * 60 // 1小时
  setInterval(() => {
    getAlarmTrend()
    getAlarmType()
    getAlarmTime()
  }, refreshInterval)
}

onMounted(() => {
  init()
})

// 导出组件
defineOptions({
  name: 'LeftPanel',
})
</script>
<style lang="scss" scoped>
.top {
  height: 190px;
  padding: 0px 16px 16px 16px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}

.alarm-trend-analysis {
  margin-top: 9px;
  height: 200px;
  padding: 0px 16px 16px 16px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
.alarm-type-analysis {
  margin-top: 9px;
  height: 200px;
  padding: 0px 16px 16px 16px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
.alarm-time-analysis {
  margin-top: 9px;
  height: 200px;
  padding: 0px 16px 16px 16px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
</style>
