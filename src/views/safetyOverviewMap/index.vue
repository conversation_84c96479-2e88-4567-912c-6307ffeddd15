<template>
  <div class="w-full h-full relative">
    <div class="w-full h-full relative">
      <div class="top">
        <div class="flex items-center justify-center">
          <div v-for="(item, index) in warningCards" :key="index" class="warning-card">
            <img
              :src="getImageUrl(`header${index + 1}.png`)"
              class="w-full h-full absolute top-0 left-0 -z-10"
              alt="card background"
            />
            <div class="warning-content">
              <div class="waring-title">{{ item.title }}</div>
              <div class="flex items-center">
                <div class="waring-value mr-6px">{{ item.value }}</div>
                <div class="waring-unit">{{ item.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="center">
        <!-- <lms></lms> -->
        <nkt></nkt>
      </div>
      <!-- 左侧抽屉区域 -->
      <div class="left">
        <!-- 左侧抽屉按钮，符号“><”，关闭时贴页面左侧，展开时贴抽屉右侧 -->
        <button
          class="drawer-toggle"
          @click="leftDrawerOpen = !leftDrawerOpen"
          :style="leftDrawerOpen ? 'right:-35px;' : 'left:0; right:auto;'"
          aria-label="展开/收起左侧抽屉"
        >
          <span class="drawer-arrow"> <img src="@/assets/image/safeMap/leftPopup.png" class="w-50px h-50px" /></span>
        </button>
        <transition name="drawer-fade">
          <left v-show="leftDrawerOpen" />
        </transition>
      </div>
      <!-- 右侧抽屉区域 -->
      <div class="right">
        <button
          class="drawer-toggle"
          @click="rightDrawerOpen = !rightDrawerOpen"
          :style="rightDrawerOpen ? 'left:-35px;' : 'right:0; left:auto;'"
          aria-label="展开/收起右侧抽屉"
        >
          <span class="drawer-arrow"> <img src="@/assets/image/safeMap/rightPopup.png" class="w-50px h-50px" /></span>
        </button>
        <transition name="drawer-fade">
          <right v-show="rightDrawerOpen" />
        </transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import left from './left.vue'
import right from './right.vue'
import { useUserInfo } from '~/store'
import lms from '@/components/lms/index.vue'
import nkt from '@/components/Nkt/index.vue'

// 左右抽屉展开/收起状态，默认展开
const leftDrawerOpen = ref(true)
const rightDrawerOpen = ref(true)
const userInfo = useUserInfo().value
// 顶部卡片数据
const warningCards = ref([
  {
    title: '储能电站',
    value: '0',
    key: 'storageNum',
    unit: '个',
  },
  {
    title: '电池舱',
    value: '0',
    key: 'cabinNum',
    unit: '个',
  },
  // {
  //   title: '电池仓',
  //   value: '120',
  //   unit: '个',
  // },
  {
    title: '监测设备',
    value: '0',
    key: 'deviceNum',
    unit: '台',
  },
])

// 获取图片路径
const getImageUrl = (name: string) => {
  return new URL(`/src/assets/image/safeMap/${name}`, import.meta.url).href
}
// 顶部卡片数据
const getTopCards = async () => {
  try {
    const { data } = await $API.post({
      url: '/safetyOverview/getTopDataOverview',
      data: {
        superviseId: userInfo.orgCode,
        unitId: userInfo.unitId,
      },
    })
    // 需要根据key查找value
    warningCards.value = warningCards.value.map((item: any) => {
      return {
        ...item,
        value: data[item.key] || 0,
      }
    })
  } catch (e) {
    console.error('请求报警类型数据异常:', e)
  }
}

onMounted(() => {
  getTopCards()
})
</script>

<style lang="scss" scoped>
.footer-menu {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
}
.left {
  position: absolute;
  left: 0;
  width: 450px;
  height: 100%;
  background: linear-gradient(-90deg, rgba(6, 17, 23, 0) 0%, #061117 50%);
  border-radius: 0px 0px 0px 0px;
}
.top {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  // z-index: 10;
  width: 100%;

  .warning-card {
    position: relative;
    width: 168px;
    height: 98px;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-right: 26px;

    .warning-content {
      position: relative;
      z-index: 1;
      color: white;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .waring-title,
      .waring-unit {
        height: 22px;
        font-size: 16px;
        color: #ffffff;
        line-height: 22px;
      }

      .waring-value {
        height: 39px;
        font-weight: 600;
        font-size: 28px;
        line-height: 39px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    &:nth-child(1) .waring-value {
      background-image: linear-gradient(90deg, #ffffff 0%, #7ec2ff 100%);
    }

    &:nth-child(2) .waring-value {
      background-image: linear-gradient(90deg, #ffffff 0%, #7ec2ff 100%);
    }

    &:nth-child(3) .waring-value {
      background-image: linear-gradient(90deg, #ffffff 0%, #b27eff 100%);
    }

    &:nth-child(4) .waring-value {
      background-image: linear-gradient(90deg, #ffffff 0%, #ff937e 100%);
    }
  }
}
.center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
}
.right {
  position: absolute;
  right: 0;
  width: 450px;
  height: 100%;
  background: linear-gradient(90deg, rgba(6, 17, 23, 0) 0%, #061117 50%);
  border-radius: 0px 0px 0px 0px;
}
.drawer-toggle {
  position: absolute;
  top: 50%;
  z-index: 2;
  transform: translateY(-50%);
  cursor: pointer;
}
</style>
