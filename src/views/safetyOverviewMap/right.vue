<template>
  <div class="w-full h-full flex flex-col mr-10px">
    <div class="health-analysis">
      <cardTtile title="储能电站综合安全指数"> </cardTtile>
      <healthAnalysis />
    </div>
    <div class="flex-1 overflow-hidden">
      <div class="w-full h-full overflow-auto"></div>
    </div>
    <div class="waring-analysis">
      <cardTtile title="关键数据趋势分析"> </cardTtile>
      <warningAnalysis class="h-450px" :warningAnalysisData="warningAnalysisData" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import cardTtile from '@/components/public/cardTtile.vue'
import warningAnalysis from './components/warningAnalysis.vue'
import healthAnalysis from './components/healthAnalysis.vue'

const userInfo = useUserInfo().value
const loading = ref(false)
// 储能电站综合安全指数
const healthAnalysisData = ref({})
// 关键数据趋势分析
const warningAnalysisData = ref({})

// 储能电站综合安全指数
const getHealthAnalysisData = async () => {
  loading.value = true
  try {
    const { data } = await $API.post({
      url: '/safetyOverview/getStorageSafetyScore',
      data: {
        superviseId: userInfo.orgCode,
        unitId: userInfo.unitId,
      },
    })
    healthAnalysisData.value = data || {}
  } catch (e) {
    console.error('请求储能电站综合安全指数数据异常:', e)
  } finally {
    loading.value = false
  }
}

// 关键数据趋势分析
const getWarningAnalysisData = async () => {
  loading.value = true
  try {
    const { data } = await $API.post({
      url: '/safetyOverview/getCriticalDataTrend',
      data: {
        superviseId: userInfo.orgCode,
        unitId: userInfo.unitId,
      },
    })
    warningAnalysisData.value = data || {}
  } catch (e) {
    console.error('请求储能电站综合安全指数数据异常:', e)
  } finally {
    loading.value = false
  }
}

const init = async () => {
  try {
    loading.value = true
    await Promise.all([getHealthAnalysisData(), getWarningAnalysisData()])
  } catch (e) {
    console.error('初始化数据异常:', e)
  } finally {
    loading.value = false
  }

  // 设置定时刷新，1小时刷新一次
  const refreshInterval = 1000 * 60 * 60 // 1小时
  setInterval(() => {
    getHealthAnalysisData()
    getWarningAnalysisData()
  }, refreshInterval)
}

onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.health-analysis {
  padding: 0px 16px 16px 16px;
  width: 450px;
  height: 385px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
.waring-analysis {
  margin-top: 9px;
  padding: 5px 16px 16px 16px;
  width: 450px;
  height: 589px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
</style>
