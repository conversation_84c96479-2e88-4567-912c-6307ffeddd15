<template>
  <div class="w-full h-full">
    <div class="h-530px top">
      <cardTtile title="储能预警总体情况"> </cardTtile>
      <warningStats />
    </div>
    <div class="h-[calc(100%-530px)] bottom">
      <cardTtile title="分区域预警分布"> </cardTtile>
      <warningDistribution />
    </div>
  </div>
</template>
<script lang="ts" setup>
import cardTtile from '@/components/public/cardTtile.vue'
import warningStats from './components/warningStats.vue'
import warningDistribution from './components/warningDistribution.vue'
</script>
<style lang="scss" scoped>
.top {
  padding: 5px 16px 16px 16px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
.bottom {
  margin-top: 9px;
  min-height: 260px;
  padding: 5px 16px 100px 16px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
</style>
