<template>
  <div class="w-full h-full">
    <div class="h-390px top">
      <cardTtile title="消防报警总体情况" />
      <alarmOverallSituation />
    </div>
    <div class="w-full h-[calc(100%-400px)] bottom">
      <div class="h-full">
        <cardTtile title="实时设备监测列表">
          <el-select class="!w-140px mb-10px big-screen-select" v-model="energyValue" placeholder="请选择电站">
            <el-option v-for="item in energyOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </cardTtile>
        <!-- 实时设备监测列表 -->
        <realTimeDeviceMonitoringList />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import cardTtile from '@/components/public/cardTtile.vue'
import alarmOverallSituation from './components/alarmOverallSituation.vue'
import realTimeDeviceMonitoringList from './components/realTimeDeviceMonitoringList.vue'
// 电站选项
const energyOptions = ref([
  {
    value: '1',
    label: '唐能光伏运维中心光储电站',
  },
])

const energyValue = ref('')
</script>
<style lang="scss" scoped>
.top {
  padding: 5px 16px 16px 16px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
.bottom {
  margin-top: 9px;
  padding: 5px 16px 100px 16px;
  background: rgba(27, 63, 137, 0.2);
  border-radius: 0px 0px 0px 0px;
}
</style>
