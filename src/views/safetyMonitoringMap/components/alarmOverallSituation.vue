<template>
  <div class="big-screen-analysis h-full">
    <div class="top-line flex justify-end">
      <el-segmented class="small-screen-segmented" v-model="value" :options="timeOptions" @change="handleClick" />
    </div>
    <div class="right-section">
      <div class="alarm-stats">
        <div class="safety-content">
          <div
            v-for="(item, index) in alarmStatsData"
            :key="'stat-' + index"
            :class="[
              'alarm-stat-item',
              item.type,
              {
                'fire-alarm': item.type === 'alarmNum' || item.type === 'serious',
                'normal-alarm': item.type !== 'alarmNum' && item.type !== 'serious',
              },
            ]"
          >
            <div class="stat-icon">
              <img :src="item.icon" :alt="item.name" />
            </div>
            <div class="stat-info">
              <div class="stat-value">
                {{ item.value }}
                <!-- <span class="stat-unit">{{ item.unit }}</span> -->
              </div>
              <div class="stat-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="h-full">
      <alarmPieChart :alarmPieData="alarmPieData" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import alarmPieChart from './alarmPieChart.vue'
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import { timeOptions } from './data'
import { FireAlarmSituation } from './data'
const fireIcon = new URL('@/assets/image/safeMap/fire.png', import.meta.url).href
const warningIcon = new URL('@/assets/image/safeMap/waring.png', import.meta.url).href
const userInfo = useUserInfo().value
const dateValue = ref([$API.getTypeDaysInMonth('0')[0], $API.getTypeDaysInMonth('0')[1]])
const value = ref('0')
const alarmPieData: FireAlarmSituation = ref({})
const alarmStatsData = ref([
  {
    name: '火警',
    value: 0,
    unit: '个',
    icon: fireIcon,
    type: 'alarmNum',
  },
  {
    name: '预警',
    value: 0,
    unit: '个',
    icon: warningIcon,
    type: 'warnNum',
  },
  {
    name: '故障',
    value: 0,
    unit: '个',
    icon: warningIcon,
    type: 'faultNum',
  },
  {
    name: '隐患',
    value: 0,
    unit: '个',
    icon: warningIcon,
    type: 'hazardNum',
  },
])
const handleClick = (tab: string) => {
  if (tab === '1') {
    dateValue.value[0] = $API.getTypeDaysInMonth('1')[0] + ' 00:00:00'
    dateValue.value[1] = $API.getTypeDaysInMonth('1')[1] + ' 23:59:59'
  } else if (tab === '2') {
    dateValue.value[0] = $API.getTypeDaysInMonth('2')[0] + ' 00:00:00'
    dateValue.value[1] = $API.getTypeDaysInMonth('2')[1] + ' 23:59:59'
  } else if (tab === '3') {
    dateValue.value[0] = $API.getTypeDaysInMonth('3')[0] + ' 00:00:00'
    dateValue.value[1] = $API.getTypeDaysInMonth('3')[1] + ' 23:59:59'
  } else {
    dateValue.value[0] = $API.getTypeDaysInMonth('0')[0] + ' 00:00:00'
    dateValue.value[1] = $API.getTypeDaysInMonth('0')[1] + ' 23:59:59'
  }
  getAlarmOverall()
}

// 消防报警总体情况
const getAlarmOverall = async () => {
  try {
    const { data } = await $API.post({
      url: '/safetyMonitor/getFireAlarmSituation',
      data: {
        superviseId: userInfo.orgCode,
        unitId: userInfo.unitId,
        startTime: dateValue.value[0] || '',
        endTime: dateValue.value[1] || '',
      },
    })
    alarmStatsData.value = alarmStatsData.value.map((item) => {
      return {
        ...item,
        value: data[item.type],
      }
    })
    alarmPieData.value = data || {}
  } catch (e) {
    console.error('请求报警趋势数据异常:', e)
  }
}

const init = async () => {
  getAlarmOverall()
  // 设置定时刷新，1小时刷新一次
  const refreshInterval = 1000 * 60 * 60 // 1小时
  setInterval(() => {
    getAlarmOverall()
  }, refreshInterval)
}

onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.right-section {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  .safety-card {
    background: rgba(255, 255, 255, 0.1);
    margin-bottom: 16px;
    .safety-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .safety-title {
        color: #fff;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .alarm-stats {
    .safety-content {
      display: flex;
      justify-content: space-between;
      margin-right: 40px;
      margin-top: 10px;
      flex-wrap: wrap;
    }
  }
  .alarm-stat-item {
    width: 78px;
    height: 62px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 14px;
    box-sizing: border-box;

    .stat-icon {
      display: none;
    }

    .stat-info {
      text-align: center;
      .stat-name {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 4px;
      }

      .stat-value {
        font-size: 20px;
        font-weight: bold;
        color: #fff;

        .stat-unit {
          font-size: 14px;
          margin-left: 2px;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
    .safety-content {
      flex: 1;
    }
    &.alarmNum,
    &.serious {
      background-image: url('@/assets/image/safeMap/fire.png');
    }

    &.warnNum,
    &.faultNum,
    &.hazardNum,
    &.offline,
    &.normal-alarm {
      background-image: url('@/assets/image/safeMap/waring.png');
    }
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    padding: 10px;
    box-sizing: border-box;
    transition: all 0.3s;
  }
}
</style>
