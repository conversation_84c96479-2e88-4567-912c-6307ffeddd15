export const timeOptions = [
  { label: '当前', value: '0' },
  { label: '今日', value: '1' },
  { label: '本月', value: '2' },
  { label: '本年', value: '3' },
]

export interface DeviceMonitorInfo {
  /**
   * 楼栋名称
   */
  buildingName?: string
  /**
   * 设备位置
   */
  deviceAddress: string
  /**
   * 设备id
   */
  deviceId?: string
  /**
   * 设备名称
   */
  deviceName: string
  /**
   * 事件时间
   */
  eventTime: string
  /**
   * 楼层名称
   */
  floorName?: string
  /**
   * 电站id
   */
  unitId?: string
  /**
   * 电站名称
   */
  unitName: string
  [property: string]: any
}

/**
 * ResponseModelFireAlarmSituation
 */
export interface Response {
  code?: string
  data?: FireAlarmSituation
  dataType?: string
  message?: { [key: string]: any }
  status?: string
  token?: string
  [property: string]: any
}

/**
 * FireAlarmSituation
 */
export interface FireAlarmSituation {
  /**
   * 火警数
   */
  alarmNum?: number
  /**
   * 故障数
   */
  faultNum?: number
  /**
   * 环型图统计
   */
  fireAlarmList?: StorageFireAlarm[]
  /**
   * 总数
   */
  fireTotal?: number
  /**
   * 隐患数
   */
  hazardNum?: number
  /**
   * 预警数
   */
  warnNum?: number
  [property: string]: any
}

/**
 * 电站报警统计
 *
 * StorageFireAlarm
 */
export interface StorageFireAlarm {
  /**
   * 报警数
   */
  count?: number
  /**
   * id
   */
  id?: string
  /**
   * 名称
   */
  name?: string
  [property: string]: any
}
