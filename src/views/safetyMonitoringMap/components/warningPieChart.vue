<template>
  <div ref="chartRef" :style="{ width, height }"></div>
</template>
<script lang="ts" setup>
import { Ref, ref, watch } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import { FireAlarmSituation } from './data'

const props = defineProps({
  width: {
    type: String,
  },
  height: {
    type: String,
    default: '100%',
  },
  warningPieData: {
    type: Object as () => FireAlarmSituation,
    default: () => {},
  },
})

const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>)

watch(
  () => props.warningPieData,
  () => {
    const chartData = props.warningPieData.fireAlarmList?.map((item) => ({
      name: item.name,
      value: item.count,
    }))
    setOptions(
      {
        title: {
          text: String(props.warningPieData.total),
          left: '24%',
          top: '40%',
          textAlign: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 24,
            fontWeight: 'bold',
          },
          subtext: '预警总数',
          subtextStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            fontSize: 16,
            align: 'center',
            verticalAlign: 'bottom',
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          borderWidth: 0,
          textStyle: {
            color: '#fff',
            fontSize: 12,
          },
        },
        toolbox: {
          show: false,
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: '5%',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 16,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            fontSize: 12,
            rich: {
              name: {
                width: 60,
                color: 'rgba(255, 255, 255, 0.8)',
                padding: [0, 10],
              },
              value: {
                color: '#35FFF4',
              },
              percent: {
                color: 'rgba(255, 255, 255, 1)',
                padding: [0, 10],
              },
            },
          },
          formatter: (name: string) => {
            return `{name|${name.replace('', '')}}`
          },
        },
        backgroundColor: 'transparent',
        series: [
          {
            name: '电站名称',
            type: 'pie',
            center: ['25%', '50%'],
            radius: ['50%', '80%'],
            left: 0,
            color: ['#3AA0FF', '#36CBCB', '#4DCB73', '#FAD337', '#F2637B', '#975FE4', '#CA8622', '#BDA29A'],
            data: chartData,
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            emphasis: {
              scale: false,
              itemStyle: {
                shadowBlur: 0,
                shadowOffsetX: 0,
                shadowColor: 'transparent',
              },
            },
            itemStyle: {
              borderRadius: 1,
              borderColor: 'transparent',
              borderWidth: 0,
            },
          },
        ],
      },
      { isEmpty: chartData?.length === 0 }
    )
  },
  { immediate: true }
)
</script>
