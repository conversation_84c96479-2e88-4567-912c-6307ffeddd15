<template>
  <div class="h-full">
    <div class="flex justify-end items-center mt-15px">
      <el-segmented
        class="small-screen-segmented"
        size="small"
        v-model="value"
        :options="timeOptions"
        @change="handleClick"
      />
    </div>
    <!-- 分区域预警分布饼图 -->
    <div class="h-full">
      <warningPieChart :warningPieData="warningPieData" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import warningPieChart from './warningPieChart.vue'
import { timeOptions } from './data'

const value = ref('0')
const userInfo = useUserInfo().value
const dateValue = ref([$API.getTypeDaysInMonth('0')[0], $API.getTypeDaysInMonth('0')[1]])
const warningPieData = ref({})
const handleClick = (tab: string) => {
  if (tab === '1') {
    dateValue.value[0] = $API.getTypeDaysInMonth('1')[0] + ' 00:00:00'
    dateValue.value[1] = $API.getTypeDaysInMonth('1')[1] + ' 23:59:59'
  } else if (tab === '2') {
    dateValue.value[0] = $API.getTypeDaysInMonth('2')[0] + ' 00:00:00'
    dateValue.value[1] = $API.getTypeDaysInMonth('2')[1] + ' 23:59:59'
  } else if (tab === '3') {
    dateValue.value[0] = $API.getTypeDaysInMonth('3')[0] + ' 00:00:00'
    dateValue.value[1] = $API.getTypeDaysInMonth('3')[1] + ' 23:59:59'
  } else {
    dateValue.value[0] = $API.getTypeDaysInMonth('0')[0] + ' 00:00:00'
    dateValue.value[1] = $API.getTypeDaysInMonth('0')[1] + ' 23:59:59'
  }
  getWarningDistribution()
}
// 分区域预警分布
const getWarningDistribution = async () => {
  try {
    const { data } = await $API.post({
      url: '/safetyMonitor/getStorageWarnDistribution',
      data: {
        superviseId: userInfo.orgCode,
        unitId: userInfo.unitId,
        startTime: dateValue.value[0] || '',
        endTime: dateValue.value[1] || '',
      },
    })
    warningPieData.value = data || {}
  } catch (error) {
    console.error('获取预警统计数据失败:', error)
  }
}

const init = async () => {
  getWarningDistribution()
  // 设置定时刷新，1小时刷新一次
  const refreshInterval = 1000 * 60 * 60 // 1小时
  setInterval(() => {
    getWarningDistribution()
  }, refreshInterval)
}

onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped></style>
