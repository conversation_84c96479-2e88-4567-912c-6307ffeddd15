<template>
  <div class="warning-stats-container">
    <!-- 时间筛选 -->
    <div class="time-filter">
      <el-segmented
        v-model="value"
        class="small-screen-segmented"
        :options="timeOptions"
        @change="handleTimeChange"
        size="small"
      />
    </div>

    <!-- 预警统计 -->
    <div class="stats-wrapper">
      <!-- 预警统计卡片 -->
      <div class="warning-cards">
        <div class="warning-card total-warning">
          <div class="warning-bg" :style="{ backgroundImage: 'url(' + warningTotalBg + ')' }">
            <div class="flex">
              <div class="warning-label">预警总数</div>
              <div class="warning-value">{{ statsData.total }}</div>
            </div>
          </div>
        </div>
        <!-- 严重预警 -->
        <div class="warning-card severe-warning">
          <div class="warning-bg" :style="{ backgroundImage: 'url(' + warningImpBg + ')' }">
            <div class="flex">
              <div class="warning-label">严重预警</div>
              <div class="warning-value2">{{ statsData.severe }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警类型列表 -->
      <div class="warning-list">
        <div
          v-for="(item, index) in warningTypes"
          :key="index"
          class="warning-item"
          :style="{ backgroundImage: 'url(' + warningNumBg + ')' }"
        >
          <div class="flex justify-between">
            <div class="warning-type">{{ item.warnName }}</div>
            <div class="warning-count">{{ item.count }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import { timeOptions } from './data'

// 统计数据类型
interface WarningType {
  warnName: string
  count: number
}

interface WarningStats {
  total: number
  severe: number
  types: WarningType[]
}
const userInfo = useUserInfo().value
// 背景图
const warningTotalBg = new URL('@/assets/image/safeMap/waringTotal.png', import.meta.url).href
const warningImpBg = new URL('@/assets/image/safeMap/waringImp.png', import.meta.url).href
const warningNumBg = new URL('@/assets/image/safeMap/waringNum.png', import.meta.url).href

const value = ref('0')
const timeRange = ref([$API.getTypeDaysInMonth('0')[0], $API.getTypeDaysInMonth('0')[1]])

// 统计数据
const statsData = reactive<WarningStats>({
  total: 0,
  severe: 0,
  types: [],
})

// 从统计数据生成预警类型列表
const warningTypes = computed(() => {
  return statsData.types.map((type: WarningType) => ({
    warnName: type.warnName,
    count: type.count,
  }))
})

// 储能预警总体情况
const fetchStatsData = async () => {
  try {
    const { data } = await $API.post({
      url: '/safetyMonitor/getStorageWarnSituation',
      data: {
        superviseId: userInfo.orgCode,
        unitId: userInfo.unitId,
        startTime: timeRange.value[0] || '',
        endTime: timeRange.value[1] || '',
      },
    })
    Object.assign(statsData, {
      total: data.warnAllNum || 0,
      severe: data.warnSeriousNum || 0,
      types: data.warnTypeList || [],
    })
  } catch (error) {
    console.error('获取预警统计数据失败:', error)
  }
}

const handleTimeChange = (tab: string) => {
  if (tab === '1') {
    timeRange.value[0] = $API.getTypeDaysInMonth('1')[0] + ' 00:00:00'
    timeRange.value[1] = $API.getTypeDaysInMonth('1')[1] + ' 23:59:59'
    console.log($API.getTypeDaysInMonth('1'), 222)
  } else if (tab === '2') {
    timeRange.value[0] = $API.getTypeDaysInMonth('2')[0] + ' 00:00:00'
    timeRange.value[1] = $API.getTypeDaysInMonth('2')[1] + ' 23:59:59'
  } else if (tab === '3') {
    timeRange.value[0] = $API.getTypeDaysInMonth('3')[0] + ' 00:00:00'
    timeRange.value[1] = $API.getTypeDaysInMonth('3')[1] + ' 23:59:59'
  } else {
    timeRange.value[0] = $API.getTypeDaysInMonth('0')[0] + ' 00:00:00'
    timeRange.value[1] = $API.getTypeDaysInMonth('0')[1] + ' 23:59:59'
  }
  fetchStatsData()
}

onMounted(() => {
  fetchStatsData()
})
</script>

<style lang="scss" scoped>
.warning-stats-container {
  padding: 12px;
  color: #fff;

  .time-filter {
    margin-bottom: 5px;
    display: flex;
    justify-content: flex-end;

    :deep(.el-segmented) {
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      padding: 2px;

      .el-segmented-item {
        color: rgba(255, 255, 255, 0.6);
        padding: 0 12px;

        &.is-active {
          color: #fff;
          background: linear-gradient(90deg, #1b8dff 0%, #0066ff 100%);
          border-radius: 2px;
        }
      }
    }
  }

  .stats-wrapper {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .warning-bg {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .warning-value {
      font-size: 26px;
      line-height: 36px;
      text-transform: none;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-image: linear-gradient(180deg, #ffffff 0%, #ffaf7e 100%);
    }

    .warning-value2 {
      font-size: 26px;
      line-height: 36px;
      text-transform: none;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-image: linear-gradient(180deg, #ffffff 0%, #ff7e7e 100%);
    }
    .warning-label {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      color: #ffffff;
    }
  }

  .warning-cards {
    display: flex;
    gap: 10px;

    .warning-card {
      flex: 1;
      height: 48px;

      .warning-bg {
        width: 100%;
        height: 100%;
      }
    }

    .total-warning {
      margin-right: 6px;
    }

    .severe-warning {
      margin-left: 6px;
    }
  }

  .warning-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;

    .warning-item {
      padding: 0px 10px;
      height: 38px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      background-size: 100% 100%;
      background-repeat: no-repeat;

      .warning-type {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
      }

      .warning-count {
        font-weight: 600;
        font-size: 26px;
        line-height: 36px;
        text-transform: none;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-image: linear-gradient(180deg, #ffffff 0%, #7ed4ff 100%);
      }
    }
  }
}
</style>
