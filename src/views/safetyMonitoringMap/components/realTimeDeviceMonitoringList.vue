<template>
  <div class="h-full">
    <div class="top-line flex justify-center items-center mt-15px">
      <el-segmented
        class="small-screen-segmented"
        size="small"
        v-model="value"
        :options="segmentedOptions"
        @change="handleClick"
      />
    </div>
    <scrollbarList>
      <template v-if="realTimeDeviceMonitoringList.length > 0">
        <div v-for="(item, index) in realTimeDeviceMonitoringList" :key="index">
          <monitoringList :list="item" />
        </div>
      </template>
      <template v-else>
        <div class="flex justify-center items-center h-full">
          <el-empty description="暂无数据" :size="100" />
        </div>
      </template>
    </scrollbarList>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import monitoringList from './monitoringList.vue'
import scrollbarList from '@/components/public/scrollbarList.vue'
import { DeviceMonitorInfo } from './data'

const props = defineProps({
  unitIdProps: {
    type: String,
    default: '',
  },
})
const segmentedOptions = [
  {
    value: '1',
    label: '火警',
  },
  {
    value: '2',
    label: '预警',
  },
  {
    value: '3',
    label: '故障',
  },
  {
    value: '4',
    label: '隐患',
  },
  {
    value: '5',
    label: '动作',
  },
  {
    value: '6',
    label: '离线',
  },
]
const userInfo = useUserInfo().value
const value = ref('1')
const realTimeDeviceMonitoringList = ref([] as DeviceMonitorInfo[])
const handleClick = (tab: string) => {
  value.value = tab
  getRealTimeDeviceMonitoringList()
}

// 实时设备监测列表
const getRealTimeDeviceMonitoringList = async () => {
  try {
    const { data } = await $API.post({
      url: '/safetyMonitor/getDeviceMonitorList',
      data: {
        superviseId: userInfo.orgCode,
        unitId: props.unitIdProps || userInfo.unitId,
        enevtType: value.value,
      },
    })
    realTimeDeviceMonitoringList.value = data || []
  } catch (e) {
    console.error('请求报警趋势数据异常:', e)
  }
}

const init = async () => {
  getRealTimeDeviceMonitoringList()
  // 设置定时刷新，1小时刷新一次
  const refreshInterval = 1000 * 60 * 60 // 1小时
  setInterval(() => {
    getRealTimeDeviceMonitoringList()
  }, refreshInterval)
}

onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped></style>
