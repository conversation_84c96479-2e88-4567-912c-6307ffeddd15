<template>
  <div class="warning-item">
    <div class="flex justify-between mb-[10px]">
      <div class="flex items-center text-16px">{{ props.list.unitName || '' }}</div>
      <div class="flex text-[14px] items-center">
        <div class="flex items-center text-[#9FBCD0] mr-[10px]">{{ props.list.eventTime || '' }}</div>
      </div>
    </div>
    <div class="text-[14px] text-[#9FBCD0]">
      <div>
        {{ props.list.deviceName || '未知设备' }} {{ props.list.buildingName || '' }}{{ props.list.floorName || ''
        }}{{ props.list.deviceAddress || '' }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { DeviceMonitorInfo } from './data'

const props = defineProps({
  list: {
    type: Object as () => DeviceMonitorInfo,
    default: () => {},
  },
})

defineOptions({
  name: 'earlyWarningItem',
})
</script>

<style lang="scss" scoped>
.warning-item {
  background: linear-gradient(180deg, rgba(21, 133, 181, 0.2) 0%, rgba(4, 61, 94, 0.2) 100%);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #528cff;
  padding: 12px;
  margin-top: 20px;
  margin-bottom: 10px;
}
</style>
