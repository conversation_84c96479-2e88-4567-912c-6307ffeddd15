<template>
  <div class="real-time-monitoring">
    <!-- 顶部状态 Tab -->
    <div class="status-tabs">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-card', tab.key === activeTab ? 'active' : '', tab.color]"
        @click="onTabChange(tab.key)"
      >
        <div class="title">{{ tab.name }}</div>
        <div class="count">{{ counts[tab.key] ?? 0 }}</div>
      </div>
    </div>

    <!-- 条件筛选区 -->
    <div class="filters">
      <el-form :inline="true" label-width="auto">
        <el-form-item label="设备类型">
          <el-select v-model="filters.systemType" placeholder="请选择" clearable @change="handleSearch">
            <el-option v-for="opt in systemTypeOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="楼栋楼层">
          <el-select v-model="filters.building" placeholder="请选择" clearable @change="handleSearch">
            <el-option v-for="opt in buildingOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="安装位置">
          <el-select v-model="filters.location" placeholder="请选择" clearable @change="handleSearch">
            <el-option v-for="opt in locationOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格（公共组件） -->
    <table-list
      ref="tableRef"
      :columns="columnsWithOp"
      :api="fetchTable"
      :noPage="false"
      :pagination="paginationProps"
      border
      stripe
      class="rt-table"
    >
      <template #op="{ row }">
        <el-button link type="primary" @click="viewDetail(row)">详情</el-button>
        <el-button link type="primary" @click="viewLocation(row)">位置</el-button>
      </template>
    </table-list>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import tableList, { IColumn, IPaginationProps } from '@/components/public/tableList/index.vue'

// Tab Key 类型
type TabKey = 'fire' | 'fault' | 'hazard' | 'action'

// 表格列配置（复用公共表格 IColumn）
type ColumnDef = IColumn

// 顶部 Tab 配置
const tabs = ref<Array<{ key: TabKey; name: string; color: string }>>([
  { key: 'fire', name: '火警', color: 'c-red' },
  { key: 'fault', name: '故障', color: 'c-orange' },
  { key: 'hazard', name: '隐患', color: 'c-magenta' },
  { key: 'action', name: '动作', color: 'c-blue' },
])

// 当前激活 Tab
const activeTab = ref<TabKey>('fire')

const counts = reactive<Record<TabKey, number>>({
  fire: 14,
  fault: 14,
  hazard: 14,
  action: 14,
})

// 筛选项
const filters = reactive({
  systemType: '' as string | number,
  building: '' as string | number,
  location: '' as string | number,
})

// 下拉选项（后续可由接口替换）
const systemTypeOptions = ref([
  { label: '气体探测器', value: 'gas' },
  { label: '烟感探测器', value: 'smoke' },
])
const buildingOptions = ref([
  { label: '1栋1层', value: 'b1f1' },
  { label: '1栋2层', value: 'b1f2' },
])
const locationOptions = ref([
  { label: '配电室', value: 'room_power' },
  { label: '消防监控室', value: 'room_fire' },
])

// 各 Tab 对应的列配置
const columnMap: Record<TabKey, ColumnDef[]> = {
  fire: [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'system', label: '系统类型', width: 120 },
    { prop: 'deviceType', label: '设备类型', width: 120 },
    { prop: 'deviceNum', label: '设备位置' },
    { prop: 'imei', label: 'IMEI', width: 160 },
    { prop: 'alarmValue', label: '报警值' },
    { prop: 'alarmTime', label: '报警时间', width: 160 },
    { prop: 'onlineTime', label: '设备上线时间', width: 160 },
  ],
  fault: [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'system', label: '系统类型', width: 120 },
    { prop: 'deviceType', label: '设备类型', width: 120 },
    { prop: 'deviceNum', label: '设备位置' },
    { prop: 'imei', label: 'IMEI', width: 160 },
    { prop: 'faultType', label: '故障类型' },
    { prop: 'faultTime', label: '故障时间', width: 160 },
    { prop: 'onlineTime', label: '设备上线时间', width: 160 },
  ],
  hazard: [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'system', label: '系统类型', width: 120 },
    { prop: 'hazardLevel', label: '隐患等级' },
    { prop: 'hazardDesc', label: '隐患描述' },
    { prop: 'foundTime', label: '发现时间', width: 160 },
    { prop: 'deadline', label: '整改期限', width: 160 },
  ],
  action: [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'system', label: '系统类型', width: 120 },
    { prop: 'deviceType', label: '设备类型', width: 120 },
    { prop: 'deviceNum', label: '设备位置' },
    { prop: 'actionType', label: '动作类型' },
    { prop: 'actionTime', label: '动作时间', width: 160 },
  ],
}

const currentColumns = computed<ColumnDef[]>(() => columnMap[activeTab.value])

// 追加操作列（固定右侧）
const columnsWithOp = computed<IColumn[]>(() => [
  ...currentColumns.value,
  { label: '操作', width: 120, fixed: 'right', align: 'center', slot: 'op' },
])

// 表格实例与分页配置
const tableRef = ref<InstanceType<typeof tableList> | null>(null)
const paginationProps = reactive<IPaginationProps>({
  pageSizes: [10, 20, 50, 100],
})

// Tab 切换
const onTabChange = (key: TabKey) => {
  if (activeTab.value === key) return
  activeTab.value = key
  tableRef.value?.getTableData(1)
}

// 查询
const handleSearch = () => {
  tableRef.value?.getTableData(1)
}

// 详情
const viewDetail = (row: Record<string, any>) => {
  console.log('查看详情', activeTab.value, row)
}

// 位置
const viewLocation = (row: Record<string, any>) => {
  console.log('查看详情', activeTab.value, row)
}

const fetchTable = async (pageModel: { pageNo: number; pageSize: number }) => {
  const { pageNo, pageSize } = pageModel
  const rows = Array.from({ length: pageSize }, (_, i) => ({
    index: (pageNo - 1) * pageSize + i + 1,
    system: filters.systemType || '1329.8',
    deviceType: '3201',
    deviceNum: '1#电表1',
    imei: 862345676543210 + i,
    alarmValue: 3201,
    alarmTime: '2025-08-18 12:00:00',
    onlineTime: '2025-08-01 09:00:00',
    warnValue: 3201,
    warnTime: '2025-08-18 12:00:00',
    faultType: '通讯异常',
    faultTime: '2025-08-18 12:00:00',
    hazardLevel: '较高',
    hazardDesc: '线路老化需更换',
    foundTime: '2025-08-18 10:00:00',
    deadline: '2025-08-25',
    actionType: '复位',
    actionTime: '2025-08-18 12:10:00',
  }))
  return Promise.resolve({
    code: 'success',
    data: { rows, total: 800 },
  })
}

onMounted(() => {})
</script>

<style scoped lang="scss">
.real-time-monitoring {
  height: 100%;
  padding: 0px 16px 0 16px;

  .status-tabs {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    justify-content: center;

    .tab-card {
      cursor: pointer;
      min-width: 160px;
      height: 72px;
      padding: 10px 12px;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      transition: all 0.2s ease;
      align-items: center;
      // opacity: 0.9;

      &.active {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);
        opacity: 1;
      }

      .title {
        font-weight: 600;
        font-size: 16px;
        color: #ffffff;
      }
      .count {
        margin-top: 6px;
        font-weight: 600;
        font-size: 22px;
        color: #ffffff;
      }
    }

    /* 色系（与设计稿相近渐变） */
    .c-red {
      background: linear-gradient(270deg, rgba(7, 141, 236, 1) 0%, rgba(46, 188, 240, 1) 100%);
    }
    .c-purple {
      background: linear-gradient(270deg, rgba(90, 46, 166, 0.7) 0%, rgba(35, 18, 63, 0.7) 100%);
    }
    .c-orange {
      background: linear-gradient(270deg, rgba(123, 87, 233, 1) 0%, rgba(171, 28, 254, 1) 100%);
    }
    .c-magenta {
      background: linear-gradient(270deg, rgba(171, 28, 254, 1) 0%, rgba(123, 87, 233, 1) 100%);
    }
    .c-blue {
      background: linear-gradient(90deg, rgba(7, 141, 236, 1) 0%, rgba(41, 102, 220, 1) 100%);
    }
    .c-cyan {
      background: linear-gradient(270deg, rgba(0, 173, 239, 0.7) 0%, rgba(6, 73, 99, 0.7) 100%);
    }
  }

  .filters {
    display: flex;
    align-items: center;
    margin: 0 16px 0px 20px;

    :deep(.el-select) {
      width: 200px;
    }
  }

  .rt-table {
    width: 100%;
  }

  /* 由公共表格自带分页，这里保留空样式占位可删除 */
}
</style>
