<template>
  <div class="progress-container">
    <svg viewBox="0 0 100 100">
      <!-- 背景圆环 -->
      <circle class="background-circle" cx="50" cy="50" r="45"></circle>

      <!-- 多条数据的圆环 -->
      <template v-if="Array.isArray(props.data)">
        <circle
          v-for="(item, index) in props.data"
          :key="index"
          class="progress-circle"
          :class="`progress-${item.status || index}`"
          :style="{
            stroke: item.color,
            strokeWidth: 8,
            strokeDasharray: circumference,
            strokeDashoffset: circumference,
            transform: `rotate(${index * 90}deg)`,
            transformOrigin: '50% 50%',
          }"
          cx="50"
          cy="50"
          r="45"
        ></circle>
      </template>

      <!-- 单条数据的圆环（保持向后兼容） -->
      <template v-else>
        <circle
          class="progress-circle"
          :class="props.data.className"
          :style="{ stroke: props.data.color }"
          cx="50"
          cy="50"
          r="45"
        ></circle>
      </template>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, watch } from 'vue'

const props = defineProps({
  data: {
    type: [Object, Array],
    default: () => {
      return {
        progress: 10,
        color: '#409EFF',
        className: '',
      }
    },
  },
})

// 圆环周长
const circumference = computed(() => 2 * Math.PI * 45) // 2πr，这里 r=45

watch(
  () => props.data,
  () => {
    if (props.data) {
      setTimeout(() => {
        if (Array.isArray(props.data)) {
          // 处理多条数据
          props.data.forEach((item, index) => {
            setMultiProgress(item.progress || 0, index)
          })
        } else {
          // 处理单条数据（保持向后兼容）
          setProgress(props.data?.progress || 0)
        }
      }, 30)
    }
  },
  { immediate: true, deep: true }
)

// 设置单条数据的进度
const setProgress = (percentage: number) => {
  nextTick(() => {
    const progressCircle = document.querySelector(`.progress-${(props.data as any).status}`) as HTMLElement
    const offset = (circumference.value * (100 - percentage)) / 100
    if (progressCircle?.style) {
      progressCircle.style.strokeDashoffset = `${offset < 0 ? 0 : offset}`
    }
  })
}

// 设置多条数据的进度
const setMultiProgress = (percentage: number, index: number) => {
  nextTick(() => {
    const progressCircle = document.querySelector(`.progress-${index}`) as HTMLElement
    const maxOffset = circumference.value * 0.25 // 每条数据占1/4圆环
    const offset = (maxOffset * (100 - percentage)) / 100
    if (progressCircle?.style) {
      progressCircle.style.strokeDasharray = `${maxOffset} ${circumference.value}`
      progressCircle.style.strokeDashoffset = `${offset < 0 ? 0 : offset}`
    }
  })
}

defineOptions({ name: 'progressCom' })
</script>
<style lang="scss" scoped>
.progress-container {
  width: 130px;
  height: 130px;
  position: absolute;
  transform: rotate(90deg);

  // 为小圆环提供不同的尺寸
  &.mini {
    width: 80px;
    height: 80px;
  }

  .svg {
    width: 100%;
    height: 100%;
  }

  .background-circle {
    fill: none;
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 8;
  }

  .progress-circle {
    fill: none;
    stroke: #4caf50;
    stroke-width: 8;
    stroke-dasharray: 282.7433388230813;
    /* 2πr，这里 r=45 */
    stroke-dashoffset: 282.7433388230813;
    stroke-linecap: round;
    transition: stroke-dashoffset 1s ease-in-out;
  }
}

// 为不同状态的圆环添加动画效果
.progress-1,
.progress-2,
.progress-3,
.progress-4 {
  transition: stroke-dashoffset 1.5s ease-in-out;
}
</style>
