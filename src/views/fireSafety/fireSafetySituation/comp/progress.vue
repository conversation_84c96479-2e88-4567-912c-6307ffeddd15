<template>
  <div class="progress-container">
    <svg viewBox="0 0 100 100">
      <circle class="background-circle" cx="50" cy="50" r="45"></circle>
      <circle
        class="progress-circle"
        :class="props.data.className"
        :style="{ stroke: props.data.color }"
        cx="50"
        cy="50"
        r="45"
      ></circle>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        progress: 10,
        color: '#409EFF',
        className: '',
      }
    },
  },
})
watch(
  () => props.data,
  () => {
    if (props.data) {
      setTimeout(() => {
        // 示例：设置进度为 70%
        setProgress(props.data?.progress)
      }, 30)
    }
  },
  { immediate: true, deep: true }
)
const setProgress = (percentage: number) => {
  nextTick(() => {
    let progressCircle = document.querySelector(`.progress-${props.data.status}`)
    const offset = (282.7433388230813 * (100 - percentage)) / 100
    if (progressCircle?.style) {
      progressCircle.style.strokeDashoffset = offset < 0 ? 0 : offset
    }
  })
}

async function init() {
  setTimeout(() => {
    // 示例：设置进度为 70%
    setProgress(props.data?.progress)
  }, 30)
}
onMounted(async () => {})

defineOptions({ name: 'leftComp' })
</script>
<style lang="scss" scoped>
.progress-container {
  width: 130px;
  height: 130px;
  position: absolute;
  transform: rotate(90deg);
  .svg {
    width: 100%;
    height: 100%;
  }

  .background-circle {
    fill: none;
    stroke: transparent;
    stroke-width: 10;
  }

  .progress-circle {
    fill: none;
    stroke: #4caf50;
    stroke-width: 10;
    stroke-dasharray: 282.7433388230813;
    /* 2πr，这里 r=45 */
    stroke-dashoffset: 282.7433388230813;
  }
}
</style>
