<template>
  <div class="progress-container">
    <svg viewBox="0 0 100 100">
      <circle class="background-circle" cx="50" cy="50" r="45"></circle>
      <circle
        class="progress-circle"
        :class="props.data.className"
        :style="{ stroke: props.data.color }"
        cx="50"
        cy="50"
        r="45"
      ></circle>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        progress: 10,
        color: '#409EFF',
        className: '',
      }
    },
  },
})
watch(
  () => props.data,
  () => {
    if (props.data) {
      setTimeout(() => {
        // 示例：设置进度为 70%
        setProgress(props.data?.progress)
      }, 30)
    }
  },
  { immediate: true, deep: true }
)
const setProgress = (percentage: number) => {
  nextTick(() => {
    let progressCircle = document.querySelector(`.progress-${props.data.status}`)
    const offset = (282.7433388230813 * (100 - percentage)) / 100
    if (progressCircle?.style) {
      progressCircle.style.strokeDashoffset = offset < 0 ? 0 : offset
    }
  })
}

async function init() {
  setTimeout(() => {
    // 示例：设置进度为 70%
    setProgress(props.data?.progress)
  }, 30)
}
onMounted(async () => {})

defineOptions({ name: 'leftComp' })
</script>
<style lang="scss" scoped>
.progress-container {
  width: 130px;
  height: 130px;
  position: absolute;
  transform: rotate(90deg);

  // 为小圆环提供不同的尺寸
  &.mini {
    width: 80px;
    height: 80px;
  }

  .svg {
    width: 100%;
    height: 100%;
  }

  .background-circle {
    fill: none;
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 8;
  }

  .progress-circle {
    fill: none;
    stroke: #4caf50;
    stroke-width: 8;
    stroke-dasharray: 282.7433388230813;
    /* 2πr，这里 r=45 */
    stroke-dashoffset: 282.7433388230813;
    stroke-linecap: round;
    transition: stroke-dashoffset 1s ease-in-out;
  }
}

// 为不同状态的圆环添加动画效果
.progress-1,
.progress-2,
.progress-3,
.progress-4 {
  transition: stroke-dashoffset 1.5s ease-in-out;
}
</style>
