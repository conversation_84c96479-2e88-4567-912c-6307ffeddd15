<template>
  <div class="left-comp h-full w-full flex items-center">
    <div class="bg flex items-center justify-center">
      <div class="values flex flex-col items-center justify-center h-180px w-180px">
        <progressCom
          :data="{
            status: mainIndicator.status,
            progress: mainIndicator.percent,
            color: mainIndicator.color,
            className: `progress-${mainIndicator.status}`,
          }"
        ></progressCom>
        <span class="value">{{ mainIndicator.value }}分</span>
        <div class="btns">{{ mainIndicator.name }}</div>
      </div>
    </div>
    <div class="right flex-1">2</div>
  </div>
</template>

<script setup lang="ts">
import progressCom from './progress.vue'

// 主要安全指数（原有的圆形进度显示）
const mainIndicator = {
  name: '平均分',
  percent: '89%',
  value: 89,
  color: '#4FC7F5',
  status: 2,
}
</script>

<style scoped lang="scss">
.bg {
  width: 170px;
  height: 170px;
  background-image: url('@/assets/image/safeMap/bg1.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;

  .values {
    width: 118px;
    height: 118px;
    position: relative;

    .percent {
      font-size: 18px;
      font-weight: 600;
    }

    .value {
      font-size: 18px;
      line-height: 20px;
      font-family: MStiffHeiPRC, MStiffHeiPRC;
    }
  }

  .btns {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 122px;
    height: 32px;
    font-weight: 500;
    font-size: 18px;
    color: #ffffff;
    line-height: 30px;
    text-align: center;
    // background-image: url('./img/bg-btn1.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }
}
</style>
