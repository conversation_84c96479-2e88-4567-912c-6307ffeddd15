<template>
  <div class="left-comp h-full w-full flex items-center">
    <div class="bg flex items-center justify-center">
      <div class="values flex flex-col items-center justify-center h-180px w-180px">
        <progressCom
          :data="{
            status: mainIndicator.status,
            progress: mainIndicator.percent,
            color: mainIndicator.color,
            className: `progress-${mainIndicator.status}`,
          }"
        ></progressCom>
        <span class="value">{{ mainIndicator.value }}分</span>
        <div class="btns">{{ mainIndicator.name }}</div>
      </div>
    </div>
    <div class="right flex-1 grid grid-cols-2 gap-4 p-4">
      <!-- 四组圆环数据展示 -->
      <div v-for="(indicator, index) in indicators" :key="index" class="indicator-item">
        <div class="mini-bg flex items-center justify-center">
          <div class="mini-values flex flex-col items-center justify-center">
            <progressCom
              :data="{
                status: indicator.status,
                progress: indicator.percent,
                color: indicator.color,
                className: `progress-${indicator.status}`,
              }"
            ></progressCom>
            <span class="mini-value">{{ indicator.value }}{{ indicator.unit }}</span>
            <div class="mini-name">{{ indicator.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import progressCom from './progress.vue'

// 主要安全指数（原有的圆形进度显示）
const mainIndicator = {
  name: '平均分',
  percent: 89,
  value: 89,
  color: '#4FC7F5',
  status: 2,
}

// 四组模拟数据
const indicators = [
  {
    name: '火警处理率',
    percent: 95,
    value: 95,
    unit: '%',
    color: '#FF6B6B',
    status: 1,
  },
  {
    name: '设备在线率',
    percent: 87,
    value: 87,
    unit: '%',
    color: '#4ECDC4',
    status: 2,
  },
  {
    name: '隐患整改率',
    percent: 78,
    value: 78,
    unit: '%',
    color: '#45B7D1',
    status: 3,
  },
  {
    name: '巡检完成率',
    percent: 92,
    value: 92,
    unit: '%',
    color: '#96CEB4',
    status: 4,
  },
]
</script>

<style scoped lang="scss">
.bg {
  width: 170px;
  height: 170px;
  background-image: url('@/assets/image/safeMap/bg1.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;

  .values {
    width: 118px;
    height: 118px;
    position: relative;

    .percent {
      font-size: 18px;
      font-weight: 600;
    }

    .value {
      font-size: 18px;
      line-height: 20px;
      font-family: MStiffHeiPRC, MStiffHeiPRC;
      color: #ffffff;
    }
  }

  .btns {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 122px;
    height: 32px;
    font-weight: 500;
    font-size: 18px;
    color: #ffffff;
    line-height: 30px;
    text-align: center;
    // background-image: url('./img/bg-btn1.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }
}

// 右侧四个小圆环的样式
.right {
  .indicator-item {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .mini-bg {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    backdrop-filter: blur(10px);

    .mini-values {
      width: 80px;
      height: 80px;
      position: relative;

      .mini-value {
        font-size: 14px;
        line-height: 16px;
        font-weight: 600;
        color: #ffffff;
        margin-top: 5px;
      }

      .mini-name {
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        margin-top: 2px;
        opacity: 0.9;
      }
    }
  }
}
</style>
