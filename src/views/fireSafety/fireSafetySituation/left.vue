<template>
  <div class="w-full h-full left">
    <div class="health-analysis flex flex-col">
      <cardTtile title="最新安全指数分析"> </cardTtile>
      <safetyIndex></safetyIndex>
    </div>
    <div class="h-10px"></div>
    <div class="waring-analysis">
      <cardTtile title="储能安全指数排行"> </cardTtile>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted } from 'vue'
import cardTtile from '@/components/public/cardTtile.vue'
import safetyIndex from './comp/safetyIndex.vue'

const init = async () => {}

onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.left {
  width: 450px;
  display: grid;
  grid-template-rows: 270px 10px 1fr;
}
.health-analysis {
  padding: 0px 16px 16px 16px;
  height: 100%;
  background: rgba(27, 63, 137, 0.2);
}
.waring-analysis {
  padding: 5px 16px 16px 16px;
  height: 100%;
  background: rgba(27, 63, 137, 0.2);
}
</style>
