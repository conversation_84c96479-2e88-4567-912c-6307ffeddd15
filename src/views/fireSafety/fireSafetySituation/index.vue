<template>
  <div class="w-full h-full relative">
    <div class="w-full h-full relative">
      <div class="top" v-loading="loading">
        <div class="warning-cards">
          <div v-for="(item, index) in cardConfigs" :key="index" class="warning-card">
            <img
              :src="getImageUrl(`safeTop${index + 1}.png`)"
              class="w-full h-full absolute top-0 left-0 -z-10"
              alt="card background"
            />
            <div class="warning-content">
              <div class="waring-title">{{ item.title }}</div>
              <div class="flex mt-8px items-center font-normal">
                风险项
                <div class="waring-value ml-12px">{{ item.count }}</div>
              </div>
              <div class="waring-point w-109px h-28px">
                <div>风险点：{{ item.value }}处</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="center">
        <!-- <lms></lms> -->
      </div>
      <!-- 左侧抽屉区域 -->
      <div class="left">
        <button
          class="drawer-toggle"
          @click="leftDrawerOpen = !leftDrawerOpen"
          :style="leftDrawerOpen ? 'right:-35px;' : 'left:0; right:auto;'"
          aria-label="展开/收起左侧抽屉"
        >
          <span class="drawer-arrow"> <img src="@/assets/image/safeMap/leftPopup.png" class="w-50px h-50px" /></span>
        </button>
        <transition name="drawer-fade">
          <left v-show="leftDrawerOpen" />
        </transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import left from './left.vue'
import { useUserInfo } from '~/store'

const leftDrawerOpen = ref(true)
const userInfo = useUserInfo().value

const cardConfigs: Array<{ title: string; count: string; value: string | number }> = [
  { title: '建筑防火', count: '1', value: '0' },
  { title: '消防设施', count: '2', value: '0' },
  { title: '消防安全管理', count: '3', value: '0' },
]

const loading = ref(false)

const getImageUrl = (name: string) => {
  return new URL(`/src/assets/image/safeMap/${name}`, import.meta.url).href
}

const getTopCards = async () => {
  try {
    loading.value = true
    // const { data } = await $API.get({
    //   url,
    //   params: {
    //     superviseId: userInfo.superviseId,
    //   },
    // })
  } catch (e) {
    console.error(e)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getTopCards()
})
</script>

<style lang="scss" scoped>
.footer-menu {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;

  .tabs {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .tab-item {
    height: 39px;
    min-width: 158px;
    padding: 0 16px;
    border-radius: 4px;
    border: 1px solid rgba(98, 142, 215, 0.6);
    color: #ffffff;
    font-size: 14px;
    line-height: 39px;
    background: linear-gradient(353deg, rgba(68, 130, 255, 0.3) 0%, rgba(25, 63, 140, 0) 100%);
    box-shadow: inset 0 0 0 1px rgba(25, 63, 140, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(2px);

    &:hover:not(.active) {
      filter: brightness(1.08);
    }

    &.active {
      min-width: 159px;
      background: linear-gradient(353deg, rgba(225, 169, 30, 0.3) 0%, rgba(225, 169, 30, 0) 100%);
      border-color: rgba(225, 169, 30, 0.6);
      box-shadow: inset 0 0 0 1px rgba(225, 169, 30, 0.25);
    }
  }
}
.left {
  position: absolute;
  left: 0;
  width: 450px;
  height: 100%;
  background: linear-gradient(-90deg, rgba(6, 17, 23, 0) 0%, #061117 50%);
  border-radius: 0px 0px 0px 0px;
}
.top {
  position: absolute;
  top: 30px;
  z-index: 1;
  width: 100%;
  display: flex;
  justify-content: center;

  .warning-cards {
    position: relative;
    transition: opacity 0.2s ease;
    display: flex;
  }

  .warning-card {
    position: relative;
    width: 190px;
    height: 119px;
    border-radius: 4px;
    overflow: hidden;
    padding: 0 20px;
    margin-bottom: 20px;
    margin-right: 28px;

    .warning-content {
      position: relative;
      z-index: 1;
      color: white;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 19px;

      .waring-title,
      .waring-unit {
        height: 22px;
        font-size: 16px;
        color: #ffffff;
        line-height: 22px;
        font-weight: 700;
      }

      .waring-value {
        height: 39px;
        font-weight: 600;
        font-size: 28px;
        line-height: 39px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .waring-point {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 6px 6px 6px 6px;
        font-weight: 400;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &:nth-child(1) .waring-value {
      background-image: linear-gradient(180deg, #ffffff 0%, #7ec2ff 100%);
    }

    &:nth-child(2) .waring-value {
      background-image: linear-gradient(180deg, #ffffff 0%, #7ec2ff 100%);
    }

    &:nth-child(3) .waring-value {
      background-image: linear-gradient(180deg, #ffffff 0%, #b27eff 100%);
    }
  }
}
.center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
}
.left {
  position: absolute;
  left: 0;
  width: 450px;
  height: 100%;
  background: linear-gradient(90deg, rgba(6, 17, 23, 0) 0%, #061117 50%);
  border-radius: 0px 0px 0px 0px;
}
.drawer-toggle {
  position: absolute;
  top: 50%;
  z-index: 2;
  transform: translateY(-50%);
  cursor: pointer;
}
</style>
