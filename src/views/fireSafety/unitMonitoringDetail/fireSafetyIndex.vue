<template>
  <div class="fire-safety-index">
    <div class="top-row">
      <div class="panel">
        <CardTitle2 title="消防安全指数" />
        <div class="panel-body top-height">
          <TopSafetyOverview
            :loading="loading"
            :score="overview.score"
            :grade="overview.grade"
            :update-time="overview.updateTime"
            :stats="overview.stats"
          />
        </div>
      </div>
      <div class="panel">
        <CardTitle2 title="近30天综合安全趋势" />
        <div class="panel-body top-height">
          <TrendLineChart :x-data="trend.x" :y-data="trend.y" />
        </div>
      </div>
    </div>
    <div class="panelBottom">
      <!-- 底部：评估详情 -->
      <CardTitle2 title="评估详情" />
      <div class="panel-body bottom-body">
        <BottomAssessmentDetail
          :selected="selectedSection"
          :sections="sections"
          :tree-data="assessTree"
          @node-click="handleTreeNodeClick"
          @change-section="handleChangeSection"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from 'vue'
import TopSafetyOverview from './components/TopSafetyOverview.vue'
import TrendLineChart from './components/TrendLineChart.vue'
import BottomAssessmentDetail, { type Section } from './components/BottomAssessmentDetail.vue'
import CardTitle2 from '@/components/public/cardTitle2.vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const loading = ref(false)
const route = useRoute()
const userInfo = useUserInfo().value

// 顶部概览数据
const overview = reactive({
  score: 93,
  grade: '优秀',
  updateTime: '2024-03-13 17:04:29',
  stats: { alarm: 98, fault: 8, hazard: 8, action: 90 },
})

// 趋势图数据（近30天）
const trend = reactive<{ x: string[]; y: number[] }>({ x: [], y: [] })

// 评估项数据（左侧可选项）
const sections = ref<Section[]>([
  {
    key: 'buildFire',
    title: '建筑防火',
    riskCount: 0,
    warnCount: 1,
    inputCount: 0,
    items: ['消防审图与备案', '消防验收'],
  },
  {
    key: 'evac',
    title: '被动防火',
    riskCount: 0,
    warnCount: 0,
    inputCount: 0,
    items: ['防火分区', '防火隔断构件完整性'],
  },
  {
    key: 'elec',
    title: '电气火灾',
    riskCount: 0,
    warnCount: 1,
    inputCount: 0,
    items: ['配电室温升异常预警', '客梯电容异常'],
  },
  {
    key: 'gas',
    title: '可燃气体',
    riskCount: 0,
    warnCount: 0,
    inputCount: 0,
    items: ['厨房燃气监测', '报警联动'],
  },
])

// 选中项（默认第一个）
const selectedKey = ref<string>('buildFire')
const selectedSection = computed<Section | null>(() => sections.value.find((s) => s.key === selectedKey.value) || null)

// 评估树数据（右侧）
type AssessTreeNode = { id: string | number; label: string; children?: AssessTreeNode[] }
const assessTree = ref<AssessTreeNode[]>([])

// TODO: Apifox 接口替换完成
async function fetchOverviewAndTrend() {
  loading.value = true
  try {
    // 1) 顶部左侧：最新评估分
    const newestScoreReq = $API.post({
      url: '/fireSafety/getNewstScore',
      params: {
        superviseId: userInfo.superviseId,
        unitId: (route.query?.unitId as string) || '',
      },
    })
    // 2) 顶部右侧：风险趋势
    const riskTrendReq = $API.post({
      url: '/fireSafety/getRiskEcharts',
      data: {
        superviseId: userInfo.superviseId,
        unitId: (route.query?.unitId as string) || '',
      },
    })

    const [scoreRes, trendRes]: any = await Promise.all([newestScoreReq, riskTrendReq])
    if (scoreRes?.code === 'success' && scoreRes.data) {
      const d = scoreRes.data
      overview.score = Number(d.score ?? 0)
      overview.grade = d.grade ?? '-'
      overview.updateTime = d.updateTime ?? ''
      overview.stats = {
        alarm: Number(d.alarm ?? 0),
        fault: Number(d.fault ?? 0),
        hazard: Number(d.hazard ?? 0),
        action: Number(d.action ?? 0),
      }
    }
    if (trendRes?.code === 'success' && trendRes.data) {
      const t = trendRes.data
      trend.x = Array.isArray(t.xDates) ? t.xDates : []
      trend.y = Array.isArray(t.values) ? t.values : []
    }
  } finally {
    loading.value = false
  }
}

async function fetchAssessTree() {
  const res: any = await $API.post({
    url: '/fireSafety/queryAssessUnitDetail',
    data: {
      superviseId: userInfo.superviseId,
      unitId: (route.query?.unitId as string) || '',
    },
  })
  if (res?.code === 'success') {
    const mapNode = (n: any): AssessTreeNode => ({
      id: n.id ?? n.nodeId ?? n.code ?? Math.random(),
      label: n.name ?? n.title ?? n.label ?? '未命名',
      children: Array.isArray(n.children) ? n.children.map(mapNode) : undefined,
    })
    assessTree.value = Array.isArray(res.data) ? res.data.map(mapNode) : []
  }
}

function handleTreeNodeClick(node: AssessTreeNode) {
  const found = sections.value.find((s) => s.title === node.label || s.key === String(node.id))
  if (found) selectedKey.value = found.key
}

function handleChangeSection(key: string) {
  selectedKey.value = key
}

onMounted(() => {
  fetchOverviewAndTrend()
  fetchAssessTree()
})
</script>

<style scoped lang="scss">
.fire-safety-index {
  display: grid;
  grid-template-rows: auto 1fr auto 1fr;
  gap: 8px;
  height: 800px;
  padding: 8px 12px;
}
.top-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}
.panel {
  height: 280px;
  display: grid;
  grid-template-rows: auto 1fr;
  background: rgba(27, 63, 137, 0.2);
}
.panelBottom {
  margin-top: 10px;
  height: 480px;
  overflow-y: scroll;
  background: rgba(27, 63, 137, 0.2);
}
.panel-body {
  border-radius: 0;
  padding: 24px;
}

.bottom-body {
  overflow: auto;
}
</style>
