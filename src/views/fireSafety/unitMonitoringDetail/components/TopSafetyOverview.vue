<template>
  <div class="top-overview" v-loading="loading">
    <div class="left-score">
      <div class="score-circle">
        <span class="text-[48px]">{{ score || '0' }}</span>
        <span class="label">消防安全指数</span>
      </div>
    </div>
    <div class="desc mt-10px">
      <div class="mb-20px grade">消防安全指数{{ score || '0' }}分</div>
      <div class="grade mb-20px">
        风险等级：<span class="strong">{{ grade }}</span>
      </div>
      <div class="meta">
        评估指标：共 {{ stats.alarm }} 项 | {{ stats.alarm }} 项高风险 ， {{ stats.fault }} 项异常 ，
        {{ stats.hazard }} 项未输入
      </div>
      <div class="meta">评估时间：{{ updateTime || '-' }}</div>
      <div class="meta">评估结果仅供了解单位消防安全情况参考之用，其内容并未包含所有影响消防安全风险的因素。</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

defineProps<{
  loading?: boolean
  score: number
  grade: string
  updateTime: string
  stats: { alarm: number; fault: number; hazard: number; action: number }
}>()
</script>

<style scoped lang="scss">
.top-overview {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 4px;
}
.left-score {
  display: flex;
  align-items: center;
  margin-right: 20px;
  gap: 16px;
}
.score-circle {
  margin-top: 2px;
  width: 145px;
  height: 128px;
  background: url('@/assets/image/energyStorageSafety/healthiness-bg.png') no-repeat center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.score {
  font-size: 14px;
  font-weight: 400;
}
.label {
  opacity: 0.85;
  margin-top: 6px;
}
.desc .grade {
  font-size: 18px;
  font-weight: 600;
  color: #e6f7ff;
}
.desc .meta {
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 10px;
}
</style>
