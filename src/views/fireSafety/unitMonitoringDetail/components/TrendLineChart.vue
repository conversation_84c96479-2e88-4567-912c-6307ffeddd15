<template>
  <div class="trend-card">
    <div ref="chartRef" class="chart" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed } from 'vue'
import type { EChartsOption } from 'echarts'
import { useECharts } from '@/hooks/web/useECharts'

const props = defineProps<{
  xData: string[]
  yData: number[]
  loading?: boolean
}>()

const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(chartRef as any, 'default')

const option = computed<EChartsOption>(() => ({
  color: ['#5B8FF9'],
  grid: { left: 40, right: 20, top: 30, bottom: 30 },
  tooltip: { trigger: 'axis' },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: props.xData,
    axisLine: { lineStyle: { color: '#7fa6d6' } },
    axisLabel: { color: '#bcd2f0' },
  },
  yAxis: {
    type: 'value',
    axisLine: { show: false },
    splitLine: { lineStyle: { color: 'rgba(127,166,214,0.2)' } },
    axisLabel: { color: '#bcd2f0' },
  },
  series: [
    {
      type: 'line',
      data: props.yData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: { width: 2 },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(91,143,249,0.35)' },
            { offset: 1, color: 'rgba(91,143,249,0.02)' },
          ],
        },
      },
    },
  ],
}))

onMounted(() => setOptions(option.value))
watch(option, (opt) => setOptions(opt))
</script>

<style scoped lang="scss">
.trend-card {
  height: 100%;
  position: relative;
  padding-top: 36px;
}
</style>
