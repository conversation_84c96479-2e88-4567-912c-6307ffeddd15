<template>
  <popupSideBlack v-model="visible" :popup-title="popupTitle">
    <div class="body">
      <div class="title">
        <span>{{ sectionTitle }}</span>
      </div>
      <div class="texts">
        <div v-for="item in texts" :key="item.label">
          <div class="name">{{ item.label }}</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
      <div class="title mt-40px">
        <span>设备信息</span>
      </div>
      <div class="devices">
        <div v-for="item in deviceInfo" :key="item.label">
          <div class="name">{{ item.label }}</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </popupSideBlack>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import popupSideBlack from '~/components/public/popup/popupSideBlack.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  event: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['update:modelValue'])
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const popupTitle = computed<string>(() => {
  const map: Record<string, string> = { '1': '火警详情', '3': '故障详情', '4': '隐患详情', '5': '动作详情' }
  return map[props.event] ?? '火警详情'
})

const sectionTitle = computed<string>(() => {
  const map: Record<string, string> = { '1': '火警信息', '3': '故障信息', '4': '隐患信息', '5': '动作信息' }
  return map[props.event] ?? '火警信息'
})

const texts = computed<Array<{ label: string; value: string }>>(() => {
  const time1 = '2025-07-27 11:33:08'
  const time2 = '2025-07-27 11:33:08'
  switch (props.event) {
    case '1': //火警
      return [
        { label: '平台首次接收时间', value: time1 },
        { label: '平台末次接收时间', value: time2 },
      ]
    case '3': //故障
      return [
        { label: '故障描述', value: '故障' },
        { label: '平台首次接收时间', value: time1 },
        { label: '平台末次接收时间', value: time2 },
      ]
    case '4': //隐患
      return [
        { label: '隐患分类', value: '消防设施类隐患' },
        { label: '隐患来源', value: '物联网监测' },
        { label: '隐患问题', value: '管网压力异常' },
        { label: '隐患位置', value: '4号仓库库房东门消火栓内' },
        { label: '平台末次接收时间', value: time2 },
      ]
    case '5': //动作
      return [
        { label: '动作描述', value: '动作' },
        { label: '平台首次接收时间', value: time1 },
        { label: '平台末次接收时间', value: time2 },
      ]
    default:
      return []
  }
})
const deviceInfo = computed<Array<{ label: string; value: string }>>(() => [
  { label: '所属电站', value: '林西储能电站' },
  { label: '设备编号', value: '20240928152345649899' },
  { label: '主机回路点位', value: '1-1-17' },
  { label: '二次码', value: '000101017' },
  { label: '系统类型', value: '火灾自动报警系统' },
  { label: '设备类型', value: '点型感烟火灾探测器' },
  { label: '设备位置', value: '3#1高压柜' },
  { label: '品牌型号', value: '海康威视' },
  { label: '安装日期', value: '2024-09-28' },
])
</script>
<style lang="scss" scoped>
.body {
  display: flex;
  flex-direction: column;
  height: 100%;
  > .title {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    > span:first-child {
      font-weight: 600;
      font-size: 18px;
      margin-right: 20px;
    }
  }
  > .texts,
  > .devices {
    > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0px;
      > .name {
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }
      > .value {
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
      }
      &:nth-child(2n + 1) {
        position: relative;
        background: linear-gradient(
          90deg,
          rgba(21, 85, 181, 0) 1%,
          rgba(13, 59, 140, 0.2) 52%,
          rgba(21, 85, 181, 0) 100%
        );
        &::after,
        &::before {
          content: '';
          position: absolute;
          left: 0;
          width: 100%;
          height: 1px;
          background: linear-gradient(90deg, rgba(35, 88, 178, 0.01) 0%, #184eac 52%, rgba(35, 88, 178, 0) 100%);
        }
        &::after {
          bottom: 0;
        }
        &::before {
          top: 0;
        }
      }
    }
  }
}
</style>
