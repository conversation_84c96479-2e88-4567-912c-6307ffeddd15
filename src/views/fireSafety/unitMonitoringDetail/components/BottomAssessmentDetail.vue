<template>
  <div class="assessment-detail">
    <!-- 左侧：纵向标签列表 -->
    <div class="left-pane">
      <ul class="nav-list">
        <li
          v-for="sec in sections"
          :key="sec.key"
          :class="['nav-item', { active: selected?.key === sec.key }]"
          @click="emit('change-section', sec.key)"
        >
          <span class="nav-title">{{ sec.title }}</span>
          <span class="nav-stats">
            <em class="c-danger">{{ sec.riskCount }}项风险</em>
            <em class="c-warn">{{ sec.warnCount }}项异常</em>
            <em class="c-info">{{ sec.inputCount }}项未输入</em>
          </span>
        </li>
      </ul>
    </div>
    <!-- 右侧：评估树（自定义节点） -->
    <div class="right-pane">
      <el-tree
        class="assess-tree"
        :data="treeData"
        node-key="id"
        :props="treeProps"
        highlight-current
        @node-click="onNodeClick"
      >
        <template #default="{ data }">
          <div class="node-row">
            <span class="dot" />
            <span class="label">{{ data.label }}</span>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

export type Section = {
  key: string
  title: string
  riskCount: number
  warnCount: number
  inputCount: number
  items: string[]
}

type AssessTreeNode = { id: string | number; label: string; children?: AssessTreeNode[] }

defineProps<{
  selected: Section | null
  sections: Section[]
  treeData: AssessTreeNode[]
}>()

const emit = defineEmits<{
  (e: 'node-click', node: AssessTreeNode): void
  (e: 'change-section', key: string): void
}>()

const treeProps = { children: 'children', label: 'label' }
const onNodeClick = (node: AssessTreeNode) => emit('node-click', node)
</script>

<style scoped lang="scss">
.assessment-detail {
  display: grid;
  grid-template-columns: 340px 1fr;
  gap: 12px;
}
.c-danger {
  color: #ff6b6b;
}
.c-warn {
  color: #ffb74d;
}
.c-info {
  color: #9fb7d3;
}
.left-pane {
  background: transparent;
  padding: 8px 12px;
}
.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background: rgba(8, 20, 40, 0.35);
  border: 1px solid rgba(60, 120, 200, 0.25);
  border-radius: 4px;
  cursor: pointer;
  color: #cfe6ff;
}
.nav-item.active {
  background: rgba(27, 63, 137, 0.35);
  border-color: rgba(96, 149, 255, 0.45);
}
.nav-title {
  font-weight: 600;
}
.nav-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
}
.right-pane {
  background: transparent;
  padding: 8px 12px;
}
.sel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.sel-title {
  color: #cfe6ff;
  font-weight: 600;
}
.sel-stats {
  display: flex;
  gap: 14px;
  font-size: 12px;
}
.bullet-list {
  margin: 0;
  padding-left: 18px;
}
.bullet-list li {
  line-height: 22px;
}
.assess-tree {
  height: 100%;
  overflow: auto;
}
.node-row {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #cfe6ff;
}
.node-row .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #5b8ff9;
  display: inline-block;
}
.node-row .label {
  line-height: 22px;
}
.empty {
  color: #9fb7d3;
  padding: 12px 0;
}
</style>
