<template>
  <div class="fire-system-monitoring">
    <div class="small-screen-title">
      <el-segmented v-model="activeTab" class="small-screen-segmented" :options="systemOptions" size="small" />
    </div>
    <div class="filters">
      <el-form :inline="true" label-width="auto">
        <el-form-item label="设备类型">
          <DeviceTypeCas v-model="filters.deviceType" @change="handleSearch" />
        </el-form-item>
        <el-form-item label="设备状态">
          <el-select v-model="filters.deviceStatus" placeholder="请选择" clearable @change="handleSearch">
            <el-option v-for="opt in deviceStatusOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="监测状态">
          <el-select v-model="filters.monitorStatus" placeholder="请选择" clearable @change="handleSearch">
            <el-option v-for="opt in monitorStatusOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="在线状态">
          <el-select v-model="filters.onlineStatus" placeholder="请选择" clearable @change="handleSearch">
            <el-option v-for="opt in onlineStatusOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备编号">
          <el-input v-model.trim="filters.deviceNo" placeholder="请输入" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格（公共组件） -->
    <table-list
      ref="tableRef"
      :columns="columnsWithOp"
      :api="fetchTable"
      :noPage="false"
      :pagination="paginationProps"
      border
      stripe
      class="fs-table"
    >
      <template #op="{ row }">
        <el-button link type="primary" @click="viewDetail(row)">详情</el-button>
        <el-button link type="primary" @click="viewLocation(row)">位置</el-button>
      </template>
    </table-list>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, onMounted, watch } from 'vue'
import tableList, { IColumn, IPaginationProps } from '@/components/public/tableList/index.vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { deviceStatusOptions, monitorStatusOptions, onlineStatusOptions } from './data'
import { useUserInfo } from '~/store'
import DeviceTypeCas from '@/components/public/common-form/deviceTypeCas.vue'
type PageModel = { pageNo: number; pageSize: number }
type ApiData<T> = { rows: T[]; total: number }
type ApiRes<T> = { code?: string; data?: ApiData<T> }

type BaseRow = {
  index?: number
  deviceNo?: string
  devicePos?: string
  deviceStatus?: string | number
  onlineStatus?: string | number
  monitorStatus?: string | number
}
type FireRow = BaseRow & {
  systemType?: string
  deviceType?: string
}
type GasRow = BaseRow & {
  gasType?: string
  concentration?: string | number
}
type SystemRow = FireRow | GasRow

type TabKey = 'fire' | 'gas'

const activeTab = ref<TabKey>('fire')
const loading = ref(false)
const route = useRoute()
const userInfo = useUserInfo().value

const systemOptions = ref([
  { label: '火灾自动报警系统', value: 'fire' },
  { label: '可燃气体监测系统', value: 'gas' },
])
// 筛选项
const filters = reactive({
  deviceType: '' as string | number,
  deviceStatus: '' as string | number,
  monitorStatus: '' as string | number,
  onlineStatus: '' as string | number,
  deviceNo: '' as string,
})

// 列：不同系统不同列
const fireColumns: IColumn[] = [
  { prop: 'index', label: '序号', width: 80, align: 'center' },
  { prop: 'deviceNum', label: '设备编号', minWidth: 160 },
  { prop: 'deviceTypePname', label: '系统类型', minWidth: 140 },
  { prop: 'deviceTypeName', label: '设备类型', minWidth: 160 },
  { prop: 'deviceAddress', label: '设备位置', minWidth: 220 },
  { prop: 'deviceState', label: '设备状态', width: 120, align: 'center' },
  { prop: 'onlineState', label: '在线状态', width: 120, align: 'center' },
  { prop: 'monitorState', label: '监测状态', width: 120, align: 'center' },
]

const gasColumns: IColumn[] = [
  { prop: 'index', label: '序号', width: 80, align: 'center' },
  { prop: 'deviceNum', label: '设备编号', minWidth: 160 },
  { prop: 'deviceTypePname', label: '系统类型', width: 120 },
  { prop: 'deviceTypeName', label: '设备类型', minWidth: 160 },
  { prop: 'deviceAddress', label: '设备位置', minWidth: 220 },
  { prop: 'deviceState', label: '设备状态', width: 120, align: 'center' },
  { prop: 'onlineState', label: '在线状态', width: 120, align: 'center' },
  { prop: 'monitorState', label: '监测状态', width: 120, align: 'center' },
]

const currentColumns = computed<IColumn[]>(() => (activeTab.value === 'fire' ? fireColumns : gasColumns))
const columnsWithOp = computed<IColumn[]>(() => [
  ...currentColumns.value,
  { label: '操作', width: 120, fixed: 'right', align: 'center', slot: 'op' },
])

const tableRef = ref<InstanceType<typeof tableList> | null>(null)
const paginationProps = reactive<IPaginationProps>({ pageSizes: [10, 30, 50, 100] })

watch(activeTab, () => {
  tableRef.value?.getTableData(1)
})

const handleSearch = () => tableRef.value?.getTableData(1)
const viewDetail = (row: Record<string, any>) => console.log('详情', activeTab.value, row)
const viewLocation = (row: Record<string, any>) => console.log('位置', activeTab.value, row)

const API_URL: Record<TabKey, string> = {
  fire: '/fireSafety/queryDeviceEventLisBySystemType',
  gas: '/fireSafety/queryDeviceEventLisBySystemType',
}
// fireSystemType: 消防系统分类
// 1.火灾自动报警系统 2.物联网传输设备 3.独立式报警设备 4.消防供水监测设备 5.电气火灾监测设备 6.可燃气体监测设备
const FIRE_SYSTEM_TYPE_MAP: Record<TabKey, 1 | 6> = {
  fire: 1,
  gas: 6,
}
const fetchTable = async (pageModel: PageModel): Promise<ApiRes<SystemRow>> => {
  loading.value = true
  try {
    const res: any = await $API.post({
      url: API_URL[activeTab.value],
      data: {
        superviseId: userInfo.superviseId,
        unitId: (route.query?.unitId as string) || '',
        fireSystemType: FIRE_SYSTEM_TYPE_MAP[activeTab.value],
        pageNo: pageModel.pageNo,
        pageSize: pageModel.pageSize,
        deviceType: filters.deviceType,
        deviceStatus: filters.deviceStatus,
        monitorStatus: filters.monitorStatus,
        onlineStatus: filters.onlineStatus,
        deviceNo: filters.deviceNo,
      },
    })
    return res
  } finally {
    loading.value = false
  }
}

onMounted(() => {})
</script>

<style scoped lang="scss">
.fire-system-monitoring {
  padding: 0 16px;
  background: rgba(27, 63, 137, 0.2);
  .small-screen-title {
    padding: 30px 20px;
  }
  .system-tabs {
    display: flex;
    justify-content: center;
    gap: 24px;
    padding: 10px 0 6px;
    .sys-tab {
      padding: 6px 14px;
      border-radius: 4px;
      color: #cfe8ff;
      cursor: pointer;
      transition: all 0.2s ease;
      background: rgba(7, 141, 236, 0.15);
      &.active {
        color: #fff;
        background: linear-gradient(90deg, rgba(7, 141, 236, 1) 0%, rgba(41, 102, 220, 1) 100%);
        transform: translateY(-1px);
      }
    }
  }

  .filters {
    display: flex;
    align-items: center;
    margin: 0 16px 8px 20px;
    :deep(.el-select) {
      width: 200px;
    }
    :deep(.el-input) {
      width: 220px;
    }
  }

  .fs-table {
    width: 100%;
  }
}
</style>
