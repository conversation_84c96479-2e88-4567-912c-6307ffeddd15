<template>
  <div class="warning-stats-container">
    <div class="big-screen-title">
      <el-segmented
        v-model="value"
        class="big-screen-segmented"
        :options="timeOptions"
        @change="handleTimeChange"
        size="small"
      />
    </div>
    <!-- 单位概况 -->
    <div v-if="value === 1">
      <unitOverview />
    </div>
    <!-- 实时监测 -->
    <div v-if="value === 2">
      <realTimeMonitoring />
    </div>
    <!-- 消防系统运行监测 -->
    <div v-if="value === 3">
      <fireSystemMonitoring />
    </div>
    <!-- 设备档案 -->
    <div v-if="value === 4">
      <equipmentArchive />
    </div>
    <!-- 消防安全指数 -->
    <div v-if="value === 5">
      <fireSafetyIndex />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import unitOverview from './unitOverview.vue'
import realTimeMonitoring from './realTimeMonitoring.vue'
import fireSystemMonitoring from './fireSystemMonitoring.vue'
import equipmentArchive from './equipmentArchive.vue'
import fireSafetyIndex from './fireSafetyIndex.vue'
// import $API from '~/common/api'
// import { useUserInfo } from '~/store'

// const userInfo = useUserInfo().value
const timeOptions = [
  { label: '单位概况', value: 1 },
  { label: '实时监测', value: 2 },
  { label: '消防系统运行监测', value: 3 },
  { label: '设备档案', value: 4 },
  { label: '消防安全指数', value: 5 },
]
const value = ref(1)

const handleTimeChange = (value) => {
  console.log('value', value)
}

onMounted(() => {})
</script>

<style lang="scss" scoped>
.warning-stats-container {
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat;
  background-size: 100% 100%;
  height: 100%;
  .big-screen-title {
    display: flex;
    margin-bottom: 19px;
  }
}
</style>
