<template>
  <div class="unit-overview" v-loading="loading">
    <div class="top">
      <div class="title">
        <cardTitle3 :title="unitName">
          <template #subTitle>
            {{ unitInfo?.unitAddress }}
          </template>
        </cardTitle3>
        <div class="row">
          <div class="left">
            <span> 消防安全责任人：{{ unitInfo?.personLiableName }} {{ unitInfo?.personLiableTel }}</span>
            <span> 消防安全管理人：{{ unitInfo?.contactsName }} {{ unitInfo?.contactsTel }}</span>
          </div>
          <div class="right">
            <span>{{ unitInfo?.score || '0' }}</span>
            <span class="label">消防安全指数</span>
          </div>
        </div>
      </div>
    </div>
    <div class="bottpm">
      <cardTitle3 title="建筑信息" />
      <!-- 顶部汇总条 -->
      <div class="message">
        <div>
          <span class="label">总接入面积：</span>
          <span class="value">{{ unitInfo?.buildingArea }}</span>
          <span class="ml-6px">m<sup>2</sup></span>
        </div>
        <div>
          <span class="label">接入建筑数：</span> <span class="value">{{ unitInfo?.buildingCount ?? 0 }}</span>
          <span class="ml-6px">栋</span>
        </div>
      </div>

      <div class="buildings">
        <div v-for="(b, idx) in buildingList" :key="idx" class="card">
          <div class="card-bg"></div>
          <div v-for="(f, i) in fields" :key="f.key" class="field" :style="{ top: `${rowTops[i]}px` }">
            <span class="label">{{ f.label }}</span>
            <span class="value">{{ (b as any)[f.key] }}</span>
          </div>
          <div class="card-name">{{ b.buildingName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import cardTitle3 from '@/components/public/cardTitle3.vue'
import { useRoute } from 'vue-router'
import { ref, onMounted, computed } from 'vue'
import $API from '~/common/api'

// 建筑信息数据结构
type Building = {
  buildingName: string
  downFloor: number
  upFloor: number
  buildingHeight: number
  buildingArea: number
}

type Field = {
  label: string
  key: keyof Building
  unit?: string
}
type UnitInfo = {
  unitAddress: string
  personLiableName: string
  personLiableTel: string
  contactsName: string
  contactsTel: string
  score: number
  buildingArea: number
  buildingCount: number
  buildingList: Building[]
}

const loading = ref(false)
const route = useRoute()
const unitName = ref(route.query?.unitName as string)
const unitInfo = ref<UnitInfo | null>(null)
const buildingList = computed<Building[]>(() => unitInfo.value?.buildingList ?? [])

const fields = ref<Field[]>([
  { label: '地下层数（层）：', key: 'downFloor' },
  { label: '地上层数（层）：', key: 'upFloor' },
  { label: '建筑高度（米）：', key: 'buildingHeight' },
  { label: '建筑面积（平方米）：', key: 'buildingArea' },
])

const rowTops = [48, 96, 148, 200]

const getUnitInfo = async () => {
  loading.value = true
  const res: any = await $API.post({
    url: '/fireSafety/getUnitInfoById',
    params: {
      unitId: route.query?.unitId,
    },
  })
  const data = res?.data || {}
  unitInfo.value = {
    unitAddress: data.unitAddress,
    personLiableName: data.personLiableName,
    personLiableTel: data.personLiableTel,
    contactsName: data.contactsName,
    contactsTel: data.contactsTel,
    score: data.score,
    buildingArea: data.buildingArea,
    buildingCount: data.buildingCount,
    buildingList: Array.isArray(data.buildingList) ? data.buildingList : [],
  }
  loading.value = false
}
onMounted(() => {
  getUnitInfo()
})
</script>
<style scoped lang="scss">
.unit-overview {
  .top {
    width: 100%;
    height: 140px;
    background: rgba(27, 63, 137, 0.2);
    border-radius: 0px 0px 0px 0px;
    margin-bottom: 20px;

    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        display: flex;
        text-align: left;
        flex-direction: column;
        gap: 10px;
        margin-top: 20px;
        margin-left: 50px;
        color: #9fbcd0;
        display: flex;
        align-items: center;
        font-size: 14px;

        span {
          text-align: left;
        }
      }
      .right {
        position: absolute;
        right: 0px;
        margin-right: 80px;
        width: 125px;
        height: 112px;
        background: url('@/assets/image/energyStorageSafety/healthiness-bg.png') no-repeat center;
        background-size: cover;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        > span {
          width: 100%;
          text-align: center;
        }
        > span:first-child {
          font-weight: 600;
          font-size: 26px;
          color: #ffffff;
          margin-bottom: 4px;
        }
        > span:last-child {
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }
  }
  .bottpm {
    width: 100%;
    height: calc(100vh - 320px);
    overflow-y: auto;
    background: rgba(27, 63, 137, 0.2);
    border-radius: 0px 0px 0px 0px;
    /* 顶部汇总条 */
    .message {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background-image: url('@/assets/image/fireSafety/architectureMessage.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      margin-left: 50px;
      margin-top: 20px;
      height: 69px;
      width: 475px;
      .label {
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }
      .value {
        font-weight: 600;
        font-size: 22px;
        line-height: 22px;
        color: #ffffff;
      }
    }

    /* 建筑卡片区 */
    .buildings {
      display: flex;
      gap: 40px;
      margin: 20px 0 40px 50px;
      .card {
        position: relative;
        width: 231px;
        height: 268px;
        .card-bg {
          position: absolute;
          inset: 0;
          background: url('@/assets/image/fireSafety/architectureDetail.png') no-repeat center;
          background-size: 100% 100%;
          pointer-events: none;
        }
        .field {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -100%);
          display: flex;
          align-items: center;
          white-space: nowrap;
          word-break: keep-all;
          color: #9fbcd0;
          font-size: 14px;
        }
        .card-name {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: 5px;
          height: 36px;
          padding: 0 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 16px;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
