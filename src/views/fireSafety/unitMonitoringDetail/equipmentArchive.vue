<template>
  <div class="equipment-archive">
    <!-- 筛选表单 -->
    <div class="content">
      <div class="filters">
        <el-form :inline="true" label-width="auto">
          <el-form-item label="设备类型">
            <el-select v-model="filters.deviceType" placeholder="请选择" clearable @change="handleSearch">
              <el-option v-for="opt in deviceTypeOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="楼栋楼层">
            <el-select v-model="filters.building" placeholder="请选择" clearable @change="handleSearch">
              <el-option v-for="opt in buildingOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input v-model.trim="filters.deviceNo" placeholder="请输入" clearable @keyup.enter="handleSearch" />
          </el-form-item>
        </el-form>
        <QueryButton text="查询" @click="handleSearch" />
      </div>

      <table-list
        ref="tableRef"
        :columns="columnsWithOp"
        :api="fetchTable"
        :noPage="false"
        :pagination="paginationProps"
      >
        <template #op="{ row }">
          <el-button link type="primary" @click="viewDetail(row)">详情</el-button>
          <el-button link type="primary" @click="viewLocation(row)">位置</el-button>
        </template>
      </table-list>
    </div>
  </div>
  <!-- 设备档案详情 -->
  <deviceArchive v-model="visible" v-if="visible" />
</template>

<script setup lang="ts">
import { computed, reactive, ref, onMounted } from 'vue'
import tableList, { IColumn, IPaginationProps } from '@/components/public/tableList/index.vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import QueryButton from '@/components/public/common-form/QueryButton.vue'
import deviceArchive from '@/views/fireSafety/unitMonitoringDetail/components/deviceArchive.vue'
// 筛选项
const filters = reactive({
  deviceType: '' as string | number,
  building: '' as string | number,
  deviceNo: '' as string,
})
const visible = ref(false)

const deviceTypeOptions = ref([
  { label: '点型感烟探测器', value: 'smoke' },
  { label: '点型感温探测器', value: 'heat' },
])
const buildingOptions = ref([
  { label: 'H6-5', value: 'H6-5' },
  { label: 'H6-6', value: 'H6-6' },
])

const baseColumns = ref<IColumn[]>([
  { prop: 'index', label: '序号', width: 80, align: 'center' },
  { prop: 'deviceNum', label: '设备编号', minWidth: 160 },
  { prop: 'deviceTypePname', label: '系统类型', minWidth: 140 },
  { prop: 'deviceTypeName', label: '设备类型', minWidth: 160 },
  { prop: 'deviceAddress', label: '设备位置', minWidth: 220 },
  { prop: 'brandName', label: '设备品牌', minWidth: 120 },
  { prop: 'onlineState', label: '在线状态', width: 120, align: 'center' },
])

const columnsWithOp = computed<IColumn[]>(() => [
  ...baseColumns.value,
  { label: '操作', width: 120, fixed: 'right', align: 'center', slot: 'op' },
])

const tableRef = ref<InstanceType<typeof tableList> | null>(null)
const paginationProps = reactive<IPaginationProps>({ pageSizes: [10, 30, 50, 100] })
const route = useRoute()
const userInfo = useUserInfo().value
const loading = ref(false)

const handleSearch = () => tableRef.value?.getTableData(1)
const viewDetail = (row: Record<string, any>) => {
  console.log(row)
  visible.value = true
}
const viewLocation = (row: Record<string, any>) => console.log('设备位置', row)

const fetchTable = async (pageModel: { pageNo: number; pageSize: number }) => {
  loading.value = true
  try {
    const res: any = await $API.post({
      url: '/fireSafety/queryDeviceInfoPageList',
      data: {
        superviseId: userInfo.superviseId,
        unitId: (route.query?.unitId as string) || '',
        pageNo: pageModel.pageNo,
        pageSize: pageModel.pageSize,
        deviceType: filters.deviceType,
        buildingId: filters.building,
        deviceNo: filters.deviceNo,
      },
    })
    return res
  } finally {
    loading.value = false
  }
}

onMounted(() => {})
</script>

<style scoped lang="scss">
.equipment-archive {
  height: calc(100vh - 260px);
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  background: rgba(27, 63, 137, 0.2);
  .content {
    flex: 1;
    .filters {
      display: flex;
      align-items: center;
      padding-top: 30px;
      justify-content: space-between;
      margin: 0 20px 8px 20px;
      :deep(.el-select) {
        width: 200px;
      }
      :deep(.el-input) {
        width: 220px;
      }
    }
  }
}
</style>
