<template>
  <div class="real-time-monitoring">
    <!-- 顶部状态 Tab -->
    <div class="status-tabs" v-loading="loading">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-card', tab.key === activeTab ? 'active' : '', tab.color]"
        @click="onTabChange(tab.key)"
      >
        <div class="title">{{ tab.name }}</div>
        <div class="count">{{ counts[tab.key] ?? 0 }}</div>
      </div>
    </div>

    <!-- 条件筛选区 -->
    <div class="filters">
      <el-form :inline="true" label-width="auto">
        <el-form-item label="设备类型">
          <DeviceTypeCas v-model="filters.systemType" @change="handleSearch" />
        </el-form-item>
        <el-form-item label="楼栋楼层">
          <BuilidAndfloorCas v-model="filters.building" @change="handleSearch" />
        </el-form-item>
        <el-form-item label="动作描述">
          <el-input v-model="filters.actionDesc" placeholder="请选择" clearable @change="handleSearch" />
        </el-form-item>
        <el-form-item label="主机号">
          <el-select v-model="filters.location" placeholder="请选择" clearable @change="handleSearch">
            <el-option v-for="opt in locationOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </el-form-item>
    </div>

    <!-- 表格（公共组件） -->
    <table-list
      ref="tableRef"
      :columns="columnsWithOp"
      :api="fetchTable"
      :noPage="false"
      :pagination="paginationProps"
      border
      stripe
      class="rt-table"
    >
      <template #op="{ row }">
        <el-button link type="primary" @click="viewDetail(row)">详情</el-button>
        <el-button link type="primary" @click="viewLocation(row)">位置</el-button>
      </template>
    </table-list>
  </div>
  <!-- 火警详情 -->
  <firePopup v-model="visible" v-if="visible && activeTab === '1'" />
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import tableList, { IColumn, IPaginationProps } from '@/components/public/tableList/index.vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import DeviceTypeCas from '@/components/public/common-form/deviceTypeCas.vue'
import BuilidAndfloorCas from '@/components/public/common-form/builidAndfloorCas.vue'
import firePopup from '@/views/fireSafety/unitMonitoringDetail/components/firePopup.vue'

// 事件类型(1.火警 2.预警 3.故障 4.隐患 5.动作)
type TabKey = '1' | '3' | '4' | '5'

type ColumnDef = IColumn

// 顶部 Tab 配置
const tabs = ref<Array<{ key: TabKey; name: string; color: string }>>([
  { key: '1', name: '火警 (起)', color: 'c-red' },
  { key: '3', name: '故障 (起)', color: 'c-orange' },
  { key: '4', name: '隐患 (起)', color: 'c-magenta' },
  { key: '5', name: '动作 (起)', color: 'c-blue' },
])
const userInfo = useUserInfo().value
const route = useRoute()
const activeTab = ref<TabKey>('1')
const counts = reactive<Record<TabKey, number>>({
  '1': 0,
  '3': 0,
  '4': 0,
  '5': 0,
})
const visible = ref(false)
const loading = ref(false)
// 筛选项
const filters = reactive({
  systemType: '' as string | number,
  building: '' as string | number,
  location: '' as string | number,
  actionDesc: '' as string | number,
})

const columnMap: Record<TabKey, ColumnDef[]> = {
  '1': [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'deviceTypePName', label: '系统类型', width: 160 },
    { prop: 'deviceTypeName', label: '设备类型' },
    { prop: 'deviceAddress', label: '设备位置', width: 240 },
    { prop: 'laMake', label: '主机回路点位', width: 160 },
    { prop: 'iotReceiveTime', label: '平台接收时间', width: 160 },
    { prop: 'firstEventTime', label: '设备上报时间', width: 160 },
  ],
  '3': [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'deviceTypePName', label: '系统类型', width: 160 },
    { prop: 'deviceTypeName', label: '设备类型' },
    { prop: 'deviceAddress', label: '设备位置', width: 240 },
    { prop: 'description', label: '故障描述' },
    { prop: 'laMake', label: '主机回路点位', width: 160 },
    { prop: 'iotReceiveTime', label: '平台接收时间', width: 160 },
    { prop: 'firstEventTime', label: '设备上报时间', width: 160 },
  ],
  '4': [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'deviceTypePName', label: '系统类型', width: 160 },
    { prop: 'deviceTypeName', label: '设备类型' },
    { prop: 'deviceAddress', label: '设备位置', width: 240 },
    { prop: 'deviceNum', label: 'IMEI', width: 160 },
    { prop: 'hdProblemCode', label: '隐患问题' },
    { prop: 'hazardType', label: '隐患分类' },
    { prop: 'className', label: '隐患来源' },
    { prop: 'firstEventTime', label: '平台接收时间', width: 160 },
  ],
  '5': [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'deviceTypePName', label: '系统类型', width: 160 },
    { prop: 'deviceTypeName', label: '设备类型' },
    { prop: 'deviceAddress', label: '设备位置', width: 240 },
    { prop: 'description', label: '动作描述' },
    { prop: 'laMake', label: '主机回路点位', width: 160 },
    { prop: 'iotReceiveTime', label: '平台接收时间', width: 160 },
    { prop: 'firstEventTime', label: '设备上报时间', width: 160 },
  ],
}

const currentColumns = computed<ColumnDef[]>(() => columnMap[activeTab.value])

// 追加操作列（固定右侧）
const columnsWithOp = computed<IColumn[]>(() => [
  ...currentColumns.value,
  { label: '操作', width: 120, fixed: 'right', align: 'center', slot: 'op' },
])

// 表格实例与分页配置
const tableRef = ref<InstanceType<typeof tableList> | null>(null)
const paginationProps = reactive<IPaginationProps>({
  pageSizes: [10, 20, 50, 100],
})

// Tab 切换
const onTabChange = (key: TabKey) => {
  if (activeTab.value === key) return
  activeTab.value = key
  tableRef.value?.getTableData(1)
}

// 查询
const handleSearch = () => {
  tableRef.value?.getTableData(1)
}

// 详情
const viewDetail = (row: Record<string, any>) => {
  visible.value = true
  console.log('查看详情', activeTab.value, row)
}

// 位置
const viewLocation = (row: Record<string, any>) => {
  console.log('查看详情', activeTab.value, row)
}

const fetchTable = async (pageModel: { pageNo: number; pageSize: number }) => {
  loading.value = true
  try {
    const res: any = await $API.post({
      url: '/fireSafety/getRealEventPage',
      data: {
        superviseId: userInfo.superviseId,
        unitId: route.query?.unitId,
        eventType: activeTab.value,
        pageNo: pageModel.pageNo,
        pageSize: pageModel.pageSize,
        deviceTypePid: (filters as any).systemType,
        deviceTypeId: (filters as any).deviceTypeId,
        buildingId: (filters as any).building,
        floorId: (filters as any).floorId,
        description: (filters as any).actionDesc,
      },
    })
    return res
  } finally {
    loading.value = false
  }
}

const getRealtimeAlarm = async () => {
  loading.value = true
  const res: any = await $API.post({
    url: '/fireSafety/getRealtimeAlarm',
    params: {
      superviseId: userInfo.superviseId,
      unitId: route.query?.unitId,
    },
  })
  counts['1'] = res.data.alarmNum || 0
  counts['3'] = res.data.faultNum || 0
  counts['4'] = res.data.hazardNum || 0
  counts['5'] = res.data.actionNum || 0
  loading.value = false
}
onMounted(() => {
  getRealtimeAlarm()
  tableRef.value?.getTableData(1)
})
</script>

<style scoped lang="scss">
.real-time-monitoring {
  height: 100%;
  padding: 0px 16px 0 16px;

  .status-tabs {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    justify-content: center;

    .tab-card {
      cursor: pointer;
      min-width: 160px;
      height: 72px;
      padding: 10px 12px;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      transition: all 0.2s ease;
      align-items: center;
      // opacity: 0.9;

      &.active {
        transform: translateY(-10px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);
        opacity: 1;
      }

      .title {
        font-weight: 600;
        font-size: 16px;
        color: #ffffff;
      }
      .count {
        margin-top: 6px;
        font-weight: 600;
        font-size: 22px;
        color: #ffffff;
      }
    }

    /* 色系（与设计稿相近渐变） */
    .c-red {
      background: linear-gradient(270deg, rgba(7, 141, 236, 1) 0%, rgba(46, 188, 240, 1) 100%);
    }
    .c-purple {
      background: linear-gradient(270deg, rgba(90, 46, 166, 0.7) 0%, rgba(35, 18, 63, 0.7) 100%);
    }
    .c-orange {
      background: linear-gradient(270deg, rgba(123, 87, 233, 1) 0%, rgba(171, 28, 254, 1) 100%);
    }
    .c-magenta {
      background: linear-gradient(270deg, rgba(171, 28, 254, 1) 0%, rgba(123, 87, 233, 1) 100%);
    }
    .c-blue {
      background: linear-gradient(90deg, rgba(7, 141, 236, 1) 0%, rgba(41, 102, 220, 1) 100%);
    }
    .c-cyan {
      background: linear-gradient(270deg, rgba(0, 173, 239, 0.7) 0%, rgba(6, 73, 99, 0.7) 100%);
    }
  }

  .filters {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 20px 0px 20px;

    :deep(.el-select) {
      width: 200px;
    }
  }

  .rt-table {
    width: 100%;
  }

  /* 由公共表格自带分页，这里保留空样式占位可删除 */
}
</style>
