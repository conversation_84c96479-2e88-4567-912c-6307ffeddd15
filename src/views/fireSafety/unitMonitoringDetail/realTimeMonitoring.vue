<template>
  <div class="real-time-monitoring">
    <!-- 顶部状态 Tab -->
    <div class="status-tabs">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-card', tab.key === activeTab ? 'active' : '', tab.color]"
        @click="onTabChange(tab.key)"
      >
        <div class="title">{{ tab.name }}</div>
        <div class="count">{{ counts[tab.key] ?? 0 }}</div>
      </div>
    </div>

    <!-- 条件筛选区 -->
    <div class="content" v-loading="loading">
      <div class="filters">
        <el-form :inline="true" label-width="auto">
          <el-form-item label="设备类型">
            <DeviceTypeCas v-model="filters.systemType" @change="handleSearch" />
          </el-form-item>
          <el-form-item label="楼栋楼层">
            <BuilidAndfloorCas v-model="filters.building" @change="handleSearch" />
          </el-form-item>
          <el-form-item label="动作描述">
            <el-input v-model="filters.actionDesc" placeholder="请选择" clearable @change="handleSearch" />
          </el-form-item>
          <el-form-item label="主机号">
            <el-input v-model="filters.location" placeholder="请输入" clearable @change="handleSearch" />
          </el-form-item>
        </el-form>
        <el-form-item>
          <QueryButton text="查询" @click="handleSearch" />
        </el-form-item>
      </div>

      <!-- 表格（公共组件） -->
      <table-list
        ref="tableRef"
        :columns="columnsWithOp"
        :api="fetchTable"
        :noPage="false"
        :pagination="paginationProps"
      >
        <template #operation="{ row }">
          <el-button link type="primary" @click="viewDetail(row)">详情</el-button>
          <el-button link type="primary" @click="viewLocation(row)">位置</el-button>
        </template>
      </table-list>
    </div>
  </div>
  <!-- 火警详情 -->
  <firePopup v-model="visible" v-if="visible" :event="activeTab" />
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import tableList, { IColumn, IPaginationProps } from '@/components/public/tableList/index.vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import DeviceTypeCas from '@/components/public/common-form/deviceTypeCas.vue'
import BuilidAndfloorCas from '@/components/public/common-form/builidAndfloorCas.vue'
import firePopup from '@/views/fireSafety/unitMonitoringDetail/components/firePopup.vue'
import QueryButton from '@/components/public/common-form/QueryButton.vue'

// 事件类型(1.火警 2.预警 3.故障 4.隐患 5.动作)
type TabKey = '1' | '3' | '4' | '5'

type ColumnDef = IColumn

// 顶部 Tab 配置
const tabs = ref<Array<{ key: TabKey; name: string; color: string }>>([
  { key: '1', name: '火警 (起)', color: 'c-red' },
  { key: '3', name: '故障 (起)', color: 'c-orange' },
  { key: '4', name: '隐患 (起)', color: 'c-magenta' },
  { key: '5', name: '动作 (起)', color: 'c-blue' },
])
const userInfo = useUserInfo().value
const route = useRoute()
const activeTab = ref<TabKey>('1')
const counts = reactive<Record<TabKey, number>>({
  '1': 0,
  '3': 0,
  '4': 0,
  '5': 0,
})
const visible = ref(false)
const loading = ref(false)
// 筛选项
const filters = reactive({
  systemType: '' as string | number,
  building: '' as string | number,
  location: '' as string | number,
  actionDesc: '' as string | number,
})

const columnMap: Record<TabKey, ColumnDef[]> = {
  '1': [
    { type: 'index', label: '序号', width: 80 },
    { prop: 'deviceTypePName', label: '系统类型', width: 160 },
    { prop: 'deviceTypeName', label: '设备类型' },
    { prop: 'deviceAddress', label: '设备位置', width: 240 },
    { prop: 'laMake', label: '主机回路点位', width: 160 },
    { prop: 'iotReceiveTime', label: '平台接收时间', width: 160 },
    { prop: 'firstEventTime', label: '设备上报时间', width: 160 },
  ],
  '3': [
    { type: 'index', label: '序号', width: 80 },
    { prop: 'deviceTypePName', label: '系统类型', width: 160 },
    { prop: 'deviceTypeName', label: '设备类型' },
    { prop: 'deviceAddress', label: '设备位置', width: 240 },
    { prop: 'description', label: '故障描述' },
    { prop: 'laMake', label: '主机回路点位', width: 160 },
    { prop: 'iotReceiveTime', label: '平台接收时间', width: 160 },
    { prop: 'firstEventTime', label: '设备上报时间', width: 160 },
  ],
  '4': [
    { type: 'index', label: '序号', width: 80 },
    { prop: 'deviceTypePName', label: '系统类型', width: 160 },
    { prop: 'deviceTypeName', label: '设备类型' },
    { prop: 'deviceAddress', label: '设备位置', width: 240 },
    { prop: 'deviceNum', label: 'IMEI', width: 160 },
    { prop: 'hdProblemCode', label: '隐患问题' },
    { prop: 'hazardType', label: '隐患分类' },
    { prop: 'className', label: '隐患来源' },
    { prop: 'firstEventTime', label: '平台接收时间', width: 160 },
  ],
  '5': [
    { type: 'index', label: '序号', width: 80 },
    { prop: 'deviceTypePName', label: '系统类型', width: 160 },
    { prop: 'deviceTypeName', label: '设备类型' },
    { prop: 'deviceAddress', label: '设备位置', width: 240 },
    { prop: 'description', label: '动作描述' },
    { prop: 'laMake', label: '主机回路点位', width: 160 },
    { prop: 'iotReceiveTime', label: '平台接收时间', width: 160 },
    { prop: 'firstEventTime', label: '设备上报时间', width: 160 },
  ],
}

const currentColumns = computed<ColumnDef[]>(() => columnMap[activeTab.value])

// 追加操作列（固定右侧）
const columnsWithOp = computed<IColumn[]>(() => [
  ...currentColumns.value,
  { label: '操作', width: 120, fixed: 'right', align: 'center', slot: 'operation' },
])

// 表格实例与分页配置
const tableRef = ref<InstanceType<typeof tableList> | null>(null)
const paginationProps = reactive<IPaginationProps>({
  pageSizes: [10, 20, 50, 100],
})

// Tab 切换
const onTabChange = (key: TabKey) => {
  if (activeTab.value === key) return
  activeTab.value = key
  tableRef.value?.getTableData(1)
}

// 查询
const handleSearch = () => {
  tableRef.value?.getTableData(1)
}

// 详情
const viewDetail = (row: Record<string, any>) => {
  visible.value = true
  console.log('查看详情', activeTab.value, row)
}

// 位置
const viewLocation = (row: Record<string, any>) => {
  console.log('查看详情', activeTab.value, row)
}

const fetchTable = async (pageModel: { pageNo: number; pageSize: number }) => {
  loading.value = true
  try {
    const res: any = await $API.post({
      url: '/fireSafety/getRealEventPage',
      data: {
        superviseId: userInfo.superviseId,
        unitId: route.query?.unitId,
        eventType: activeTab.value,
        pageNo: pageModel.pageNo,
        pageSize: pageModel.pageSize,
        deviceTypePid: (filters as any).systemType,
        deviceTypeId: (filters as any).deviceTypeId,
        buildingId: (filters as any).building,
        floorId: (filters as any).floorId,
        description: (filters as any).actionDesc,
      },
    })
    return res
  } finally {
    loading.value = false
  }
}

const getRealtimeAlarm = async () => {
  loading.value = true
  const res: any = await $API.post({
    url: '/fireSafety/getRealtimeAlarm',
    params: {
      superviseId: userInfo.superviseId,
      unitId: route.query?.unitId,
    },
  })
  counts['1'] = res.data.alarmNum || 0
  counts['3'] = res.data.faultNum || 0
  counts['4'] = res.data.hazardNum || 0
  counts['5'] = res.data.actionNum || 0
  loading.value = false
}
onMounted(() => {
  getRealtimeAlarm()
  tableRef.value?.getTableData(1)
})
</script>

<style scoped lang="scss">
.real-time-monitoring {
  height: calc(100vh - 260px);
  display: flex;
  flex-direction: column;
  padding: 0px 16px 0 16px;

  .status-tabs {
    display: flex;
    gap: 23px;
    margin-bottom: 30px;
    justify-content: center;

    .tab-card {
      cursor: pointer;
      min-width: 171px;
      height: 90px;
      padding: 10px 21px;
      /* 统一圆角，避免与下方重复声明冲突 */
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      transition: all 0.2s ease;
      align-items: flex-start;
      background: linear-gradient(180deg, rgba(24, 89, 161, 0.1405) 0%, #1456a2 100%);
      background-clip: padding-box;
      overflow: hidden;
      position: relative;
      border: none;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        padding: 1px;
        border-radius: inherit;
        background: linear-gradient(180deg, rgba(108, 166, 241, 1), rgba(88, 146, 216, 0.5));
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
      }

      &.active {
        background: linear-gradient(180deg, #078dec 0%, #2ebcf0 100%);
      }

      .title {
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }
      .count {
        margin-top: 6px;
        font-weight: 600;
        font-size: 28px;
        background: linear-gradient(180deg, #ffffff 0%, #7ec2ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
  .content {
    flex: 1;
    background: rgba(27, 63, 137, 0.2);
    border-radius: 0px 0px 0px 0px;
    .filters {
      display: flex;
      align-items: center;
      padding-top: 30px;
      justify-content: space-between;
      margin: 0 20px 0px 20px;

      :deep(.el-select) {
        width: 200px;
      }
    }
  }
}
</style>
