<template>
  <div class="securitySituation">
    <div class="stations">
      <div v-for="item in stations" :key="item.unitName" class="station">
        <div class="title" @click="handleClick(item)">
          <span>{{ item.unitName || '--' }}</span>
        </div>
        <div class="flex center-content">
          <div class="meta">
            <div class="row">
              <span class="label">位置：</span>
              <span class="val">{{ item.unitAddress || '--' }}</span>
            </div>
            <div class="row">
              <span class="label">联系人：</span>
              <span class="val">{{ item.contactsName || '--' }}</span>
              <span class="phone">{{ item.contactsTel || '--' }}</span>
            </div>
          </div>
          <div class="healthiness">
            <span>{{ item.score || '--' }}</span>
            <span class="label">消防安全指数</span>
          </div>
        </div>
        <div class="content">
          <div class="left">
            <div class="section-title">设备实时监测</div>
            <div class="info">
              <div class="item">
                <span>接入设备总数</span>
                <span>{{ item.deviceTotal || '--' }}</span>
              </div>
              <div class="item">
                <span>火灾报警设备</span>
                <span>{{ item.alarmDeviceNum || '--' }}</span>
              </div>
              <div class="item">
                <span>其他设备</span>
                <span>{{ item.ohterDeviceNum || '--' }}</span>
              </div>
            </div>
          </div>
        </div>
        <span class="current-warning-title">当前预警类型分布</span>
        <div class="current-warning">
          <template v-if="item.currentWarning.length > 0">
            <div v-for="it in item.currentWarning" :key="it.name" :class="['item', 't' + it.type]">
              <span>{{ it.name }} {{ it.count }}</span>
            </div>
          </template>
          <div class="emtry" v-else>
            <span>当前电站暂无预警</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

type WarningItem = {
  name: string
  count: number
  type: 1 | 2 | 3 | 4 // 1火警 2预警 3故障 4隐患
}

type Station = {
  unitId: string
  unitName: string
  unitAddress: string
  contactsName: string
  contactsTel: string
  score: number
  deviceTotal: number
  alarmDeviceNum: number
  ohterDeviceNum: number
  currentWarning: WarningItem[]
}
const userInfo = useUserInfo().value
const router = useRouter()
const stations = ref<Station[]>([
  {
    unitId: '1',
    unitName: '',
    unitAddress: '',
    contactsName: '',
    contactsTel: '',
    score: 0,
    deviceTotal: 0,
    alarmDeviceNum: 0,
    ohterDeviceNum: 0,
    currentWarning: [
      { name: '火警', count: 0, type: 1 },
      { name: '预警', count: 0, type: 2 },
      { name: '故障', count: 0, type: 3 },
      { name: '隐患', count: 0, type: 4 },
    ],
  },
])

const handleClick = (item) => {
  router.push({
    path: '/fireSafety/unitMonitoringDetail',
    query: {
      id: item.id,
      isShowBack: 'true',
      unitName: item.unitName,
      unitId: item.unitId,
    },
  })
}

async function getStations() {
  const res: any = await $API.post({
    url: '/fireSafety/getUnitMonitorList',
    params: {
      superviseId: userInfo.superviseId,
    },
  })
  stations.value = res.data || []
  stations.value.forEach((item: any) => {
    item.currentWarning = [
      { name: '火警', count: item.alarmNum, type: 1 },
      { name: '预警', count: item.warnNum, type: 2 },
      { name: '故障', count: item.faultNum, type: 3 },
      { name: '隐患', count: item.hazardNum, type: 4 },
    ]
  })
}
onMounted(() => {
  getStations()
})
</script>
<style scoped lang="scss">
.securitySituation {
  height: 100%;
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  > .stations {
    display: grid;
    grid-template-columns: repeat(4, minmax(320px, 1fr));
    grid-gap: 24px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    > .station {
      padding: 20px;
      background: linear-gradient(180deg, rgba(12, 57, 115, 0.4) 0%, rgba(5, 31, 64, 0.4) 100%);
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #0b4ea5;
      min-width: 450px;
      display: flex;
      flex-direction: column;
      > .title {
        cursor: pointer;
        display: flex;
        align-items: center;
        > span {
          font-weight: 600;
          font-size: 18px;
          color: #ffffff;
        }
      }
      .center-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        > .meta {
          .row {
            color: #9fbcd0;
            display: flex;
            align-items: center;
            font-size: 14px;
            margin-bottom: 6px;
          }
          .phone {
            margin-left: 10px;
          }
        }

        > .healthiness {
          width: 125px;
          height: 103px;
          background: url('@/assets/image/energyStorageSafety/healthiness-bg.png') no-repeat center;
          background-size: cover;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          > span {
            width: 100%;
            text-align: center;
          }
          > span:first-child {
            font-weight: 600;
            font-size: 26px;
            color: #ffffff;
            margin-bottom: 4px;
          }
          > span:last-child {
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
          }
        }
      }

      > .content {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        > .left {
          display: flex;
          flex-direction: column;
          gap: 10px;
          > .section-title {
            font-weight: 400;
            font-size: 14px;
            color: #e1e8f3;
          }
          > .info {
            display: flex;
            gap: 10px;
            justify-content: space-between;
            > .item {
              padding: 7px 10px;
              width: auto;
              height: 36px;
              background: linear-gradient(180deg, #164284 1%, #122d55 100%);
              border-radius: 2px 2px 2px 2px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              > span:first-child {
                font-weight: 400;
                font-size: 16px;
              }
              > span:last-child {
                margin-left: 2px;
                font-weight: 500;
                font-size: 20px;
              }
            }
          }
        }
      }
      > .current-warning-title {
        margin-bottom: 12px;
      }
      > .current-warning {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        > div {
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        > .item {
          width: 91px;
          height: 36px;
          border-radius: 888px;
          &.t1 {
            background: linear-gradient(180deg, #a20202 0%, #370505 100%);
            border: 1px solid rgba(255, 45, 45, 0.3);
          }
          &.t2 {
            background: linear-gradient(180deg, #5a2ea6 0%, #23123f 100%);
            border: 1px solid rgba(119, 74, 255, 0.3);
          }
          &.t3 {
            background: linear-gradient(180deg, #8f5300 0%, #352003 100%);
            border: 1px solid rgba(232, 140, 11, 0.3);
          }
          &.t4 {
            background: linear-gradient(180deg, #006c28 0%, #032f15 100%);
            border: 1px solid rgba(14, 181, 75, 0.5);
          }
        }
        > .emtry {
          width: 191px;
          height: 36px;
          background: linear-gradient(180deg, #006c28 0%, #032f15 100%);
          border-radius: 888px;
          border: 1px solid rgba(14, 181, 75, 0.5);
        }
      }
    }
  }
}
</style>
