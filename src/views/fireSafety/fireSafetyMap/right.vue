<template>
  <div class="w-full h-full flex flex-col mr-10px">
    <div class="health-analysis">
      <cardTtile title="报警次数排行"> </cardTtile>
    </div>
    <div class="flex-1 overflow-hidden">
      <div class="w-full h-full overflow-auto"></div>
    </div>
    <div class="waring-analysis">
      <cardTtile title="报警趋势分析"> </cardTtile>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted } from 'vue'
import cardTtile from '@/components/public/cardTtile.vue'

const init = async () => {}

onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.health-analysis {
  padding: 0px 16px 16px 16px;
  width: 450px;
  height: 385px;
  background: rgba(27, 63, 137, 0.2);
}
.waring-analysis {
  padding: 5px 16px 16px 16px;
  width: 450px;
  height: 385px;
  background: rgba(27, 63, 137, 0.2);
}
</style>
