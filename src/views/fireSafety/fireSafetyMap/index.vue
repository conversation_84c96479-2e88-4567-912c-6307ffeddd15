<template>
  <div class="w-full h-full relative">
    <div class="w-full h-full relative">
      <div class="top" v-loading="loading">
        <unitSelect class="mb-30px" style="width: 240px" v-model="unitId" @change="handleChange" />
        <div class="warning-cards">
          <div v-for="(item, index) in warningCards" :key="index" class="warning-card">
            <img
              :src="getImageUrl(`header${headerStartIndex + index}.png`)"
              class="w-full h-full absolute top-0 left-0 -z-10"
              alt="card background"
            />
            <div class="warning-content">
              <div class="waring-title">{{ item.title }}</div>
              <div class="flex items-center">
                <div class="waring-value mr-6px">{{ item.value }}</div>
                <div class="waring-unit">{{ item.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="center">
        <!-- <lms></lms> -->
      </div>
      <!-- 右侧抽屉区域 -->
      <div class="right">
        <button
          class="drawer-toggle"
          @click="rightDrawerOpen = !rightDrawerOpen"
          :style="rightDrawerOpen ? 'left:-35px;' : 'right:0; left:auto;'"
          aria-label="展开/收起右侧抽屉"
        >
          <span class="drawer-arrow"> <img src="@/assets/image/safeMap/rightPopup.png" class="w-50px h-50px" /></span>
        </button>
        <transition name="drawer-fade">
          <right v-show="rightDrawerOpen" />
        </transition>
      </div>
    </div>
  </div>
  <!-- 底部 Tab 菜单 -->
  <div class="footer-menu">
    <div class="tabs">
      <button
        v-for="(tab, idx) in tabs"
        :key="idx"
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        type="button"
        @click="onTabClick(tab)"
        :aria-pressed="activeTab === tab.key"
      >
        {{ tab.label }}
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import $API from '~/common/api'
import right from './right.vue'
import { useUserInfo } from '~/store'
import unitSelect from '@/components/public/common-form/unitSelect.vue'

const rightDrawerOpen = ref(true)
const userInfo = useUserInfo().value
const unitId = ref('')
// 底部 Tabs
const tabs = ref([
  { key: 'safe', label: '安全一张图' },
  { key: 'device', label: '设备一张图' },
  { key: 'monitor', label: '监测一张图' },
] as const)
const activeTab = ref<'safe' | 'device' | 'monitor'>('safe')

const cardConfigs: Record<
  'safe' | 'device' | 'monitor',
  Array<{ title: string; key: string; unit: string; value: string | number }>
> = {
  safe: [
    { title: '储能电站', key: 'unitCount', unit: '个', value: '0' },
    { title: '各电站电池舱数', key: 'batteryCompartmentCount', unit: '个', value: '0' },
    { title: '各电站接入面积', key: 'area', unit: '万m²', value: '0' },
  ],
  device: [
    { title: '各电站接入设备数', key: 'deviceCount', unit: '台', value: '0' },
    { title: '各电站火警设备数', key: 'fireDeviceCount', unit: '台', value: '0' },
    { title: '各电站其他设备数', key: 'otherDeviceCount', unit: '台', value: '0' },
  ],
  monitor: [
    { title: '各电站今日火警', key: 'todayFireCount', unit: '起', value: '0' },
    { title: '各电站本月火警', key: 'monthFireCount', unit: '起', value: '0' },
    { title: '各电站本年火警', key: 'yearDeviceCount', unit: '起', value: '0' },
  ],
}

const warningCards = ref(cardConfigs.safe.map((i) => ({ ...i })))
const loading = ref(false)
let debounceTimer: ReturnType<typeof setTimeout> | null = null

// 背景图起始序号：安全(1-3)、设备(4-6)、监测(7-9)
const headerStartIndex = computed(() => (activeTab.value === 'safe' ? 1 : activeTab.value === 'device' ? 4 : 7))

const getImageUrl = (name: string) => {
  return new URL(`/src/assets/image/safeMap/${name}`, import.meta.url).href
}

const endpointByTab: Record<'safe' | 'device' | 'monitor', string> = {
  safe: '/safety/dashboard/unit/statistics',
  device: '/device/dashboard/unit/statistics',
  monitor: '/monitor/dashboard/unit/statistics',
}

const getTopCards = async () => {
  const url = endpointByTab[activeTab.value]
  try {
    loading.value = true
    const { data } = await $API.get({
      url,
      params: {
        superviseId: userInfo.superviseId,
      },
    })
    warningCards.value = warningCards.value.map((item: any) => ({
      ...item,
      value: data?.[item.key] ?? 0,
    }))
  } catch (e) {
    console.error(e)
  } finally {
    loading.value = false
  }
}

const getTopCardsDebounced = () => {
  if (debounceTimer) clearTimeout(debounceTimer)
  debounceTimer = setTimeout(() => {
    getTopCards()
  }, 200)
}

const handleChange = (val: any) => {
  unitId.value = val.unitId
  getTopCardsDebounced()
}

const onTabClick = (tab: { key: 'safe' | 'device' | 'monitor'; label: string }) => {
  if (activeTab.value === tab.key) return
  activeTab.value = tab.key
  warningCards.value = cardConfigs[activeTab.value].map((i) => ({ ...i }))
  getTopCardsDebounced()
}

onMounted(() => {
  getTopCards()
})
</script>

<style lang="scss" scoped>
.footer-menu {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;

  .tabs {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .tab-item {
    height: 39px;
    min-width: 158px;
    padding: 0 16px;
    border-radius: 4px;
    border: 1px solid rgba(98, 142, 215, 0.6);
    color: #ffffff;
    font-size: 14px;
    line-height: 39px;
    background: linear-gradient(353deg, rgba(68, 130, 255, 0.3) 0%, rgba(25, 63, 140, 0) 100%);
    box-shadow: inset 0 0 0 1px rgba(25, 63, 140, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(2px);

    &:hover:not(.active) {
      filter: brightness(1.08);
    }

    &.active {
      min-width: 159px;
      background: linear-gradient(353deg, rgba(225, 169, 30, 0.3) 0%, rgba(225, 169, 30, 0) 100%);
      border-color: rgba(225, 169, 30, 0.6);
      box-shadow: inset 0 0 0 1px rgba(225, 169, 30, 0.25);
    }
  }
}
.left {
  position: absolute;
  left: 0;
  width: 450px;
  height: 100%;
  background: linear-gradient(-90deg, rgba(6, 17, 23, 0) 0%, #061117 50%);
  border-radius: 0px 0px 0px 0px;
}
.top {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  width: 100%;

  .warning-cards {
    position: relative;
    transition: opacity 0.2s ease;
  }

  .warning-card {
    position: relative;
    width: 188px;
    height: 98px;
    border-radius: 4px;
    overflow: hidden;
    padding: 0 20px;
    margin-bottom: 20px;

    .warning-content {
      position: relative;
      z-index: 1;
      color: white;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .waring-title,
      .waring-unit {
        height: 22px;
        font-size: 16px;
        color: #ffffff;
        line-height: 22px;
      }

      .waring-value {
        height: 39px;
        font-weight: 600;
        font-size: 28px;
        line-height: 39px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    &:nth-child(1) .waring-value {
      background-image: linear-gradient(180deg, #ffffff 0%, #7ec2ff 100%);
    }

    &:nth-child(2) .waring-value {
      background-image: linear-gradient(180deg, #ffffff 0%, #7ec2ff 100%);
    }

    &:nth-child(3) .waring-value {
      background-image: linear-gradient(180deg, #ffffff 0%, #b27eff 100%);
    }
  }
}
.center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
}
.right {
  position: absolute;
  right: 0;
  width: 450px;
  height: 100%;
  background: linear-gradient(90deg, rgba(6, 17, 23, 0) 0%, #061117 50%);
  border-radius: 0px 0px 0px 0px;
}
.drawer-toggle {
  position: absolute;
  top: 50%;
  z-index: 2;
  transform: translateY(-50%);
  cursor: pointer;
}
</style>
