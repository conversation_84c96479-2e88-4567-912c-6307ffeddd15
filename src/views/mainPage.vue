<template>
  <el-container class="h-full w-full main-page">
    <el-container>
      <el-header class="flex items-center p-0 el-header-im" v-show="!isAdmin">
        <header-component></header-component>
      </el-header>

      <breadcrumb-navigation v-if="!hiddenBread" :route="route" :router="router" :userInfo="ui" />

      <el-main :class="{ 'home-page': isHomepage, 'chart-page': isChartpage }" class="relative" style="padding-top: 0">
        <router-view v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </el-main>
    </el-container>
  </el-container>
  <!-- :popup-title="popupTitle || '实时监测'"  popup-title='实时监测' -->
  <popup-wrap popup-title="位置" v-model="showMaybeAlarm">
    <div class="w-1300px h-700px">
      <mapbe-alarm-map-view ref="maybeAlarmRef"></mapbe-alarm-map-view>
    </div>
  </popup-wrap>

  <!-- 位置详情 -->
  <popup-side popup-title="详情" v-model="showDeviceInfo">
    <device-info :device-info="currentDeviceInfo"></device-info>
  </popup-side>

  <!-- 真警弹窗 -->
  <popup-component v-model="showRealAlarmDialog">
    <real-alarm-dialog :message-data="realAlarmData" @close="showRealAlarmDialog = !showRealAlarmDialog">
    </real-alarm-dialog>
  </popup-component>

  <audio :src="audioPath" ref="audioRef"></audio>
</template>

<script lang="ts" setup>
import { createVNode, ref, nextTick, onUnmounted, onMounted, computed } from 'vue'
import headerComponent from '~/components/mainPage/headerComponent.vue'
import Notification from '~/components/message/notification.vue'
import notificationNotice from '~/components/message/notificationNotice.vue'

import { ElMessage, ElNotification } from 'element-plus'
import $API from '~/common/api'
import * as types from '~/common/types'
import { getDeviceInfoById, getEventRecordInfo } from '~/common/services'
import { useUserInfo, useCounterStore } from '@/store'
import SocketUtil from '@/common/socket'
import { useRoute, useRouter } from 'vue-router'

import PubSub from 'pubsub-js'
import _ from 'lodash'
import { EVENT_TYPE } from '~/common/eventType'
import realAlarmDialog from '~/components/message/realAlarmDialog.vue'
import mapbeAlarmMapView from '~/components/maybeAlarmMapView/index.vue'
import alarmAudio from '~/assets/audio/alarm.mp3'
import config from '~/config'
const counterStore = useCounterStore()
interface NotificationRecord {
  close: () => void
  add: (item: any) => void
}
const isAerialMapTemp: any = ref({})
const axldListData: any = ref({})
const notificationRecord: Record<string, NotificationRecord> = {}
const isCollapse = computed(() => counterStore.isCollapse)
const ui = useUserInfo()

const route = useRoute()
const router = useRouter()

const systemConfig = ref({
  notification_tone: '',
})

onMounted(() => {})

function getSysConf() {
  // $API
  //   .post({
  //     url: '/remindConf/findRemindConfig',
  //     params: {
  //       superviseUnitId: ui.value.orgCode,
  //     },
  //   })
  //   .then((res: any) => {
  //     if (res && res.code === 'success') {
  //       systemConfig.value.notification_tone = res.data.notificationTone || ''
  //     }
  //   })
}

const audioPath = computed(() => {
  const path = systemConfig.value.notification_tone
  console.log(config.base_host + path, 'audioPath')
  if (path) return config.base_host + path
  return alarmAudio
})

// 需要隐藏面包屑导航的页面
const hiddenBread = computed(() => {
  const pagePaths = [
    '/safety-management/safeMap',
    '/safety-management/safeScreen',
    '/safety-management/security_Map',
    '/safety-management/deviceMap',
    '/safety-management/deviceScreen',
    '/safety-management/device_map',
    '/fireRemoteManage/fireremoteManageUnitmap',
    '/fireRemoteManage/fireremoteManage-unitmap',
    '/overview',
    '/gisPage',
    '/statisticalQuery/fireHandle',
    '/statisticalQuery/fireSafetyReport',
    '/statisticalQuery/earlyWarningHandle',
    '/statisticalQuery/hiddenHandle',
    '/statisticalQuery/offLineHandle',
    '/equipmentInfrastructure/device-file/index',
    '/fireIntelligent-judgment/runDiagnosticsInfo/diagnosticRecords',
    '/fireSafetySituation/fireSafetySituationMap',
    // '/fireIntelligent-judgment/riskAssessment/customModel',
    '/fireDataCenter/dataAnalysis/dataCenterMap',
    '/fireSafetySituation/fireSafetySituationAnalysis',
  ]
  return pagePaths.includes(route.path)
})

const isAdmin = computed(() => route.meta.isAdmin)

const floorViewRef = ref()

const showFloorView = ref(false)
const popupTitle = ref('')

const showNineView = ref(false)

const nineMapViewRef = ref()

const showDeviceInfo = ref(false)

const showMessage = ref(false)

const currentDeviceInfo = ref({} as any)

const showRealAlarmDialog = ref(false)

const realAlarmData = ref()

const showMaybeAlarm = ref(false)
const showAxldFlg = ref(false)
const gisModalVisible = ref<boolean>(false)
const maybeAlarmRef = ref()
const isHomepage = computed(() => route.path === '/home-page')
const isChartpage = computed(() => ['/safety-management/safeMap'].includes(route.path))

const socket = new SocketUtil({
  topic: '/topic/' + ui.value.orgCode,
  headers: {
    userId: ui.value._userId,
    userName: ui.value.userName,
    terminal: 'web',
  },
})
socket.onmessage = handleMessage

const NO_POINT_MESSAGE = '暂无点位信息,无法展示'
const mapTypeTemp: any = ref()
PubSub.subscribe(types.OPEN_FLOOR_VIEW, async (msg: string, data: any) => {
  if (!data.deviceId) {
    return ElMessage.warning(NO_POINT_MESSAGE)
  }
  const typeTemp = data.ownerType || data.unitType || ''
  const di = await getDeviceInfoById(data.deviceId, typeTemp)
  console.log(di, '****di')
  console.error('data', data)

  mapTypeTemp.value = di.floorMapType
  if (!di) {
    return ElMessage.warning(NO_POINT_MESSAGE)
  }
  if (di.status == '1') return ElMessage.warning('该设备已经停用')
  data.isAerialMap = di.isAerialMap
  data.floorMapType = di.floorMapType
  data.aerialMapType = di.aerialMapType
  isAerialMapTemp.value = data
  const isNine = (di._isNine = di.ownerType !== 0)
  console.log(ui, 'ui')

  const noFloorAreaImg = di.floorAreaImg === '' && data.floorMapType !== 1

  if (noFloorAreaImg && (!isNine || (isNine && di.buildingId !== '' && di.floorId !== '')))
    return ElMessage.warning('单位尚未采集图纸信息,无法展示')
  if (isNine && noFloorAreaImg) {
    return handleOpenFloorView('', {
      x: di.unitPointX,
      y: di.unitPointY,
    })
  }

  if (isEmptyCoordinate(di)) {
    return ElMessage.warning(NO_POINT_MESSAGE)
  }
  di.evetType = data.eventType + ''
  di.priorityEventType = data.eventType + ''
  di.unitType = data.ownerType || data.unitType
  console.log(data.eventType)
  console.log(di)
  const ids = di.videoId.split(',').filter((item) => !!item)
  const promised: Promise<any>[] = []
  for (let i = 0; i < ids.length; i++) {
    promised.push(getDeviceInfoById(ids[i]))
  }
  popupTitle.value = (di.buildingName || '') + (di.floorName || '') + (di.deviceAddress || '')
  showFloorView.value = true

  Promise.all(promised).then((values) => {
    console.log('🚀 ~ Promise.all ~  floorViewRef.value:', floorViewRef.value)
    nextTick(() => {
      setTimeout(() => floorViewRef.value?.showFloor([di].concat(values)), 500)
    })
  })
})

PubSub.subscribe(types.OPEN_NICE_FLOOR_VIEW, (msg: string, { x, y, title }) => {
  if (!x || !y) {
    ElMessage.warning('尚未采集平面图信息')
  } else {
    showNineView.value = true
    nextTick(() => {
      const indoor = nineMapViewRef.value.getIndoor()
      indoor.showOVDataBuild([{ x, y }], 'x', 'y')
      indoor.setCenter([x, y])
    })
  }
})
function handleOpenFloorView(msg: string, { x, y, title = '' }) {
  if (!x || !y) {
    ElMessage.warning('暂无点位信息,无法展示')
  } else {
    showNineView.value = true
    nextTick(() => {
      const indoor = nineMapViewRef.value.getIndoor()
      indoor.showOVDataBuild([{ x, y }], 'x', 'y')
      indoor.setCenter([x, y])
    })
  }
}

PubSub.subscribe(types.OPEN_MAYBE_ALARM_FLORR_VIEW, async (msg: string, data) => {
  console.log(data, '高度疑似真警')

  // ************** 第0项就是首警
  let first = data.children[0]
  if (!first?.deviceId) return ElMessage.warning(NO_POINT_MESSAGE)
  const typeTemp = first.ownerType || first.unitType || ''
  const di = await getDeviceInfoById(first.deviceId, typeTemp)
  mapTypeTemp.value = di.floorMapType
  if (!di) return ElMessage.warning(NO_POINT_MESSAGE)
  first.isAerialMap = di.isAerialMap
  first.floorMapType = di.floorMapType
  first.aerialMapType = di.aerialMapType
  isAerialMapTemp.value = data

  const isNine = (di._isNine = di.ownerType !== 0)

  const noFloorAreaImg = di.floorAreaImg === '' && first.floorMapType !== 1

  if (noFloorAreaImg && (!isNine || (isNine && di.buildingId !== '' && di.floorId !== '')))
    return ElMessage.warning('单位尚未采集图纸信息,无法展示')

  showMaybeAlarm.value = true

  if (isEmptyCoordinate(di)) {
    ElMessage.warning(NO_POINT_MESSAGE)
  }

  nextTick(() => {
    maybeAlarmRef.value.showFloor(data)
  })
})

PubSub.subscribe(types.OPEN_DEVICE_DETAIL, async (msg: string, deviceInfo) => {
  currentDeviceInfo.value = deviceInfo
  console.log('OPEN_DEVICE_DETAIL-----------', deviceInfo)
  showDeviceInfo.value = true
})

PubSub.subscribe(types.OPEN_MESSAGE_LIST, () => {
  showMessage.value = true
})

//sta  安消联动相关
PubSub.subscribe(types.OPEN_AXLD_FLORR_VIEW, async (msg: string, data) => {
  if (!data.deviceId) {
    return ElMessage.warning(NO_POINT_MESSAGE)
  }
  const typeTemp = data.ownerType || data.unitType || ''
  const di = await getDeviceInfoById(data.deviceId, '0')
  mapTypeTemp.value = di.floorMapType
  data.floorMapType = di.floorMapType
  if (!di) {
    return ElMessage.warning(NO_POINT_MESSAGE)
  }
  const isNine = (di._isNine = di.ownerType !== 0)
  const noFloorAreaImg = di.floorAreaImg === '' && data.floorMapType != 1

  if (noFloorAreaImg && (!isNine || (isNine && di.buildingId !== '' && di.floorId !== '')))
    return ElMessage.warning('单位尚未采集图纸信息,无法展示')
  if (isNine && noFloorAreaImg) {
    return handleOpenFloorView('', {
      x: di.unitPointX,
      y: di.unitPointY,
    })
  }

  if (isEmptyCoordinate(di)) {
    return ElMessage.warning(NO_POINT_MESSAGE)
  }

  data.isAerialMap = di.isAerialMap
  data.floorMapType = di.floorMapType
  data.aerialMapType = di.aerialMapType
  axldListData.value = Object.assign(data, di)
  showAxldFlg.value = true
  gisModalVisible.value = true
})

function isEmptyCoordinate(item: {
  mapX: string
  mapY: string
  longitude: string
  latitude: string
  floorMapType: string | number
}) {
  if (item.floorMapType == 1) {
    return item.mapX == '' && item.mapY == ''
  }
  return item.latitude == '' && item.longitude == ''
}

const MAP_SIZE = 16

const computeOffHeight = _.debounce(() => {
  const els: NodeListOf<HTMLElement> = document.querySelectorAll('.gs-notification')
  Array.from(els).reduce((count, el, index) => {
    const total = count + (els[index - 1]?.offsetHeight || 0) + MAP_SIZE
    el.style.bottom = total + 'px'
    return total
  }, 0)
}, 500)

const setStatus = (data) => {
  // 一张图实时警情状态改变
  PubSub.publish(types.ALARM_STATUS, data)
}
const unitMapRefresh = () => {
  PubSub.publish('unitMapRefresh')
}
const safeMapListRefresh = () => {
  PubSub.publish(types.ALARM_STATUS, {})
}

const audioRef = ref()
async function handleMessage(body: any) {
  console.log('---+++++++++9999999', '0', body)
  const messageType = body.messageType.toString()
  if (!ui.value || Object.keys(ui.value).length === 0) return
  if (body && body.messageType && body.messageType == 200) {
    setStatus(body)
    return
  }
  unitMapRefresh()
  if (body && body.messageContent == 'trigger_fourcolor_refresh') return // 西藏四色图推送消息隐形刷新页面的标识
  if (messageType == 102 || messageType == 106 || messageType == 111) {
    counterStore.messageNum++
  }
  const isNotice = messageType == EVENT_TYPE.Notification_Notice

  nextTick(() => {
    // 用户不操作页面,chrome浏览器是禁止直接播放消息提示音的
    try {
      if (!isNotice) audioRef.value && audioRef.value.play()
    } catch (error) {}
  })

  if (messageType == 1 || messageType == 2 || messageType == 3 || messageType == 4) {
    safeMapListRefresh()
  }

  if (messageType == EVENT_TYPE.REAL_ALARM) {
    // console.log(EVENT_TYPE.REAL_ALARM)
    getEventRecordInfo({
      disposeId: body.businessId,
      eventType: EVENT_TYPE.ALARM,
    }).then(async (data: any) => {
      const di = await getDeviceInfoById(data.deviceId, '')
      data.isAerialMap = di.isAerialMap
      data.floorMapType = di.floorMapType
      data.aerialMapType = di.aerialMapType
      body.unitPointX = JSON.parse(body.unitPointX)
      body.unitPointY = JSON.parse(body.unitPointY)
      realAlarmData.value = {
        ...data,
        ...body,
      }
      showRealAlarmDialog.value = true
    })
  } else {
    if (messageType == EVENT_TYPE.WARING) {
      await getEventRecordInfo({
        disposeId: body.businessId,
        eventType: EVENT_TYPE.WARING,
      }).then((data: any) => {
        body.eventDesc = data.eventDesc
        body.warningRank = data.warningRank
      })
    }

    const props = {
      messageType,
      onClose: () => {
        let target: any = notificationRecord[messageType]
        try {
          if (target && audioRef.value) {
            audioRef.value.pause()
            target.close()
          }
        } catch (error) {
          console.log(error)
        }
        delete notificationRecord[messageType]
        target = null
      },
      notificationRecord,
    }
    let target = notificationRecord[messageType]
    if (!target) {
      target = notificationRecord[messageType] = {} as NotificationRecord

      const { close } = ElNotification({
        showClose: false,
        message: createVNode(isNotice ? notificationNotice : Notification, props),
        zIndex: 900,
        duration: 0,
        appendTo: '.main-page',
        position: 'bottom-right',
        customClass: isNotice ? 'notification-notice gs-notification' : 'gs-notification',
      }) as any
      target.close = close
    }

    await nextTick()
    target.add(body)
    await nextTick()

    computeOffHeight()
  }
}

// const test1 = {
//   businessId: '1677815010492944386',
//   createTime: 1689858005467,
//   eventSource: '31',
//   itemType: 2,
//   messageContent: '锦绣社区存在的隐患|其它隐患超过20天未整改，请快速响应处置，保障单位消防安全。',
//   messageId: '27710f65593f4646a296977e80f947d4',
//   messageTitle: '故障超期提醒',
//   messageType: 111,
//   monitorReceiveTime: '2023-07-09 07:00:45',
//   subCenterCode: '340104YYZX1589790745437405184',
//   superviseId: '1613835399167315970',
//   unitId: '340100DW1589793038035582976',
// }

// setTimeout(() => {
//   handleMessage(test1)
// }, 3000)

onMounted(() => {
  let link = document.querySelector('link[rel*="icon"]')
  document.title = ui.value?.systemMidName || '智慧消防系统'
  getSysConf()
})
onUnmounted(() => {
  socket.distory()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.alarm-unitName-box {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  background: #26a0fb;
  border-radius: 0 0 8px 8px;
  padding: 10px;
  color: #fff;
  z-index: 99;
}

.main-page {
  background-color: #111111;
  min-height: 800px;
  .el-aside {
    // width: 260px !important;
    --el-aside-width: auto;
    transition: all 0.3s;
  }

  .el-header-im {
    padding: 0 !important;
    height: 48px !important;
  }
}

.monitor-view {
  .popup-wrap {
    // margin-left: 220px;
  }
}

.home-page {
  background: white;
}

.chart-page {
  padding: 0 !important;
  overflow: hidden !important;
}

.message-side {
  .content {
    padding-right: 0 !important;
  }
}
</style>
