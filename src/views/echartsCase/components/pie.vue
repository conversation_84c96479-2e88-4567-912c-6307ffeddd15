<template>
  <div ref="chartRef" :style="{ width, height }"></div>
</template>
<script lang="ts" setup>
import { Ref, ref, watch } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import { isEmpty } from '@/utils/is'
import type { EChartsOption } from 'echarts'
import { data, sumValues } from './data'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
defineProps({
  width: {
    type: String,
    default: '700px',
  },
  height: {
    type: String,
    default: '300px',
  },
})

const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>)

watch(
  () => data,
  () => {
    setOptions(
      {
        tooltip: {
          trigger: 'item',
        },
        title: {
          text: '{name|' + '总和' + '}\n{val|' + sumValues(data) + '}',
          top: 'center',
          left: 'center',
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: 'normal',
                color: '#666666',
              },
              val: {
                fontSize: 16,
                fontWeight: 'bold',
                color: '#333333',
              },
            },
          },
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          align: 'right',
          right: '0%',
          top: 'middle',
          textStyle: {
            color: '#8C8C8C',
          },
          height: 150,
        },
        backgroundColor: 'rgba(0, 0, 0, 0)',
        series: [
          {
            name: '成交占比',
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['40%', '65%'],
            color: ['#5ab1ef', '#b6a2de', '#67e0e3', '#2ec2c9', '#ff4b0f', '#ff9702', '#ff7200', '#ffb400'],
            data: data,
            // roseType: 'radius',
            animationType: 'scale',
            label: {
              show: true,
              position: 'outside',
              formatter: (params) => {
                return `{font|}${params.name}:${params.percent}% \n{hr|}\n{font|}`
              },
              rich: {
                font: {
                  fontSize: 14,
                  padding: [5, 0],
                  color: '#333333',
                },
                hr: {
                  height: 0,
                  borderWidth: 1,
                  width: '100%',
                  borderColor: '#00D7E9',
                },
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0,0,0,0.5)',
              },
            },
          },
        ],
      } as EChartsOption,
      { isEmpty: isEmpty(data) }
    )
  },
  { immediate: true }
)
</script>
