<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts" setup>
import { ref, Ref, onMounted } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import { getLineData } from './data'

defineProps({
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: '340px',
  },
})

const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions, echarts } = useECharts(chartRef as Ref<HTMLDivElement>)
const { barData, lineData, category } = getLineData

onMounted(() => {
  setOptions({
    backgroundColor: '#0f375f',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          show: true,
          backgroundColor: '#333',
        },
      },
    },
    legend: {
      data: ['折线', '柱状'],
      textStyle: {
        color: '#ccc',
      },
    },
    xAxis: {
      data: category,
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
    },
    yAxis: {
      splitLine: { show: false },
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
    },
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: [0, 1],
        bottom: 20,
        height: 20,
        left: 'center',
      },
    ],
    series: [
      {
        name: '折线',
        type: 'line',
        smooth: true,
        showAllSymbol: 'auto',
        symbol: 'emptyCircle',
        symbolSize: 15,
        data: lineData,
      },
      {
        name: '柱状',
        type: 'bar',
        barWidth: 10,
        itemStyle: {
          borderRadius: 5,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#14c8d4' },
            { offset: 1, color: '#43eec6' },
          ]),
        },
        data: barData,
      },
    ],
  })
})
</script>
