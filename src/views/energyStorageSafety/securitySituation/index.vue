<template>
  <div class="securitySituation">
    <div class="total">
      <div v-for="item in total" :key="item.name" class="item">
        <span>{{ item.name }}</span>
        <span>{{ item.value }}</span>
      </div>
    </div>
    <div v-if="stations.length > 0" class="stations" v-loading="loading2">
      <div v-for="item in stations" :key="item.title" class="station">
        <div class="title">
          <img v-if="item.currentWarning.length > 0" src="@/assets/image/energyStorageSafety/warning.png" alt="" />
          <span>{{ item.title }}</span>
        </div>
        <div class="content">
          <div class="info">
            <div v-for="it in item.info" :key="it.name" class="item">
              <span>{{ it.name }}</span>
              <span>{{ it.value }}</span>
            </div>
          </div>
          <div class="healthiness">
            <span>{{ item.healthiness }}</span>
            <span>全站健康度</span>
          </div>
        </div>
        <span class="current-warning-title">当前预警类型分布</span>
        <div class="current-warning">
          <template v-if="item.currentWarning.length > 0">
            <div v-for="it in item.currentWarning" :key="it.name" :class="['item', 't' + it.type]">
              <span>{{ it.name }}</span>
            </div>
          </template>
          <div class="emtry" v-else>
            <span>当前电站暂无预警</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="flex items-center justify-center w-full h-full">
      <img src="@/assets/image/energyStorageSafety/emtry.png" alt="" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'
const userInfo = useUserInfo()

interface IStation {
  title: string
  healthiness: string
  currentWarning: {
    name: '一级预警' | '二级预警' | '三级预警'
    type: 1 | 2 | 3
  }[]
  info: {
    name: '电池仓' | '电池簇' | '当前预警' | '历史预警' | '监测设备'
    value: number
  }[]
}
const total = ref([
  { name: '储电站总数(个)', value: 0, key: 'storageStationNum' },
  { name: '电池仓总数(个)', value: 0, key: 'batteryNum' },
  { name: '监测设备数(个)', value: 0, key: 'deviceNum' },
  { name: '当前预警(起)', value: 0, key: 'warningNum' },
  { name: '历史预警(起)', value: 0, key: 'warningHistoryNum' },
])
const stations = ref<IStation[]>([])

const postData = ref({
  unitId: userInfo.value.unitId,
  superviseId: userInfo.value.orgCode,
})
const loading1 = ref(false)
const getOverview = async () => {
  loading1.value = true
  try {
    const res = await $API.post({ url: '/safetySituation/overview', data: postData.value })
    if ((res as any).code === 'success') {
      total.value = total.value.map((v) => {
        return {
          ...v,
          value: res.data[v.key] || 0,
        }
      })
    }
  } finally {
    loading1.value = false
  }
}
const loading2 = ref(false)
const getStationSafetySituation = async () => {
  loading2.value = true
  try {
    const res = await $API.post({ url: '/safetySituation/stationSafetySituation', data: postData.value })
    if ((res as any).code === 'success') {
      res.data.map((v) => {
        const obj: IStation = {
          title: v.unitName,
          healthiness: v.storageStationNum,
          currentWarning: [],
          info: [
            { name: '电池仓', value: v.batteryCompartmentNum },
            { name: '电池簇', value: v.batteryClusterNum },
            { name: '当前预警', value: v.warningNum },
            { name: '监测设备', value: v.deviceNum },
          ],
        }
        if (v.firstLevelWarningNum > 0) {
          obj.currentWarning.push({
            name: '一级预警',
            type: 1,
          })
        }
        if (v.secondLevelWarningNum > 0) {
          obj.currentWarning.push({
            name: '二级预警',
            type: 2,
          })
        }
        if (v.thirdLevelWarningNum > 0) {
          obj.currentWarning.push({
            name: '三级预警',
            type: 3,
          })
        }
        stations.value.push(obj)
      })
    }
  } finally {
    loading2.value = false
  }
}
onMounted(() => {
  getOverview()
  getStationSafetySituation()
})
</script>
<style scoped lang="scss">
.securitySituation {
  height: 100%;
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  > .total {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    > .item {
      padding: 18px;
      width: 346px;
      height: 54px;
      background: url('@/assets/image/energyStorageSafety/total-bg.png') no-repeat center;
      background-size: cover;
      display: flex;
      justify-content: space-between;
      align-items: center;
      > span:first-child {
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }
      > span:last-child {
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
      }
    }
  }
  > .stations {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 24px;
    > .station {
      padding: 20px;
      background: linear-gradient(180deg, rgba(12, 57, 115, 0.4) 0%, rgba(5, 31, 64, 0.4) 100%);
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #0b4ea5;
      > .title {
        display: flex;
        align-items: center;
        margin-bottom: 18px;
        > img {
          margin-right: 12px;
        }
        > span {
          font-weight: 600;
          font-size: 18px;
          color: #ffffff;
        }
      }
      > .content {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        > .info {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          grid-gap: 12px;
          > .item {
            padding: 8px 16px;
            width: 138px;
            height: 36px;
            background: linear-gradient(180deg, #164284 1%, #122d55 100%);
            border-radius: 2px 2px 2px 2px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            > span:first-child {
              font-weight: 400;
              font-size: 16px;
              color: #ffffff;
            }
            > span:last-child {
              font-weight: 500;
              font-size: 20px;
              color: #ffffff;
            }
          }
        }
        > .healthiness {
          width: 103px;
          height: 90px;
          background: url('@/assets/image/energyStorageSafety/healthiness-bg.png') no-repeat center;
          background-size: cover;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          > span {
            width: 100%;
            text-align: center;
          }
          > span:first-child {
            font-weight: 600;
            font-size: 26px;
            color: #ffffff;
            margin-bottom: 4px;
          }
          > span:last-child {
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
          }
        }
      }
      > .current-warning-title {
        font-weight: 400;
        font-size: 14px;
        color: #e1e8f3;
        margin-bottom: 12px;
        display: inline-block;
      }
      > .current-warning {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 13px;
        > div {
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        > .item {
          width: 128px;
          height: 36px;
          border-radius: 888px;
          &.t1 {
            background: linear-gradient(180deg, #a20202 0%, #370505 100%);
            border: 1px solid rgba(184, 10, 10, 0.3);
          }
          &.t2 {
            background: linear-gradient(180deg, #ff5500 0%, #7e2a00 100%);
            border: 1px solid rgba(161, 51, 4, 0.3);
          }
          &.t3 {
            background: linear-gradient(180deg, #ffdd00 0%, #615400 99%);
            border: 1px solid rgba(146, 85, 0, 0.3);
          }
        }
        > .emtry {
          width: 191px;
          height: 36px;
          background: linear-gradient(180deg, #006c28 0%, #032f15 100%);
          border-radius: 888px;
          border: 1px solid rgba(14, 181, 75, 0.5);
        }
      }
    }
  }
}
</style>
