<template>
  <popupSideBlack v-model="visible" :popup-title="'生成健康体检报告'">
    <div class="body">
      <div class="query">
        <div>
          <el-radio-group v-model="query.reportType">
            <el-radio-button label="日报" />
            <el-radio-button label="月报" />
            <el-radio-button label="季报" />
            <el-radio-button label="年报" />
          </el-radio-group>
        </div>
        <div>
          <el-select
            class="!w-full big-screen-select"
            v-model="query.energyStorageStationId"
            placeholder="全部储能电站"
          >
            <el-option label="电站1" value="1" />
          </el-select>
        </div>
        <div>
          <el-date-picker
            class="!w-full big-screen-select"
            v-model="query.analysisTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期"
          />
        </div>
      </div>
      <div class="btn">
        <div>确定生成报告</div>
      </div>
    </div>
  </popupSideBlack>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue'
import popupSideBlack from '~/components/public/popup/popupSideBlack.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:modelValue'])
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const query = ref({
  reportType: '日报',
  energyStorageStationId: '',
  analysisTime: '',
})
</script>
<style lang="scss" scoped>
.body {
  display: flex;
  flex-direction: column;
  height: 100%;
  > .query {
    height: 100%;
    overflow-y: auto;
    margin-bottom: 20px;
    > div {
      display: flex;
      justify-content: center;
      &:first-child {
        margin-bottom: 24px;
      }
      margin-bottom: 16px;
    }
  }
  > .btn {
    flex-shrink: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    > div {
      cursor: pointer;
      padding: 6px 25px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      background: linear-gradient(180deg, rgba(10, 73, 155, 0.85) 2%, rgba(3, 31, 73, 0.85) 100%);
      border: 1px solid #115ed1;
    }
  }
}

:deep(.el-radio-group) {
  .el-radio-button {
    .el-radio-button__inner {
      background: rgba(69, 125, 238, 0.2);
      border-color: #244378;
      font-size: 14px;
      color: #ffffff;
    }
    &.is-active {
      .el-radio-button__inner {
        background: rgba(25, 79, 185, 0.75);
        border-color: rgba(51, 119, 255, 0.75);
      }
    }
    &:first-child {
      .el-radio-button__inner {
        border-radius: 16px 0px 0px 16px;
      }
    }
    &:last-child {
      .el-radio-button__inner {
        border-radius: 0px 16px 16px 0px;
      }
    }
  }
}
</style>
