<template>
  <div class="report">
    <div class="instructions">
      <div class="title-bg">
        <div class="title-name">评估说明</div>
      </div>
      <div class="text">
        <p>1. 本报告仅供项目所有者、管理方或指定方使用，禁止向任何未获授权的第三方转交或引用。</p>
        <p>
          2.
          本报告的技术分析依赖于项目提供的有限数据信息，其质量决定了分析结果的准确性、客观性和完整性；同时，本报告的使用者应充分理解报告的局
          限性，不得对报告内容进行曲解或滥用。
        </p>
        <p>
          3.
          本报告的分析、建议及结论基于项目提供的数据，仅反映被分析储能电站的过往状况。未来运行状况可能受环境、设备老化、管理等因素影响，故本
          报告不对储能电站未来安全性、稳定性及运行风险提供保证。
        </p>
        <p>
          4.
          本报告评估单位不对评估后可能发生的设备故障、人为操作失误、自然灾害等导致的安全事故承担责任，亦不承担项目后续运营、维护或管理中的任
          何责任。项目所有者或运营方应负责和保障项目的日常维护、监控及安全管理。
        </p>
      </div>
      <div class="checked">
        <el-checkbox>评估说明已知晓</el-checkbox>
        <div @click="generatePopupVisible = true">去生成健康体检报告</div>
      </div>
    </div>
    <div class="table-wrap">
      <div class="tabs">
        <div :class="['tab-item', { active: tab === 'health' }]" @click="tab = 'health'">健康体检报告</div>
        <div :class="['tab-item', { active: tab === 'warning' }]" @click="tab = 'warning'">预警诊断报告</div>
      </div>
      <div v-if="tab === 'health'" class="query">
        <div>
          <el-select
            class="!w-240px big-screen-select"
            v-model="query.energyStorageStationId"
            placeholder="全部储能电站"
          >
            <el-option label="电站1" value="1" />
          </el-select>
        </div>
        <div>
          <el-radio-group v-model="query.reportType">
            <el-radio-button label="日报" />
            <el-radio-button label="月报" />
            <el-radio-button label="季报" />
            <el-radio-button label="年报" />
          </el-radio-group>
        </div>
        <div>
          <el-date-picker
            class="!w-240px big-screen-select"
            v-model="query.analysisTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期"
          />
        </div>
      </div>
      <div v-if="tab === 'warning'" class="query">
        <el-form :inline="true" :model="searchForm" label-width="0">
          <el-form-item v-for="item in searchFormItems" :key="item.prop" class="!mt-0 !mb-0">
            <component :is="item.component" v-bind="item" />
          </el-form-item>
        </el-form>
      </div>
      <div class="table">
        <tableList
          class="!p-0"
          :data="tab === 'health' ? tableData : tableData2"
          :columns="tab === 'health' ? columns : columns2"
          :no-page="false"
        >
          <template #opts>
            <div class="opts-btn">
              <span>预览</span>
            </div>
          </template>
        </tableList>
      </div>
      <generatePopup v-model="generatePopupVisible"></generatePopup>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import tableList from '~/components/public/tableList/index.vue'
import generatePopup from './components/generatePopup.vue'

const tab = ref('health')
const generatePopupVisible = ref(false)
const query = ref({
  reportType: '日报',
  energyStorageStationId: '',
  analysisTime: '',
})
const searchForm = ref({
  energyStorageStationId: '',
  batteryCabin: '',
  batteryCluster: '',
  warningDesc: '',
  warningTime: '',
})
const searchFormItems = [
  {
    label: '电站',
    prop: 'energyStorageStationId',
    component: 'ElSelect',
    options: [
      {
        label: '电站1',
        value: '1',
      },
    ],
    placeholder: '请选择电池站',
    class: '!w-240px big-screen-select',
  },
  {
    label: '电池舱',
    prop: 'batteryCabin',
    component: 'ElSelect',
    options: [
      {
        label: '电池舱1',
        value: '1',
      },
    ],
    placeholder: '请选择电池舱',
    class: '!w-240px big-screen-select',
  },
  {
    label: '电池簇',
    prop: 'batteryCluster',
    component: 'ElSelect',
    options: [
      {
        label: '电池簇1',
        value: '1',
      },
    ],
    placeholder: '请选择电池簇',
    class: '!w-240px big-screen-select',
  },
  {
    label: '预警内容',
    prop: 'warningDesc',
    component: 'ElSelect',
    options: [
      {
        label: '预警内容1',
        value: '1',
      },
    ],
    placeholder: '请选择预警内容',
    class: '!w-240px big-screen-select',
  },
  {
    label: '预警时间',
    prop: 'warningTime',
    component: 'ElDatePicker',
    type: 'date',
    placeholder: '请选择预警时间',
    class: '!w-240px big-screen-select',
  },
]
const columns = [
  { prop: 'index', label: '序号', width: 60, align: 'center' },
  { prop: 'fileName', label: '文件名称' },
  { prop: 'target', label: '体检对象' },
  { prop: 'reportType', label: '报告类型' },
  { prop: 'analysisTime', label: '分析时间' },
  { prop: 'productionTime', label: '生产时间' },
  { prop: 'generator', label: '生成人员' },
  { prop: 'operation', label: '操作', slot: 'opts', width: 100 },
]
const tableData = [
  {
    index: 1,
    fileName: '健康体检报告-日报-20250715',
    target: '全部储能电站',
    reportType: '日报',
    analysisTime: '2025-07-25',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
  {
    index: 2,
    fileName: '健康体检报告-周报-20250712-20250719',
    target: '全部储能电站',
    reportType: '周报',
    analysisTime: '2025-07-12——2025-07-19',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
  {
    index: 3,
    fileName: '健康体检报告-季报-2025-03',
    target: 'xxxxxxx电站',
    reportType: '季报',
    analysisTime: '2025-07-25',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
  {
    index: 4,
    fileName: '健康体检报告-年报-2025',
    target: '全部储能电站',
    reportType: '年报',
    analysisTime: '2025-07-12——2025-07-19',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
  {
    index: 5,
    fileName: '健康体检报告-日报-20250715',
    target: '全部储能电站',
    reportType: '日报',
    analysisTime: '2025-07-25',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
  {
    index: 6,
    fileName: '健康体检报告-周报-20250712-20250719',
    target: 'xxxxxxx电站',
    reportType: '周报',
    analysisTime: '2025-07-12——2025-07-19',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
  {
    index: 7,
    fileName: '健康体检报告-季报-2025-03',
    target: '全部储能电站',
    reportType: '季报',
    analysisTime: '2025-07-25',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
  {
    index: 8,
    fileName: '健康体检报告-年报-2025',
    target: '全部储能电站',
    reportType: '年报',
    analysisTime: '2025-07-12——2025-07-19',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
  {
    index: 9,
    fileName: '健康体检报告-日报-20250715',
    target: 'xxxxxxx电站',
    reportType: '日报',
    analysisTime: '2025-07-25',
    productionTime: '2025-07-25 17:52:12',
    generator: '张三',
  },
]

const columns2 = [
  { prop: 'serial_number', label: '序号', width: 60, align: 'center' },
  { prop: 'early_warning_diagnosis_equipment', label: '预警诊断设备' },
  { prop: 'early_warning_content', label: '预警内容' },
  { prop: 'early_warning_time', label: '预警时间' },
  { prop: 'diagnosis_time', label: '诊断时间' },
  { prop: 'occurring_institution', label: '发生机构' },
  { prop: 'diagnosis_personnel', label: '诊断人员', width: 140 },
  { prop: 'operation', label: '操作', slot: 'opts', width: 100 },
]
const tableData2 = [
  {
    serial_number: 1,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 17:52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
  {
    serial_number: 2,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 17:52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
  {
    serial_number: 3,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 17:52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
  {
    serial_number: 4,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 17:52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
  {
    serial_number: 5,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 17:52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
  {
    serial_number: 6,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 极52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
  {
    serial_number: 7,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 17:52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
  {
    serial_number: 8,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 17:52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
  {
    serial_number: 9,
    early_warning_diagnosis_equipment: '3-2#电池舱1#电池阵列2#电池簇',
    early_warning_content: '电池簇-三级热失控预警',
    early_warning_time: '2025-07-25 17:52:12',
    diagnosis_time: '2025-07-25 17:52:12',
    occurring_institution: 'XXXXX电站',
    diagnosis_personnel: '张三',
    operation: '预览',
  },
]
</script>
<style scoped lang="scss">
.report {
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  display: flex;
  height: 100%;
  > .instructions {
    width: 420px;
    flex-shrink: 0;
    margin-right: 20px;
    padding-left: 16px;
    padding-right: 16px;
    overflow-y: auto;
    background-color: rgba(27, 63, 137, 0.2);
    > .text {
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      line-height: 32px;
      > p {
        margin-bottom: 40px;
      }
    }
    > .checked {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-right: 40px;
      > div {
        margin-top: 12px;
        background: linear-gradient(180deg, rgba(6, 81, 178, 0.85) 2%, rgba(1, 36, 88, 0.85) 100%);
        border: 1px solid #236ddc;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        padding: 8px 24px;
        cursor: pointer;
      }
    }
  }
  > .table-wrap {
    padding: 20px;
    width: 100%;
    background: rgba(27, 63, 137, 0.2);
    > .tabs {
      display: flex;
      > .tab-item {
        font-size: 16px;
        color: #ffffff;
        padding: 7px 24px;
        cursor: pointer;
        background: linear-gradient(180deg, rgba(31, 58, 100, 0.75) 3%, rgba(18, 33, 58, 0.75) 100%);
        border-radius: 0px 2px 2px 0px;
        border: 1px solid #244378;
        &.active {
          background: linear-gradient(180deg, rgba(10, 73, 155, 0.85) 2%, rgba(3, 31, 73, 0.85) 100%);
          border-radius: 2px 0px 0px 2px;
          border: 1px solid #115ed1;
        }
      }
    }
    > .query {
      display: flex;
      margin: 20px 0 16px 0;
      > div {
        margin-right: 48px;
      }
    }
    > .table {
      width: 100%;
      height: calc(100% - 80px);
    }
  }
  .title-bg {
    height: 48px;
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    align-items: center;
    background-image: url('@/assets/image/card-title-bg.png');
    background-size: auto 100%;
    background-repeat: no-repeat;
    margin-bottom: 20px;
    > .title-name {
      padding-left: 48px;
      font-weight: bold;
      font-size: 24px;
      color: #ffffff;
      text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
      font-style: italic;
    }
  }
}
.opts-btn {
  display: flex;
  > span {
    font-weight: 400;
    font-size: 14px;
    color: #00a1ff;
    margin-right: 20px;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
  }
}
:deep(.el-radio-group) {
  .el-radio-button {
    .el-radio-button__inner {
      background: rgba(69, 125, 238, 0.2);
      border-color: #244378;
      font-size: 14px;
      color: #ffffff;
    }
    &.is-active {
      .el-radio-button__inner {
        background: rgba(25, 79, 185, 0.75);
        border-color: rgba(51, 119, 255, 0.75);
      }
    }
    &:first-child {
      .el-radio-button__inner {
        border-radius: 16px 0px 0px 16px;
      }
    }
    &:last-child {
      .el-radio-button__inner {
        border-radius: 0px 16px 16px 0px;
      }
    }
  }
}
</style>
