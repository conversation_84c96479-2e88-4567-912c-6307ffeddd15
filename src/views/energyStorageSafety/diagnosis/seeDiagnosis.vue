<template>
  <div class="see-diagnosis">
    <div class="btns">
      <span>生成诊断报告</span>
      <span @click="handleBack">返回</span>
    </div>
    <div class="content">
      <div class="instructions">
        <div class="title-bg">
          <div class="title-name">评估说明</div>
        </div>
        <div class="text">
          <p>1. 本报告仅供项目所有者、管理方或指定方使用，禁止向任何未获授权的第三方转交或引用。</p>
          <p>
            2.
            本报告的技术分析依赖于项目提供的有限数据信息，其质量决定了分析结果的准确性、客观性和完整性；同时，本报告的使用者应充分理解报告的局
            限性，不得对报告内容进行曲解或滥用。
          </p>
          <p>
            3.
            本报告的分析、建议及结论基于项目提供的数据，仅反映被分析储能电站的过往状况。未来运行状况可能受环境、设备老化、管理等因素影响，故本
            报告不对储能电站未来安全性、稳定性及运行风险提供保证。
          </p>
          <p>
            4.
            本报告评估单位不对评估后可能发生的设备故障、人为操作失误、自然灾害等导致的安全事故承担责任，亦不承担项目后续运营、维护或管理中的任
            何责任。项目所有者或运营方应负责和保障项目的日常维护、监控及安全管理。
          </p>
        </div>
      </div>
      <div class="waring-wrap">
        <div class="msg">
          <div class="title-bg">
            <div class="title-name">预警信息</div>
            <div class="type">电池异常 - 热失控预警</div>
          </div>
          <div class="device">
            <div class="device-name">3-2#电池舱1#电池阵列2#电池簇</div>
            <div class="time">2025-07-06 20:22:20</div>
          </div>
          <div class="message">
            <div class="message-name">电池簇-三级热失控预警</div>
            <div class="message-btns">
              <span>预警</span>
              <span>动作</span>
            </div>
          </div>
        </div>
        <div class="chart">
          <div class="title-bg">
            <div class="title-name">温度分析</div>
          </div>
          <div class="w-full h-full">
            <Temperature2></Temperature2>
          </div>
        </div>
        <div class="ai-wrap">
          <div class="title-bg">
            <div class="title-name">大模型诊断</div>
          </div>
          <div class="text">
            好，我需要根据用户提供的信息完成储能电站的故障诊断报告。首先，我要仔细阅读并理解所有给定的数据和要求。
            <br />
            用户给出的时间是2025年7月6日20:22:20.588，地点在3-2#电池舱1#电池阵列2#电池簇，发生的是电池簇三级热失控预警。算法诊断怀疑可能的原因是热失控预警，并指出故障出现的单体位置为4，所在pack为1。相关数据包括报警原因：“热失控，请进一步排查具体原因”，以及单体温度最大温升速率达到1℃/s。历史相关故障数据暂时没有提供。根据用户的要求，我需要按照示例格式来完成诊断报告，分为“预警分析”、“故障诊断”和“检修建议”三个部分，并且不能编造数据，只能基于已知信息进行分析。在“预警分析”中，我应该描述发生的时间、地点以及触发的预警类型。接着，在“故障诊断”里，需要说明根据算法诊断的结果，怀疑是热失控，并指出具体单体和pack的位置，同时引用温度数据支持判断。最后，“检修建议”部分应提出具体的检查和处理步骤。需要注意的是，用户特别强调不要编造数据，所以我要确保所有内容都是基于提供的信息，不添加任何假设或推测。现在，我会按照这些思路来组织报告的内容，确保逻辑清晰、结构合理，并且符合用户的格式要求。
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Temperature2 from './components/Temperature2.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const handleBack = () => {
  router.go(-1)
}
</script>
<style scoped lang="scss">
.see-diagnosis {
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  height: 100%;
  > .btns {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    > span {
      padding: 6px 30px;
      background: linear-gradient(180deg, rgba(10, 73, 155, 0.85) 2%, rgba(3, 31, 73, 0.85) 100%);
      border: 1px solid #115ed1;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      margin-left: 12px;
      margin-bottom: 16px;
      cursor: pointer;
    }
  }
  > .content {
    display: flex;
    > .instructions {
      width: 420px;
      flex-shrink: 0;
      margin-right: 20px;
      padding-left: 16px;
      padding-right: 16px;
      background-color: rgba(27, 63, 137, 0.2);
      > .text {
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        line-height: 32px;
        > p {
          margin-bottom: 40px;
        }
      }
    }
    > .waring-wrap {
      width: 100%;
      > .msg {
        height: 158px;
        background-color: rgba(27, 63, 137, 0.2);
        margin-bottom: 30px;
        padding-left: 16px;
        > .device {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-right: 20px;
          margin-bottom: 20px;
          > div {
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
          }
        }
        > .message {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-right: 20px;
          margin-bottom: 24px;
          > div {
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
            &.message-name {
              color: #ff990c;
            }
            &.message-btns {
              > span {
                font-weight: 400;
                font-size: 16px;
                color: #ffffff;
                cursor: pointer;
              }
              > span:first-child {
                margin-right: 20px;
                display: inline-block;
                color: #ff990c;
              }
            }
          }
        }
      }
      > .chart {
        height: 410px;
        background-color: rgba(27, 63, 137, 0.2);
        margin-bottom: 30px;
        display: flex;
        flex-direction: column;
      }
      > .ai-wrap {
        padding-left: 16px;
        height: 318px;
        background: url('@/assets/image/energyStorageSafety/ai-1-bg.png') no-repeat center;
        background-color: rgba(27, 63, 137, 0.2);
        background-size: 100% 100%;
        overflow: hidden;
        > .text {
          padding-bottom: 16px;
          padding-right: 24px;
          height: 100%;
          overflow: auto;
          word-wrap: break-word;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 32px;
        }
      }
    }
    .title-bg {
      height: 48px;
      display: flex;
      flex-shrink: 0;
      justify-content: space-between;
      align-items: center;
      background-image: url('@/assets/image/card-title-bg.png');
      background-size: auto 100%;
      background-repeat: no-repeat;
      margin-bottom: 20px;
      > .title-name {
        padding-left: 48px;
        font-weight: bold;
        font-size: 24px;
        color: #ffffff;
        text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
        font-style: italic;
      }

      > .type {
        margin-right: 20px;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        background: linear-gradient(180deg, #a20202 0%, #370505 100%);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid rgba(184, 10, 10, 0.3);
        padding: 4px 16px;
      }
    }
  }
}
</style>
