<template>
  <popupSideBlack v-model="visible" :popup-title="'检修反馈'">
    <div class="body">
      <div class="item">
        <div class="desc">
          <span>2025-02-12 12:12:12</span>
          <span>反馈人：张三</span>
        </div>
        <div class="msg">反馈内容：已全部检修完毕</div>
      </div>
      <div class="btn">
        <el-input type="textarea" v-model="msg" placeholder="请输入检修反馈情况" :rows="6" />
        <img src="@/assets/image/energyStorageSafety/popup-submit.png" alt="" />
      </div>
    </div>
  </popupSideBlack>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue'
import popupSideBlack from '~/components/public/popup/popupSideBlack.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:modelValue'])
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const msg = ref('')
</script>
<style lang="scss" scoped>
.body {
  display: flex;
  flex-direction: column;
  height: 100%;
  > .item {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    height: calc(100% - 160px);
    overflow-y: auto;
    > .desc {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      > span {
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }
    }
    > .msg {
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
    }
  }
  > .btn {
    flex-shrink: 0;
    width: 100%;
    position: relative;
    img {
      position: absolute;
      bottom: 20px;
      right: 20px;
      cursor: pointer;
    }
  }
}
</style>
