<template>
  <table-list :columns="columns" :data="props.data" :bottom-height="'0px'">
    <template #opts="{ row }">
      <div class="opts-btn">
        <span v-if="row.resetStatus === '未复位'" @click="handleGetDiagnosis(row)">去诊断</span>
        <span v-if="row.resetStatus === '已复位'" @click="handleSeeDiagnosis(row)">查看诊断报告</span>
        <span v-if="row.resetStatus === '已复位'" @click="handleMaintenance()">检修反馈</span>
      </div>
    </template>
  </table-list>
  <maintenancePopup v-model="visible" />
</template>
<script setup lang="ts">
import tableList, { IColumn, ITableRow } from '@/components/public/tableList/index.vue'
import { PropType, ref } from 'vue'
import { useRouter } from 'vue-router'
import maintenancePopup from './maintenancePopup.vue'

const visible = ref(false)
const router = useRouter()
const props = defineProps({
  data: {
    type: Array as PropType<ITableRow[]>,
    default: () => [],
  },
})
const columns: IColumn[] = [
  {
    label: '序号',
    width: 60,
    align: 'center',
    formatter: (row: any, column: IColumn, cellValue: any, index: number) => {
      return index + 1 + '）'
    },
  },
  {
    prop: 'deviceName',
    label: '设备名称',
  },
  {
    prop: 'warningDesc',
    label: '预警描述',
  },
  {
    prop: 'warningLevel',
    label: '预警等级',
    width: 160,
  },
  {
    prop: 'warningTime',
    label: '预警时间',
    width: 160,
  },
  {
    prop: 'resetStatus',
    label: '复位状态',
    width: 160,
  },
  {
    prop: 'opts',
    label: '操作',
    width: 220,
    slot: 'opts',
  },
  {
    prop: '',
    label: '',
    width: 90,
  },
]

const handleGetDiagnosis = (row: any) => {
  router.push({
    path: '/energyStorageSafety/diagnosis/getDiagnosis',
    query: {
      id: row.id,
    },
  })
}
const handleSeeDiagnosis = (row: any) => {
  router.push({
    path: '/energyStorageSafety/diagnosis/seeDiagnosis',
    query: {
      id: row.id,
    },
  })
}
const handleMaintenance = () => {
  visible.value = true
}
</script>
<style scoped lang="scss">
.custom-table-wrap {
  padding: 0;
}
:deep(.el-table__cell) {
  background-color: rgba(51, 99, 155, 0.12) !important;
}
:deep(.el-scrollbar__bar) {
  display: none;
}
.opts-btn {
  display: flex;
  > span {
    font-weight: 400;
    font-size: 14px;
    color: #00a1ff;
    margin-right: 20px;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
