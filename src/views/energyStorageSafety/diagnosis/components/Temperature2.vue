<template>
  <div :class="containerClass">
    <div ref="chartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import * as echarts from 'echarts/core'
import { ECharts } from 'echarts/core'

// 定义组件接受的 props
interface Props {
  width?: string
  height?: string
  className?: string
}

// 定义 props
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  className: '',
})

// 使用 props 设置容器类名
const containerClass = computed(() => {
  const classes = ['w-full h-full']
  if (props.className) {
    classes.push(props.className)
  }
  return classes.join(' ')
})

// 图表引用
const chartRef = ref<HTMLElement | null>(null)
const { setOptions } = useECharts(chartRef as any)

// X轴数据（6个时间段）
const xAxisData = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']

// 告警趋势数据
const trendData = {
  line3Data: [18, 20, 23, 35, 41, 31],
  line4Data: [16, 20, 23, 35, 41, 31],
  line5Data: [1, 0.3, 0.6, 1, 1.3, 1.6],
}

// 初始化图表
const initChart = () => {
  setOptions({
    backgroundColor: 'transparent',
    toolbox: {
      show: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      padding: [8, 12],
    },
    legend: {
      data: ['簇单体温度极差', '温差限值', '簇电流'],
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 14,
      },
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 20,
      top: 0,
      left: 'center',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '°C',
        min: 10,
        interval: 10,
        axisLabel: {
          formatter: '{value}',
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
          },
        },
      },
      {
        type: 'value',
        name: 'A',
        min: 0,
        interval: 0.3,
        axisLabel: {
          formatter: '{value}',
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '温差限值',
        type: 'line',
        yAxisIndex: 0,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: '#FF5500',
        },
        itemStyle: {
          color: '#FFC233',
          borderColor: '#fff',
          borderWidth: 1,
        },
        data: trendData.line3Data,
      },
      {
        name: '簇单体温度极差',
        type: 'line',
        yAxisIndex: 0,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: '#00BFBF',
        },
        itemStyle: {
          color: '#FFC233',
          borderColor: '#fff',
          borderWidth: 1,
        },
        data: trendData.line4Data,
      },
      {
        name: '簇电流',
        type: 'line',
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: '#FDA000',
        },
        itemStyle: {
          color: '#FFC233',
          borderColor: '#fff',
          borderWidth: 1,
        },
        data: trendData.line5Data,
      },
    ],
  })
}

// 监听窗口大小变化
const handleResize = () => {
  if (chartRef.value) {
    const instance = (echarts as any).getInstanceByDom(chartRef.value as HTMLElement) as ECharts | undefined
    if (instance) {
      instance.resize()
    }
  }
}

// 组件挂载时初始化图表
onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  initChart,
})
</script>
