<template>
  <div class="diagnosis">
    <div class="query">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item v-for="item in searchFormItems" :key="item.prop" :label="item.label">
          <component :is="item.component" v-bind="item" />
        </el-form-item>
      </el-form>
      <div class="query-btn">查询</div>
    </div>
    <div class="table">
      <table-list :columns="columns" :data="data" :pagination="pagination" :no-page="false">
        <template #expand="{ row }">
          <expandTable :data="row.expand"></expandTable>
        </template>
      </table-list>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import tableList, { IColumn, IPaginationProps } from '@/components/public/tableList/index.vue'
import expandTable from './components/expand-table.vue'

const searchForm = ref({})
const searchFormItems = [
  {
    label: '所属机构',
    prop: 'orgId',
    component: 'ElSelect',
    options: [
      {
        label: '机构1',
        value: '1',
      },
    ],
    placeholder: '请选择',
    class: '!w-240px big-screen-select',
  },
  {
    label: '电池舱',
    prop: 'batteryCabin',
    component: 'ElSelect',
    options: [
      {
        label: '电池舱1',
        value: '1',
      },
    ],
    placeholder: '请选择',
    class: '!w-240px big-screen-select',
  },
  {
    label: '预警时间',
    prop: 'warningTime',
    component: 'ElDatePicker',
    type: 'date',
    placeholder: '请选择',
    class: '!w-240px big-screen-select',
  },
  {
    label: '已复位预警',
    prop: 'resetStatus',
    component: 'ElSelect',
    options: [
      {
        label: '已复位',
        value: '1',
      },
    ],
    placeholder: '请选择',
    class: '!w-240px big-screen-select',
  },
]
const columns: IColumn[] = [
  {
    type: 'index',
    label: '序号',
    width: 60,
    align: 'center',
  },
  {
    prop: 'deviceName',
    label: '设备名称',
  },
  {
    prop: 'warningDesc',
    label: '预警描述',
  },
  {
    prop: 'warningLevel',
    label: '预警等级',
    width: 160,
  },
  {
    prop: 'warningCount',
    label: '预警次数',
    width: 160,
  },
  {
    prop: 'resetCount',
    label: '已复位预警',
    width: 160,
  },
  {
    prop: 'diagnoseCount',
    label: '诊断次数',
    width: 220,
  },
  {
    type: 'expand',
    slot: 'expand',
    width: 90,
  },
]
const data = [
  {
    deviceName: '3-2#电池舱1#电池阵列2#电池簇',
    warningDesc: '电池簇-三级热失控预警',
    warningLevel: '一级',
    warningCount: '2',
    resetCount: '2',
    diagnoseCount: '2',
    expand: [
      {
        deviceName: '3-2#电池舱1#电池阵列2#电池簇',
        warningDesc: '电池簇-三级热失控预警',
        warningLevel: '一级',
        warningTime: '2025-07-12 14:23:12',
        resetStatus: '未复位',
      },
      {
        deviceName: '3-2#电池舱1#电池阵列2#电池簇',
        warningDesc: '电池簇-三级热失控预警',
        warningLevel: '一级',
        warningTime: '2025-07-12 14:23:12',
        resetStatus: '已复位',
      },
    ],
  },
  {
    deviceName: '3-2#电池舱1#电池阵列2#电池簇',
    warningDesc: '电池簇-三级热失控预警',
    warningLevel: '一级',
    warningCount: '2',
    resetCount: '2',
    diagnoseCount: '2',
    expand: [
      {
        deviceName: '3-2#电池舱1#电池阵列2#电池簇',
        warningDesc: '电池簇-三级热失控预警',
        warningLevel: '一级',
        warningTime: '2025-07-12 14:23:12',
        resetStatus: '未复位',
      },
      {
        deviceName: '3-2#电池舱1#电池阵列2#电池簇',
        warningDesc: '电池簇-三级热失控预警',
        warningLevel: '一级',
        warningTime: '2025-07-12 14:23:12',
        resetStatus: '已复位',
      },
    ],
  },
  {
    deviceName: '3-2#电池舱1#电池阵列2#电池簇',
    warningDesc: '电池簇-三级热失控预警',
    warningLevel: '一级',
    warningCount: '2',
    resetCount: '2',
    diagnoseCount: '2',
    expand: [
      {
        deviceName: '3-2#电池舱1#电池阵列2#电池簇',
        warningDesc: '电池簇-三级热失控预警',
        warningLevel: '一级',
        warningTime: '2025-07-12 14:23:12',
        resetStatus: '未复位',
      },
      {
        deviceName: '3-2#电池舱1#电池阵列2#电池簇',
        warningDesc: '电池簇-三级热失控预警',
        warningLevel: '一级',
        warningTime: '2025-07-12 14:23:12',
        resetStatus: '已复位',
      },
    ],
  },
]
const pagination = ref<IPaginationProps>({
  total: 800,
})
</script>
<style scoped lang="scss">
.diagnosis {
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  height: 100%;
  > .query {
    padding: 0 20px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    > .query-btn {
      background: url('@/assets/image/energyStorageSafety/btn-bg.png') no-repeat center;
      background-size: 100% 100%;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      padding: 6px 22px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  > .table {
    height: 100%;
  }
}
:deep(.el-table__expanded-cell) {
  padding: 0;
}
</style>
