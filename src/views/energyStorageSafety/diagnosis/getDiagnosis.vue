<template>
  <div class="get-diagnosis">
    <div class="btns">
      <span>生成诊断报告</span>
      <span @click="handleBack">返回</span>
    </div>
    <div class="warning-list">
      <div class="warning-item" v-for="item in warningList" :key="item.title">
        <div class="title">
          <div class="title-name">{{ item.title }}</div>
          <div class="type">{{ item.type }}</div>
        </div>
        <div class="device">
          <div class="device-name">{{ item.device }}</div>
          <div class="time">{{ item.time }}</div>
        </div>
        <div class="message">
          <div class="message-name">{{ item.message }}</div>
          <div class="message-btns">
            <span>预警</span>
            <span>动作</span>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="charts">
        <div class="title">
          <div class="title-name">温度分析</div>
          <el-form :inline="true" :model="searchForm" class="demo-form-inline" :label-width="0">
            <el-form-item v-for="item in searchFormItems" :key="item.prop" class="!mb-0 !ml-20px !mr-0">
              <component :is="item.component" v-bind="item" />
            </el-form-item>
          </el-form>
        </div>
        <div class="chart">
          <div class="chart-title">
            <div class="title-name">故障诊断-温度分析-单体温度曲线</div>
            <img src="@/assets/image/energyStorageSafety/down-icon.png" alt="" />
          </div>
          <div class="p-x-16px w-full h-full">
            <Temperature></Temperature>
          </div>
        </div>
        <div class="chart">
          <div class="chart-title">
            <div class="title-name">故障诊断-温度分析-单体温度曲线</div>
            <img src="@/assets/image/energyStorageSafety/down-icon.png" alt="" />
          </div>
          <div class="p-x-16px w-full h-full">
            <Temperature2></Temperature2>
          </div>
        </div>
      </div>
      <div class="ai-message">
        <div class="ai-1">
          <div class="title">大模型诊断</div>
          <div class="text">
            好，我需要根据用户提供的信息完成储能电站的故障诊断报告。首先，我要仔细阅读并理解所有给定的数据和要求。
            <br />
            用户给出的时间是2025年7月6日20:22:20.588，地点在3-2#电池舱1#电池阵列2#电池簇，发生的是电池簇三级热失控预警。算法诊断怀疑可能的原因是热失控预警，并指出故障出现的单体位置为4，所在pack为1。
            <br />
            相关数据包括报警原因：“热失控，请进一步排查具体原因”，以及单体温度最大温升速率达到1℃/s。历史相关故障数据暂时没有提供。
            <br />
            根据用户的要求，我需要按照示例格式来完成诊断报告，分为“预警分析”、“故障诊断”和“检修建议”三个部分，并且不能编造数据，只能基于已知信息进行分析。
            <br />
            在“预警分析”中，我应该描述发生的时间、地点以及触发的预警类型。接着，在“故障诊断”里，需要说明根据算法诊断的结果，怀疑是热失控，并指出具体单体和pack的位置，同时引用温度数据支持判断。最后，“检修建议”部分应提出具体的检查和处理步骤。
            <br />
            需要注意的是，用户特别强调不要编造数据，所以我要确保所有内容都是基于提供的信息，不添加任何假设或推测。
            <br />
            现在，我会按照这些思路来组织报告的内容，确保逻辑清晰、结构合理，并且符合用户的格式要求。
          </div>
        </div>
        <div class="ai-2">
          <div class="title">大模型建议</div>
          <div class="text">
            【预警分析】<br />
            2025年7月6日20:22:20.588，3-2#电池舱1#电池阵列2#电池簇触发了三级热失控预警。此次预警提示可能存在严重的热失控风险。
            <br />
            【故障诊断】<br />
            经过算法诊断，怀疑热失控的原因可能与电池单体的异常温升有关。具体故障出现在单体位置4，所在的pack为1。相关数据显示，单体温度的最大温升速率达到1℃/s，这表明该单体可能存在过热现象或内部化学反应加剧的情况，可能导致热失控的发生。
            <br />
            【建议】<br />
            立即停止运行：为了避免潜在的安全风险，应立即将3-2#电池舱的1#电池阵列和相关设备停机。
            <br />
            现场检查：
            <br />
            使用红外热像仪对故障单体及其邻近区域进行温度分布检测。<br />
            检查单体4及相关pack是否存在机械损伤、电解液泄漏等异常现象。<br />
            数据分析：<br />
            调取单体4最近24小时的温度、电压和电流数据，分析其变化趋势，寻找可能的异常点。<br />
            对比历史运行数据，评估该单体的工作状态是否稳定。<br />
            系统排查：<br />
            检查电池管理系统（BMS）的功能，确保温度传感器正常工作且数据采集准确。<br />
            确认冷却系统运行正常，检查是否有局部散热不良的情况。<br />
            安全处理：<br />
            如果发现单体存在严重故障或无法修复的损伤，应将其安全隔离并更换为备用电池模块。<br />
            在进一步操作前，确保所有人员配备适当的防护装备，并采取必要的安全措施。<br />
            后续监测：<br />
            对整个电池舱的运行状态进行持续监控，尤其关注其他单体的温度和温升速率。<br />
            建立定期检查机制，防止类似事件再次发生。<br />
            优化策略：<br />
            根据此次故障原因，评估现有热失控预警算法的有效性，并考虑调整阈值或增加更多检测维度（如气体传感器）以提高系统安全性。<br />
            优化电池管理系统中的均衡控制策略，减少单体间差异，降低热失控风险。<br />
            应急预案：<br />
            确保所有相关人员熟悉热失控应急处理流程，定期进行演练。<br />
            配置足够的灭火设备和防护装备，特别是在储能电站的关键区域。<br />
            通过以上措施，可以有效排查并解决此次热失控预警问题，同时提升整个储能系统运行的安全性和可靠性。<br />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import Temperature from './components/Temperature.vue'
import Temperature2 from './components/Temperature2.vue'

const router = useRouter()
const handleBack = () => {
  router.go(-1)
}

const searchForm = ref({})
const searchFormItems = [
  {
    prop: 'warningTime',
    component: 'ElDatePicker',
    type: 'daterange',
    valueFormat: 'yyyy-MM-dd',
    startPlaceholder: '开始日期',
    endPlaceholder: '结束日期',
    class: '!w-240px big-screen-select',
  },
  {
    prop: 'resetStatus',
    component: 'ElSelect',
    options: [
      {
        label: '3#电池簇',
        value: '1',
      },
    ],
    placeholder: '请选择',
    class: '!w-240px big-screen-select',
  },
]

const warningList = [
  {
    title: '预警1',
    type: '电池异常-热失控预警',
    time: '2025-07-06 20:00:00',
    device: '3-3-2#电池舱1#电池阵列2#电池簇',
    message: '电池簇-三级热失控预警',
  },
  {
    title: '预警2',
    type: '电池异常-热失控预警',
    time: '2025-07-06 20:00:00',
    device: '3-3-2#电池舱1#电池阵列2#电池簇',
    message: '电池簇-三级热失控预警',
  },
  // {
  //   title: '预警3',
  //   type: '电池异常-热失控预警',
  //   time: '2025-07-06 20:00:00',
  //   device: '3-3-2#电池舱1#电池阵列2#电池簇',
  //   message: '电池簇-三级热失控预警',
  // },
]
</script>
<style scoped lang="scss">
.get-diagnosis {
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  height: 100%;
  > .btns {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    > span {
      padding: 6px 30px;
      background: linear-gradient(180deg, rgba(10, 73, 155, 0.85) 2%, rgba(3, 31, 73, 0.85) 100%);
      border: 1px solid #115ed1;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      margin-left: 12px;
      margin-bottom: 16px;
      cursor: pointer;
    }
  }
  > .warning-list {
    flex-shrink: 0;
    display: flex;
    overflow: auto;
    > .warning-item {
      margin-bottom: 12px;
      padding-left: 16px;
      flex-shrink: 0;
      width: calc(50% - 10px);
      margin-right: 20px;
      background: rgba(27, 63, 137, 0.2);
      &:last-child {
        margin-right: 0;
      }
      > .title {
        height: 48px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-image: url('@/assets/image/card-title-bg.png');
        background-size: auto 100%;
        background-repeat: no-repeat;
        margin-bottom: 20px;
        > .title-name {
          padding-left: 48px;
          font-weight: bold;
          font-size: 24px;
          color: #ffffff;
          text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
          font-style: italic;
        }
        > .type {
          margin-right: 20px;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          background: linear-gradient(180deg, #a20202 0%, #370505 100%);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid rgba(184, 10, 10, 0.3);
          padding: 4px 16px;
        }
      }
      > .device {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 20px;
        margin-bottom: 20px;
        > div {
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
        }
      }
      > .message {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 20px;
        margin-bottom: 24px;
        > div {
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          &.message-name {
            color: #ff990c;
          }
          &.message-btns {
            > span {
              font-weight: 400;
              font-size: 16px;
              color: #ffffff;
              cursor: pointer;
            }
            > span:first-child {
              margin-right: 20px;
              display: inline-block;
              color: #ff990c;
            }
          }
        }
      }
    }
  }
  > .content {
    flex-shrink: 0;
    height: 748px;
    display: flex;
    justify-content: space-between;
    > .charts {
      padding-left: 16px;
      background: rgba(27, 63, 137, 0.2);
      width: calc(50% - 10px);
      height: 100%;
      > .title {
        height: 48px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-image: url('@/assets/image/card-title-bg.png');
        background-size: auto 100%;
        background-repeat: no-repeat;
        margin-bottom: 20px;
        > .title-name {
          padding-left: 48px;
          font-weight: bold;
          font-size: 24px;
          color: #ffffff;
          text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
          font-style: italic;
        }
      }
      > .chart {
        margin-bottom: 20px;
        height: calc(50% - 10px - 48px);
        > .chart-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 12px 6px 20px;
          background: linear-gradient(270deg, rgba(19, 52, 103, 0) 0%, #133467 100%);
          > .title-name {
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
          }
          > img {
            cursor: pointer;
          }
        }
      }
    }
    > .ai-message {
      width: calc(50% - 10px);
      height: 100%;
      overflow: hidden;
      > div {
        display: flex;
        flex-direction: column;
        padding-left: 16px;
        > .title {
          flex-shrink: 0;
          height: 48px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-image: url('@/assets/image/card-title-bg.png');
          background-size: auto 100%;
          background-repeat: no-repeat;
          margin-bottom: 20px;
          padding-left: 48px;
          font-weight: bold;
          font-size: 24px;
          color: #ffffff;
          text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
          font-style: italic;
        }
        > .text {
          padding-bottom: 16px;
          padding-right: 24px;
          height: 100%;
          overflow: auto;
          word-wrap: break-word;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 32px;
        }
      }
      > .ai-1 {
        width: 100%;
        height: 358px;
        background: url('@/assets/image/energyStorageSafety/ai-1-bg.png') no-repeat center;
        background-color: rgba(27, 63, 137, 0.2);
        background-size: 100% 100%;
        margin-bottom: 20px;
      }
      > .ai-2 {
        width: 100%;
        height: 370px;
        background: url('@/assets/image/energyStorageSafety/ai-2-bg.png') no-repeat center;
        background-color: rgba(27, 63, 137, 0.2);
        background-size: 100% 100%;
      }
    }
  }
}
</style>
