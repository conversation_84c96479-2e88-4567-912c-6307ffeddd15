<template>
  <div class="battery" v-loading="loading">
    <div class="header">
      <div class="title">唐能光伏运维中心光储电站</div>
    </div>
    <el-collapse>
      <el-collapse-item v-for="ikey in 7" :name="ikey" :key="ikey">
        <template #title>
          <div class="collapse-title">
            <img v-if="ikey === 1" src="@/assets/image/energyStorageSafety/warning2.png" alt="" />
            <div class="battery-list">
              <div
                class="battery-header"
                v-for="i in 11"
                :class="{ warrning: i === 1 && ikey === 1 }"
                @click.stop="compartmentPopupVisible = true"
                :key="i"
              >
                {{ ikey + 1 }}-{{ i + 1 }}#电池舱
              </div>
            </div>
          </div>
        </template>
        <div class="battery-cabin-wrap">
          <div class="tips">
            <div v-for="item in 5" :key="item">1#堆{{ item }}#簇</div>
          </div>
          <div class="battery-list">
            <div class="battery-body-wrap" v-for="i in 11" :key="i">
              <div class="battery-body">
                <div
                  @click="clusterPopupVisible = true"
                  v-for="it in 5"
                  :key="it"
                  :class="{ warrning: i === 1 && it === 1 && ikey === 1 }"
                >
                  <span v-if="i === 1 && it === 1 && ikey === 1">预警</span>
                  <span v-else>正常</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <div class="query">
      <el-select class="!w-[240px]" v-model="currentPowerPlant" placeholder="请选择">
        <el-option v-for="item in powerPlantOpts" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <div class="button-group">
        <div @click="toDetail">综合研判</div>
        <div class="active">电池详情</div>
      </div>
    </div>
  </div>
  <clusterPopup v-model="clusterPopupVisible"></clusterPopup>
  <compartmentPopup v-model="compartmentPopupVisible"></compartmentPopup>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import clusterPopup from '@/views/energyStorageSafety/assessment/components/clusterPopup.vue'
import compartmentPopup from '@/views/energyStorageSafety/assessment/components/compartmentPopup.vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'

const userInfo = useUserInfo()
const router = useRouter()
const toDetail = () => {
  router.push({
    path: '/energyStorageSafety/assessment',
  })
}
const clusterPopupVisible = ref(false)
const compartmentPopupVisible = ref(false)
const currentPowerPlant = ref('1')
const powerPlantOpts = ref([
  {
    label: '唐能光伏运维中心光储电站',
    value: '1',
  },
  {
    label: '储能电站2',
    value: '2',
  },
  {
    label: '储能电站3',
    value: '3',
  },
])

const loading = ref(false)
const getDetail = () => {
  loading.value = true
  $API
    .post({
      url: '/comprehensiveAnalysis/batteryDetail',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
      },
    })
    .then((res) => {
      console.log(res)
    })
    .finally(() => {
      loading.value = false
    })
}
console.log(getDetail)
const getTree = () => {
  loading.value = true
  $API
    .post({
      url: '/comprehensiveAnalysis/getDeviceTreeInfo',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
      },
    })
    .then((res) => {
      console.log(res)
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  // getDetail()
  getTree()
})
</script>
<style scoped lang="scss">
.battery {
  height: 100%;
  overflow-y: auto;
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  padding: 0 10px;
  position: relative;
  > .header {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    > .title {
      width: 424px;
      flex-shrink: 0;
      padding: 0px 15px;
      background: url('@/assets/image/energyStorageSafety/title-bg.png') no-repeat center;
      background-size: contain;
      text-align: center;
      line-height: 62px;
      font-weight: 600;
      font-size: 32px;
      color: #ffffff;
      font-style: italic;
    }
  }
  > .query {
    position: absolute;
    top: 0;
    right: 10px;
    display: flex;
    align-items: center;
    > .button-group {
      margin-left: 32px;
      display: inline-flex;
      > div {
        width: 90px;
        padding: 6px 16px;
        border: none;
        color: white;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: rgba(69, 125, 238, 0.2);
        border: 1px solid #244378;
        &.active {
          background-color: rgba(25, 79, 185, 0.75);
          border: 1px solid rgba(51, 119, 255, 0.75);
        }
      }
      > div:first-child {
        border-radius: 16px 0px 0px 16px;
      }
      > div:last-child {
        border-radius: 0px 20px 20px 0px;
      }
    }
  }
}
:deep(.el-collapse) {
  border: none;
}
:deep(.el-collapse-item) {
  background: rgba(10, 33, 62, 0.4);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #104487;
  margin-bottom: 16px;
  .el-collapse-item__header {
    padding: 0 20px 0 160px;
    border: none;
    height: 68px;
    position: relative;
    cursor: auto;
  }
  .el-collapse-item__arrow {
    color: #ffffff;
    font-size: 20px;
    > svg {
      height: 20px;
      width: 20px;
      display: inline-block;
    }
  }
}
.collapse-title {
  display: flex;
  align-items: center;
  > img {
    position: absolute;
    left: 70px;
  }
}
.battery-cabin-wrap {
  display: flex;
  padding: 0 20px 0 20px;
  > .tips {
    width: 140px;
    > div {
      width: 120px;
      background: linear-gradient(180deg, #084fc8 1%, #001c52 100%);
      border-radius: 888px 888px 888px 888px;
      border: 1px solid rgba(1, 56, 152, 0.3);
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      height: 32px;
      text-align: center;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      &.warrning {
        background: linear-gradient(180deg, #a20202 0%, #370505 100%);
        border: 1px solid rgba(184, 10, 10, 0.3);
      }
    }
  }
}

.battery-list {
  display: grid;
  grid-template-columns: repeat(11, 1fr);
  grid-gap: 18px;
  > .battery-header {
    background: linear-gradient(180deg, rgba(23, 72, 158, 0.3) 0%, rgba(4, 21, 53, 0.3) 100%);
    border-radius: 888px 888px 888px 888px;
    border: 1px solid #1150bc;
    padding: 0px 18px;
    height: 32px;
    line-height: 32px;
    width: 130px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    cursor: pointer;
    text-align: center;
    &.warrning {
      background: linear-gradient(180deg, #a20202 0%, #370505 100%);
      border: 1px solid rgba(184, 10, 10, 0.3);
    }
  }
  > .battery-body-wrap {
    > .battery-body {
      display: flex;
      flex-direction: column;
      > div {
        width: 130px;
        border-radius: 888px 888px 888px 888px;
        padding: 0px 18px;
        height: 32px;
        line-height: 32px;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        margin-bottom: 12px;
        text-align: center;
        cursor: pointer;
        background: linear-gradient(180deg, rgba(0, 108, 40, 0.3) 0%, rgba(3, 47, 21, 0.3) 100%);
        border: 1px solid rgba(0, 134, 49, 1);
        &:last-child {
          margin-bottom: 0px;
        }
        &.warrning {
          background: linear-gradient(180deg, #a20202 0%, #370505 100%);
          border: 1px solid rgba(184, 10, 10, 0.3);
        }
      }
    }
  }
}
</style>
