<template>
  <div :class="containerClass" v-loading="loading">
    <div ref="chartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import * as echarts from 'echarts/core'
import { ECharts } from 'echarts/core'
import $API from '~/common/api'
import { useUserInfo } from '@/store'

const userInfo = useUserInfo()

// 定义组件接受的 props
interface Props {
  width?: string
  height?: string
  className?: string
}

// 定义 props
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  className: '',
})

// 使用 props 设置容器类名
const containerClass = computed(() => {
  const classes = ['w-full h-full']
  if (props.className) {
    classes.push(props.className)
  }
  return classes.join(' ')
})

// 图表引用
const chartRef = ref<HTMLElement | null>(null)
const { setOptions } = useECharts(chartRef as any)

const values = ref([])
const indicator = ref<
  {
    name: string
  }[]
>([])

// 初始化图表
const initChart = () => {
  setOptions({
    backgroundColor: 'transparent',
    toolbox: {
      show: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      padding: [8, 12],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    // 雷达图坐标系
    radar: {
      // indicator: [
      //   { name: '温度异常', max: 100 },
      //   { name: '一致性异常', max: 100 },
      //   { name: '容量衰竭异常', max: 100 },
      //   { name: '内短路异常', max: 100 },
      //   { name: '过放异常', max: 100 },
      //   { name: '过冲异常', max: 100 },
      //   { name: 'BMS异常', max: 100 },
      // ],
      indicator: indicator.value,
      radius: '65%',
      axisName: {
        color: '#fff',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(100, 149, 237, 0.5)',
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(100, 149, 237, 0.5)',
        },
      },
      splitArea: {
        show: false,
      },
    },
    // 系列数据
    series: [
      {
        name: '电池异常状态',
        type: 'radar',
        data: [
          {
            value: values.value,
            name: '异常状态',
            areaStyle: {
              color: 'rgba(65, 105, 225, 0.4)',
            },
            lineStyle: {
              width: 2,
              color: '#4169E1',
            },
            itemStyle: {
              color: '#4169E1',
            },
          },
        ],
      },
    ],
  })
}

// 监听窗口大小变化
const handleResize = () => {
  if (chartRef.value) {
    const instance = (echarts as any).getInstanceByDom(chartRef.value as HTMLElement) as ECharts | undefined
    if (instance) {
      instance.resize()
    }
  }
}

const loading = ref(false)
const getData = () => {
  loading.value = true
  $API
    .post({
      url: '/comprehensiveAnalysis/realtimeRiskSource',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
      },
    })
    .then((res) => {
      if ((res as any).code === 'success') {
        values.value = res.data.map((x) => x.riskSourceNum)
        indicator.value = res.data.map((x) => ({ name: x.riskSourceName }))
        initChart()
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 组件挂载时初始化图表
onMounted(() => {
  getData()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  initChart,
})
</script>
