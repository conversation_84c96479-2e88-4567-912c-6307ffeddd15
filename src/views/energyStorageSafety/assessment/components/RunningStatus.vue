<template>
  <div :class="containerClass" v-loading="loading">
    <div ref="chartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import * as echarts from 'echarts/core'
import { ECharts } from 'echarts/core'
import $API from '~/common/api'
import { useUserInfo } from '@/store'

const userInfo = useUserInfo()
// 定义组件接受的 props
interface Props {
  width?: string
  height?: string
  className?: string
}

// 定义 props
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  className: '',
})

// 使用 props 设置容器类名
const containerClass = computed(() => {
  const classes = ['w-full h-full']
  if (props.className) {
    classes.push(props.className)
  }
  return classes.join(' ')
})

// 图表引用
const chartRef = ref<HTMLElement | null>(null)
const { setOptions } = useECharts(chartRef as any)

const xAxisData = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
]

// 告警趋势数据
const trendData = {
  // 柱状图数据（左侧Y轴）
  barData: [],
  // 折线图数据（右侧Y轴）
  lineData: [],
}

// 初始化图表
const initChart = () => {
  setOptions({
    backgroundColor: 'transparent',
    toolbox: {
      show: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      padding: [8, 12],
    },
    legend: {
      data: ['日总SOC（%）', '日总有功功率（kw）'],
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 14,
      },
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 20,
      top: 0,
      left: 'center',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        min: 0,
        interval: 10,
        axisLabel: {
          formatter: '{value}',
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
          },
        },
      },
      {
        type: 'value',
        name: '百分比',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}%',
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '日总SOC（%）',
        type: 'bar',
        barWidth: 20,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(20, 97, 234, 1)' },
            { offset: 1, color: 'rgba(20, 97, 234, 0)' },
          ]),
        },
        data: trendData.barData,
      },
      {
        name: '日总有功功率（kw）',
        type: 'line',
        yAxisIndex: 1,
        // smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: '#FFC233',
        },
        itemStyle: {
          color: '#FFC233',
          borderColor: '#fff',
          borderWidth: 1,
        },
        data: trendData.lineData,
      },
    ],
  })
}

// 监听窗口大小变化
const handleResize = () => {
  if (chartRef.value) {
    const instance = (echarts as any).getInstanceByDom(chartRef.value as HTMLElement) as ECharts | undefined
    if (instance) {
      instance.resize()
    }
  }
}
const loading = ref(false)
const getData = () => {
  loading.value = true
  $API
    .post({
      url: '/comprehensiveAnalysis/runningState',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
      },
    })
    .then((res) => {
      if ((res as any).code === 'success') {
        trendData.barData = res.data[0].list.map((v) => parseFloat(v.v))
        trendData.lineData = res.data[1].list.map((v) => parseFloat(v.v))
        initChart()
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 组件挂载时初始化图表
onMounted(() => {
  getData()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  initChart,
})
</script>
