<template>
  <div :class="containerClass" v-loading="loading">
    <div ref="chartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useECharts } from '@/hooks/web/useECharts'
import * as echarts from 'echarts/core'
import { ECharts } from 'echarts/core'
import $API from '~/common/api'
import { useUserInfo } from '@/store'

const userInfo = useUserInfo()

// 定义组件接受的 props
interface Props {
  width?: string
  height?: string
  className?: string
}

// 定义 props
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  className: '',
})

// 使用 props 设置容器类名
const containerClass = computed(() => {
  const classes = ['w-full h-full']
  if (props.className) {
    classes.push(props.className)
  }
  return classes.join(' ')
})

// 图表引用
const chartRef = ref<HTMLElement | null>(null)
const { setOptions } = useECharts(chartRef as any)

// 数据准备
const data = ref<{ name: string; value: number }[]>([
  // { name: '电池异常-内短路', value: 0.38 },
])

// 颜色列表
const colorList = [
  '#5470C6',
  '#91CC75',
  '#FAC858',
  '#EE6666',
  '#73C0DE',
  '#3BA272',
  '#FC8452',
  '#9A60B4',
  '#EA7CCC',
  '#60C0DD',
  '#F7C23A',
  '#B5C334',
  '#E87C25',
  '#27727B',
  '#FE846C',
  '#9BCA63',
]

// 初始化图表
const initChart = () => {
  setOptions({
    backgroundColor: 'transparent',
    toolbox: {
      show: false,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    // 提示框
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}% ({d}%)',
    },

    // 图例 - 带翻页功能
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 'center',
      pageTextStyle: {
        color: '#fff',
      },
      itemGap: 16,
      pageIconColor: '#fff',
      pageIconInactiveColor: '#666',
      pageIconSize: 12,
      pageButtonItemGap: 5,
      pageButtonGap: 15,
      pageFormatter: '{current}/{total}',
      data: data.value.map(function (item) {
        return item.name
      }),
      icon: 'rect',
      itemWidth: 10,
      itemHeight: 10,
      formatter: function (name) {
        // 查找对应数据项
        const item = data.value.find(function (d) {
          return d.name === name
        })
        // 返回格式化字符串，名称在左，百分比在右
        return `{name|${name}}{value|${item?.value}%}`
      },
      textStyle: {
        color: '#fff',
        fontSize: 12,
        lineHeight: 20,
        // 使用富文本样式
        rich: {
          name: {
            width: 170, // 名称部分宽度
            align: 'left',
            padding: [0, 0, 0, 5],
          },
          value: {
            width: 30, // 值部分宽度
            align: 'right',
            padding: [0, 5, 0, 0],
          },
        },
      },
    },
    // 系列数据
    series: [
      {
        name: '预警分析统计',
        type: 'pie',
        radius: ['35%', '50%'],
        center: ['20%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        data: data.value.map(function (item, index) {
          return {
            value: item.value,
            name: item.name,
            itemStyle: {
              color: colorList[index % colorList.length],
            },
          }
        }),
      },
    ],
  })
}

// 监听窗口大小变化
const handleResize = () => {
  if (chartRef.value) {
    const instance = (echarts as any).getInstanceByDom(chartRef.value as HTMLElement) as ECharts | undefined
    if (instance) {
      instance.resize()
    }
  }
}

const loading = ref(false)
const getData = () => {
  loading.value = true
  $API
    .post({
      url: '/comprehensiveAnalysis/warningAnalysisStat',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
      },
    })
    .then((res) => {
      if ((res as any).code === 'success') {
        const total = res.data.reduce((acc, cur) => acc + cur.warningNum, 0)
        data.value = res.data.map((v) => ({
          name: v.warningName,
          value: ((v.warningNum / total) * 100).toFixed(2),
        }))
        initChart()
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 组件挂载时初始化图表
onMounted(() => {
  getData()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  initChart,
})
</script>
