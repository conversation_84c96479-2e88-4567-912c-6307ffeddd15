<template>
  <popupSideBlack v-model="visible" :popup-title="'预警详情'">
    <div class="body">
      <div class="title">
        <span>2-2#电池舱1#堆1#簇</span>
        <span class="warning warning-3">三级预警</span>
        <span class="warning warning-4">高温预警</span>
      </div>
      <div class="texts">
        <div v-for="item in texts" :key="item.label">
          <div class="name">{{ item.label }}</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
      <div class="btn">
        <div>查看预警诊断详情</div>
      </div>
    </div>
  </popupSideBlack>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue'
import popupSideBlack from '~/components/public/popup/popupSideBlack.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:modelValue'])
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const texts = ref([
  {
    label: 'SOC采集值',
    value: '99.7%',
  },
  {
    label: 'SOC评估值',
    value: '95.7%',
  },
  {
    label: 'SOH采集值',
    value: '92.6%',
  },
  {
    label: 'SOH评估值（热力学）',
    value: '94.4%',
  },
  {
    label: 'SOH评估值（动力学）',
    value: '93.3%',
  },
  {
    label: '剩余循环次数',
    value: '4998次',
  },
  {
    label: '最高单体电压',
    value: '3342mV',
  },
  {
    label: '最低单体电压',
    value: '3340mV',
  },
  {
    label: '单体电压最大极差',
    value: '2mV',
  },
  {
    label: '最高单体温度',
    value: '25℃',
  },
  {
    label: '最低单体温度',
    value: '23℃',
  },
  {
    label: '单体温度最大极差',
    value: '2℃',
  },
  {
    label: '充放电损耗率',
    value: '10.31%',
  },
  {
    label: '最大内短路电流',
    value: '0mA',
  },
  {
    label: 'SOC采集值',
    value: '0',
  },
])
</script>
<style lang="scss" scoped>
.body {
  display: flex;
  flex-direction: column;
  height: 100%;
  > .title {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    > span:first-child {
      font-weight: 600;
      font-size: 16px;
      color: #0099ff;
      margin-right: 20px;
    }
    > .warning {
      flex-shrink: 0;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      border-radius: 888px 888px 888px 888px;
      padding: 5px 18px;
      margin-right: 10px;
      &:last-child {
        margin-right: 0px;
      }
      &.warning-3 {
        background: linear-gradient(180deg, #8f5300 0%, #352003 100%);
        border: 1px solid rgba(146, 85, 0, 0.3);
      }
      &.warning-4 {
        background: linear-gradient(180deg, #a20202 0%, #370505 100%);
        border: 1px solid rgba(184, 10, 10, 0.3);
      }
    }
  }
  > .texts {
    height: 100%;
    overflow-y: auto;
    margin-bottom: 20px;
    > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 8px;
      > .name {
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }
      > .value {
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
      }
      &:nth-child(2n + 1) {
        position: relative;
        background: linear-gradient(
          90deg,
          rgba(21, 85, 181, 0) 1%,
          rgba(13, 59, 140, 0.2) 52%,
          rgba(21, 85, 181, 0) 100%
        );
        &::after,
        &::before {
          content: '';
          position: absolute;
          left: 0;
          width: 100%;
          height: 1px;
          background: linear-gradient(90deg, rgba(35, 88, 178, 0.01) 0%, #184eac 52%, rgba(35, 88, 178, 0) 100%);
        }
        &::after {
          bottom: 0;
        }
        &::before {
          top: 0;
        }
      }
    }
  }
  > .btn {
    flex-shrink: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    > div {
      cursor: pointer;
      padding: 6px 25px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      background: linear-gradient(180deg, rgba(10, 73, 155, 0.85) 2%, rgba(3, 31, 73, 0.85) 100%);
      border: 1px solid #115ed1;
    }
  }
}
</style>
