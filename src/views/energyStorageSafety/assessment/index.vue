<template>
  <div class="assessment">
    <div class="content">
      <div class="left" v-loading="loading2">
        <div class="situations" v-for="(item, index) in situations.slice(0, 4)" :key="item.name">
          <div class="situation">
            <div :class="['name', item.bg]">
              <span>{{ index + 1 }}</span>
              <span>{{ item.name }}</span>
            </div>
            <div class="info">
              <div v-for="info in item.info" :key="info.name" class="item">
                <div class="num">
                  <div class="value">{{ info.value }}</div>
                  <div class="unit">{{ info.unit }}</div>
                </div>
                <div class="name">{{ info.name }}</div>
              </div>
            </div>
          </div>
          <div class="warning" v-if="item.warning">
            <div v-for="warning in item.warning" :key="warning" class="item">
              <span>{{ warning }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="statistics" v-loading="loading1">
        <div class="sleft">
          <div class="icon-wrap" v-for="item in icons.slice(0, 2)" :key="item.name">
            <div class="title" :class="item.nameClass">
              <span class="name">{{ item.name }}</span>
              <div class="value">
                <span>{{ item.value }}</span>
                <span>{{ item.unit }}</span>
              </div>
            </div>
            <div class="icons">
              <div class="icon" :class="item.icon"></div>
              <div :class="item.btm"></div>
            </div>
          </div>
        </div>
        <div class="scontent">
          <div class="title">唐能光伏运维中心光储电站</div>
          <div class="scontent-bg">
            <div class="num" v-for="item in nums" :key="item.name" :class="item.icon">
              <div class="icon">
                <span>{{ item.value }}</span>
                <span>{{ item.unit }}</span>
              </div>
              <span>{{ item.name }}</span>
            </div>
          </div>
          <img class="line1" src="@/assets/image/energyStorageSafety/line-1.png" alt="" />
          <img class="line2" src="@/assets/image/energyStorageSafety/line-2.png" alt="" />
          <img class="line3" src="@/assets/image/energyStorageSafety/line-3.png" alt="" />
          <img class="line4" src="@/assets/image/energyStorageSafety/line-4.png" alt="" />
        </div>
        <div class="sright">
          <div class="icon-wrap" v-for="item in icons.slice(2, 4)" :key="item.name">
            <div class="title">
              <span class="name">{{ item.name }}</span>
              <div class="value">
                <span>{{ item.value }}</span>
                <span>{{ item.unit }}</span>
              </div>
            </div>
            <div class="icons">
              <div class="icon" :class="item.icon"></div>
              <div :class="item.btm"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="right" v-loading="loading2">
        <div class="situations" v-for="(item, index) in situations.slice(4, 8)" :key="item.name">
          <div class="warning" v-if="item.warning">
            <div v-for="warning in item.warning" :key="warning" class="item">
              <span>{{ warning }}</span>
            </div>
          </div>
          <div class="situation">
            <div :class="['name', item.bg]">
              <span>{{ index + 5 }}</span>
              <span>{{ item.name }}</span>
            </div>
            <div class="info">
              <div v-for="info in item.info" :key="info.name" class="item">
                <div class="num">
                  <div class="value">{{ info.value }}</div>
                  <div class="unit">{{ info.unit }}</div>
                </div>
                <div class="name">{{ info.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="charts">
      <div>
        <div class="title">运行状态</div>
        <RunningStatus></RunningStatus>
      </div>
      <div>
        <div class="title">全站极差分析</div>
        <StationRangeAnalysis></StationRangeAnalysis>
      </div>
      <div>
        <div class="title">储能电站实时风险源</div>
        <RealTimeRiskSources></RealTimeRiskSources>
      </div>
      <div>
        <div class="title">预警分析统计</div>
        <WarningAnalysisStats></WarningAnalysisStats>
      </div>
    </div>
    <div class="query">
      <el-select class="!w-[240px]" v-model="currentPowerPlant" placeholder="请选择">
        <el-option v-for="item in powerPlantOpts" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <div class="button-group">
        <div class="active">综合研判</div>
        <div @click="toDetail">电池详情</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '@/store'
import RunningStatus from './components/RunningStatus.vue'
import StationRangeAnalysis from './components/StationRangeAnalysis.vue'
import RealTimeRiskSources from './components/RealTimeRiskSources.vue'
import WarningAnalysisStats from './components/WarningAnalysisStats.vue'

const userInfo = useUserInfo()
const router = useRouter()
interface IStation {
  name: string
  bg: 'bg1' | 'bg2'
  key: string
  info: {
    name: string
    value: string | number
    unit: string
    key: string
  }[]
  warning?: string[]
}
// 态势
const situations = ref<IStation[]>([
  {
    name: '充放电损耗率分析',
    bg: 'bg1',
    key: 'chargeRateAnalysis',
    info: [
      { name: '充放电效率', value: 0, unit: '%', key: 'chargeDischargeEfficiency' },
      { name: '充放电损耗率', value: 0, unit: '%', key: 'chargeDischargeLossRate' },
    ],
  },
  {
    name: 'SOC分析',
    bg: 'bg1',
    key: 'socAnalysis',
    info: [
      { name: 'SOC最大极差', value: 0, unit: '%', key: 'maximumRage' },
      { name: 'SOC评估值', value: 0, unit: '%', key: 'socEvaluation' },
    ],
  },
  {
    name: 'SOH分析',
    bg: 'bg1',
    key: 'sohAnalysis',
    info: [
      { name: '动力学SOH', value: 0, unit: '%', key: 'dynamicSoh' },
      { name: '热力学SOH', value: 0, unit: '%', key: 'ThermodynamicSoh' },
    ],
  },
  {
    name: '数据异常分析',
    bg: 'bg1',
    key: 'abnormalDataAnalysis',
    info: [
      { name: '电压数据异常率', value: 0, unit: '%', key: 'voltageAbnormalRate' },
      { name: '温度数据异常率', value: 0, unit: '%', key: 'temperatureAbnormalRate' },
    ],
  },
  {
    name: '电压分析',
    bg: 'bg1',
    key: 'voltageAnalysis',
    info: [
      { name: '最高单体电压', value: 0, unit: 'mA', key: 'maximumVoltage' },
      { name: '最低单体电压', value: 0, unit: 'mA', key: 'minimumVoltage' },
    ],
  },
  {
    name: '温度分析',
    bg: 'bg1',
    key: 'temperatureAnalysis',
    info: [
      { name: '最大簇内单体压差', value: 0, unit: 'mV', key: 'maximumClusterTemperatureDifference' },
      { name: 'SOC评估值', value: 0, unit: '°C', key: 'socEvaluation' },
    ],
  },
  {
    name: '内短路分析',
    bg: 'bg1',
    key: 'internalShortAnalysis',
    info: [
      { name: '最低单体温度', value: 0, unit: '°C', key: 'minimumCellTemperature' },
      { name: '最大簇内单体温差', value: 0, unit: '°C', key: 'maximumClusterTemperatureDifference' },
    ],
  },
  {
    name: '热失控分析',
    bg: 'bg1',
    key: 'thermalRunawayAnalysis',
    info: [
      { name: '最大漏电电流', value: 0, unit: 'mA', key: 'maximumLeakageCurrent' },
      { name: '最大单体温升速率', value: 0, unit: '', key: 'maximumCellTemperatureRiseRate' },
    ],
  },
])
const nums = ref([
  { name: '总SOC', value: 0, unit: '%', icon: 'num-bg a1', key: 'totalSoc' },
  { name: '总充电量', value: 0, unit: 'GWh', icon: 'num-bg a2', key: 'totalCharge' },
  { name: '总放电量', value: 0, unit: 'GWh', icon: 'num-bg a3', key: 'totalDischarge' },
  { name: '', value: 0, unit: '全站健康度', icon: 'healthiness-center-bg a4', key: 'storageStationNum' },
  { name: '总预警数', value: 0, unit: '起', icon: 'num-bg-1 a5', key: 'totalWarningNum' },
  { name: '严重预警数', value: 0, unit: '起', icon: 'num-bg-2 a6', key: 'firstLevelWarningNum' },
  { name: '体检报告数', value: 0, unit: '份', icon: 'num-bg-1 a7', key: 'healthReportNum' },
])
const icons = ref([
  {
    name: '建筑规模',
    value: 0,
    unit: 'GWh',
    icon: 'icon-1',
    btm: 'icon-bottom',
    nameClass: 'max-w',
    key: 'designCapacity',
  },
  { name: 'PSC', value: 0, unit: '台', icon: 'icon-3', btm: 'icon-bottom', key: 'pcsNum' },
  { name: '电池舱', value: 0, unit: '个', icon: 'icon-2', btm: 'icon-bottom', key: 'batteryCompartmentNum' },
  { name: '电池簇', value: 0, unit: '个', icon: 'icon-4', btm: 'icon-bottom', key: 'batteryClusterNum' },
  // { name: '电池', value: '112127', unit: '节', icon: 'icon-4', btm: 'icon-bottom', key: 'batteryClusterNum' },
])
const currentPowerPlant = ref('1')
const powerPlantOpts = ref([
  {
    label: '唐能光伏运维中心光储电站',
    value: '1',
  },
  {
    label: '储能电站2',
    value: '2',
  },
  {
    label: '储能电站3',
    value: '3',
  },
])
const toDetail = () => {
  router.push({
    path: '/energyStorageSafety/assessment/battery',
  })
}

const loading1 = ref(false)
const getOverview = () => {
  loading1.value = true
  $API
    .post({
      url: '/comprehensiveAnalysis/overview',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
      },
    })
    .then((res) => {
      if ((res as any).code === 'success') {
        nums.value = nums.value.map((v) => {
          v.value = (res as any).data[v.key] || 0
          return v
        })
        icons.value = icons.value.map((v) => {
          v.value = (res as any).data[v.key] || 0
          return v
        })
      }
    })
    .finally(() => {
      loading1.value = false
    })
}
const loading2 = ref(false)
const getWarningStat = () => {
  loading2.value = true
  $API
    .post({
      url: '/comprehensiveAnalysis/warningStat',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
      },
    })
    .then((res) => {
      if ((res as any).code === 'success') {
        situations.value = situations.value.map((v) => {
          const obj = res.data[v.key] || {}
          v.warning = (obj.warningDesc || '').split(',').filter((x) => x)
          v.bg = obj.warningDesc ? 'bg2' : 'bg1'
          for (const item of v.info) {
            item.value = obj[item.key] || 0
          }
          return v
        })
      }
    })
    .finally(() => {
      loading2.value = false
    })
}
onMounted(() => {
  getOverview()
  getWarningStat()
})
</script>
<style scoped lang="scss">
/* 添加动画关键帧定义 */
@keyframes iconBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}
.assessment {
  height: 100%;
  overflow-y: auto;
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  padding: 0 10px;
  position: relative;
  > .content {
    display: flex;
    justify-content: space-between;
    > .left,
    > .right {
      flex-shrink: 0;
      width: 400px;
      padding-top: 60px;
      > .situations {
        display: flex;
        > .situation {
          margin-bottom: 20px;
          &:last-child {
            margin-bottom: 0;
          }
          > .name {
            display: flex;
            align-items: center;
            width: 278px;
            height: 36px;
            font-weight: 500;
            margin-bottom: 12px;
            font-size: 16px;
            color: #ffffff;
            margin-left: -15px;
            padding-left: 28px;
            &.bg1 {
              background: url('@/assets/image/energyStorageSafety/situations-bg1.png') no-repeat center;
              background-size: cover;
            }
            &.bg2 {
              background: url('@/assets/image/energyStorageSafety/situations-bg2.png') no-repeat center;
              background-size: cover;
            }
            > span:first-child {
              font-weight: 500;
              font-size: 24px;
              color: #ffffff;
              margin-right: 32px;
            }
          }
          > .info {
            > .item {
              width: 278px;
              height: 36px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              background: rgba(27, 64, 128, 0.2);
              border: 1px solid #244989;
              margin-bottom: 10px;
              padding: 0 16px;
              > .name {
                justify-self: flex-end;
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
              }
              > .num {
                display: flex;
                align-items: flex-end;
                .value {
                  font-weight: 500;
                  font-size: 20px;
                  color: #ffaa00;
                }
                .unit {
                  font-weight: 400;
                  font-size: 16px;
                  color: #ffffff;
                  margin-left: 3px;
                }
              }
            }
          }
        }
        > .warning {
          margin: 0 6px;
          > .item {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 6px 3px;
            min-width: 105px;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            margin-bottom: 8px;
            background: linear-gradient(180deg, rgba(131, 13, 13, 0.75) 0%, rgba(56, 5, 5, 0.75) 100%);
            border: 1px solid #a42424;
          }
        }
      }
    }
    > .right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
    > .statistics {
      width: 100%;
      min-width: 950px;
      min-height: 700px;
      display: flex;
      justify-content: center;
      > .scontent {
        margin: 0 28px;
        display: flex;
        flex-direction: column;
        position: relative;
        align-items: center;
        > .title {
          width: 424px;
          flex-shrink: 0;
          padding: 0px 15px;
          background: url('@/assets/image/energyStorageSafety/title-bg.png') no-repeat center;
          background-size: contain;
          text-align: center;
          line-height: 62px;
          font-weight: 600;
          font-size: 32px;
          color: #ffffff;
          font-style: italic;
        }
        > .scontent-bg {
          margin-top: 20px;
          height: 615px;
          width: 586px;
          background: url('@/assets/image/energyStorageSafety/center-bg.png') no-repeat center;
          background-size: contain;
          position: relative;
          > .num {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            > span {
              font-size: 16px;
              color: #ffffff;
              margin-top: 10px;
            }
            > .icon {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              > span:first-child {
                font-weight: bold;
                font-size: 20px;
                color: #ffffff;
                margin-bottom: 3px;
              }
              > span:last-child {
                font-size: 14px;
                color: #ffffff;
              }
            }
            &.num-bg {
              &.a1 {
                top: 31px;
                left: 49px;
              }
              &.a2 {
                top: 0px;
                right: 230px;
              }
              &.a3 {
                top: 31px;
                right: 50px;
              }
              > .icon {
                width: 130px;
                height: 115px;
                background: url('@/assets/image/energyStorageSafety/num-bg.png') no-repeat center;
                background-size: contain;
              }
            }
            &.num-bg-1 {
              bottom: 48px;
              &.a5 {
                right: 49px;
              }
              &.a7 {
                left: 50px;
              }
              > .icon {
                padding-bottom: 12px;
                width: 130px;
                height: 115px;
                background: url('@/assets/image/energyStorageSafety/num-bg-1.png') no-repeat center;
                background-size: contain;
              }
            }
            &.num-bg-2 {
              bottom: 16px;
              right: 230px;
              > .icon {
                padding-bottom: 12px;
                width: 130px;
                height: 115px;
                background: url('@/assets/image/energyStorageSafety/num-bg-2.png') no-repeat center;
                background-size: contain;
              }
            }
            &.healthiness-center-bg {
              bottom: 229px;
              left: 187px;
              > .icon {
                width: 213px;
                height: 213px;
                background: url('@/assets/image/energyStorageSafety/healthiness-center-bg.png') no-repeat center;
                background-size: contain;
                > span:first-child {
                  font-size: 64px;
                }
                > span:last-child {
                  font-size: 20px;
                }
              }
            }
          }
        }
        > img {
          height: 35px;
          width: 122px;
          position: absolute;
          &.line1 {
            top: 310px;
            left: -36px;
          }
          &.line2 {
            top: 310px;
            right: -36px;
          }
          &.line3 {
            bottom: 195px;
            left: -36px;
          }
          &.line4 {
            bottom: 195px;
            right: -36px;
          }
        }
      }
      > .sleft,
      > .sright {
        padding-top: 80px;
        display: flex;
        flex-direction: column;
        > .icon-wrap {
          display: flex;
          flex-direction: column;
          align-items: center;
          &:last-child {
            margin-top: 60px;
          }
          > .title {
            height: 50px;
            min-width: 160px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: url('@/assets/image/energyStorageSafety/icon-title-bg.png') no-repeat center;
            background-size: 100% 100%;
            padding: 0 10px 0 20px;
            margin-bottom: 20px;
            &.max-w {
              width: 190px;
            }
            > .name {
              font-weight: 400;
              font-size: 14px;
              color: #ffffff;
            }
            > .value {
              > span:first-child {
                color: #ffaa00;
                font-size: 20px;
                display: inline-block;
                margin-right: 3px;
              }
              > span:last-child {
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
              }
            }
          }
          > .icons {
            display: flex;
            flex-direction: column;
            align-items: center;
            > .icon {
              animation: iconBounce 2s ease-in-out infinite;
              transform-origin: center bottom;
            }
            > .icon-1 {
              width: 116px;
              height: 120px;
              background: url('@/assets/image/energyStorageSafety/icon-1.png') no-repeat center;
              background-size: contain;
            }
            > .icon-2 {
              width: 111px;
              height: 120px;
              background: url('@/assets/image/energyStorageSafety/icon-2.png') no-repeat center;
              background-size: contain;
            }
            > .icon-3 {
              width: 116px;
              height: 120px;
              background: url('@/assets/image/energyStorageSafety/icon-3.png') no-repeat center;
              background-size: contain;
            }
            > .icon-4 {
              width: 109px;
              height: 120px;
              background: url('@/assets/image/energyStorageSafety/icon-4.png') no-repeat center;
              background-size: contain;
            }
            > .icon-bottom {
              margin-top: -100px;
              width: 188px;
              height: 200px;
              background: url('@/assets/image/energyStorageSafety/icon-bottom.png') no-repeat center;
              background-size: contain;
            }
          }
        }
      }
    }
  }
  > .charts {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 24px;
    margin-bottom: 24px;
    > div {
      padding: 10px;
      height: 320px;
      background-color: rgba(27, 63, 137, 0.2);
      > .title {
        background: url('@/assets/image/energyStorageSafety/chart-title-bg.png') no-repeat center;
        background-size: cover;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 24px;
        color: #ffffff;
        text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
        height: 50px;
        line-height: 45px;
        padding-left: 55px;
      }
      > div:last-child {
        height: calc(100% - 50px);
      }
    }
  }
  > .query {
    position: absolute;
    top: 0;
    right: 10px;
    display: flex;
    align-items: center;
    > .button-group {
      margin-left: 32px;
      display: inline-flex;
      > div {
        width: 90px;
        padding: 6px 16px;
        border: none;
        color: white;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: rgba(69, 125, 238, 0.2);
        border: 1px solid #244378;
        &.active {
          background-color: rgba(25, 79, 185, 0.75);
          border: 1px solid rgba(51, 119, 255, 0.75);
        }
      }
      > div:first-child {
        border-radius: 16px 0px 0px 16px;
      }
      > div:last-child {
        border-radius: 0px 20px 20px 0px;
      }
    }
  }
}
</style>
