<template>
  <div class="capacity">
    <div class="instructions">
      <div class="title-bg">
        <div class="title-name">容量标定须知</div>
      </div>
      <div class="text">
        <p>1. 本报告仅供项目所有者、管理方或指定方使用，禁止向任何未获授权的第三方转交或引用。</p>
        <p>
          2.
          本报告的技术分析依赖于项目提供的有限数据信息，其质量决定了分析结果的准确性、客观性和完整性；同时，本报告的使用者应充分理解报告的局
          限性，不得对报告内容进行曲解或滥用。
        </p>
        <p>
          3.
          本报告的分析、建议及结论基于项目提供的数据，仅反映被分析储能电站的过往状况。未来运行状况可能受环境、设备老化、管理等因素影响，故本
          报告不对储能电站未来安全性、稳定性及运行风险提供保证。
        </p>
        <p>
          4.
          本报告评估单位不对评估后可能发生的设备故障、人为操作失误、自然灾害等导致的安全事故承担责任，亦不承担项目后续运营、维护或管理中的任
          何责任。项目所有者或运营方应负责和保障项目的日常维护、监控及安全管理。
        </p>
      </div>
    </div>
    <div class="table">
      <div class="title-bg">
        <div class="title-name">容量标定过程管理</div>
        <div class="btns">
          <div>容量标定演示</div>
          <div>开始容量标定</div>
        </div>
      </div>
      <tableList class="!p-0" :data="tableData" :columns="columns" :no-page="false">
        <template #opts>
          <div class="opts-btn">
            <span>查看标定过程</span>
            <span>下载标定数据</span>
            <span>导入标定结果</span>
            <span>查看标定结果</span>
          </div>
        </template>
      </tableList>
    </div>
  </div>
</template>
<script setup lang="ts">
import tableList from '~/components/public/tableList/index.vue'

const columns = [
  { prop: 'index', label: '序号', width: 60, align: 'center' },
  { prop: 'calibrationEquipment', label: '标定设备' },
  { prop: 'calibrationStartTime', label: '标定开始时间' },
  { prop: 'calibrationEndTime', label: '标定结束时间' },
  { prop: 'calibrationCapacity', label: '标定容量(Ah)' },
  { prop: 'status', label: '状态' },
  { prop: 'calibrationOperator', label: '标定操作人' },
  { prop: 'operation', label: '操作', slot: 'opts', width: 500 },
]

const tableData = [
  {
    index: 1,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 17:52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
  {
    index: 2,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 17:52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
  {
    index: 3,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 17:52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
  {
    index: 4,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 17:52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
  {
    index: 5,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 极52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
  {
    index: 6,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 17:52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
  {
    index: 7,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 17:52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
  {
    index: 8,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 17:52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
  {
    index: 9,
    calibrationEquipment: '1-1#电池舱',
    calibrationStartTime: '2025-07-25 17:52:12',
    calibrationEndTime: '2025-07-25 17:52:12',
    calibrationCapacity: 0,
    status: '标定成功',
    calibrationOperator: '张三',
  },
]
</script>
<style scoped lang="scss">
.capacity {
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  display: flex;
  height: 100%;
  > .instructions {
    width: 420px;
    flex-shrink: 0;
    margin-right: 20px;
    padding-left: 16px;
    padding-right: 16px;
    overflow-y: auto;
    background-color: rgba(27, 63, 137, 0.2);
    > .text {
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      line-height: 32px;
      > p {
        margin-bottom: 40px;
      }
    }
  }
  > .table {
    width: 100%;
    height: calc(100% - 80px);
    background: rgba(27, 63, 137, 0.2);
  }
  .title-bg {
    height: 48px;
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    align-items: center;
    background-image: url('@/assets/image/card-title-bg.png');
    background-size: auto 100%;
    background-repeat: no-repeat;
    margin-bottom: 20px;
    > .title-name {
      padding-left: 48px;
      font-weight: bold;
      font-size: 24px;
      color: #ffffff;
      text-shadow: 0px 0px 10px rgba(27, 103, 255, 0.75);
      font-style: italic;
    }
    > .btns {
      display: flex;
      > div {
        margin-left: 12px;
        background: linear-gradient(180deg, rgba(6, 81, 178, 0.85) 2%, rgba(1, 36, 88, 0.85) 100%);
        border: 1px solid #236ddc;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        padding: 8px 24px;
        cursor: pointer;
      }
    }
  }
}

.opts-btn {
  display: flex;
  > span {
    font-weight: 400;
    font-size: 14px;
    color: #00a1ff;
    margin-right: 20px;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
