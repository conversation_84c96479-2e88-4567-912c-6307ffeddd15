<template>
  <div class="h-full bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="气体监测器"></card-title2>
    <div class="p-10px">
      <TableList :columns="columns" :data="data">
        <template #tag="{ row }">
          <el-tag type="danger">{{ row.warning }}</el-tag>
        </template>
      </TableList>
    </div>
  </div>
</template>

<script setup lang="ts">
import CardTitle2 from '@/components/public/cardTitle2.vue'
import TableList from '~/components/public/tableList/index.vue'

const columns = [
  {
    label: '气体监测器',
    prop: 'gasMonitor',
  },
  {
    label: 'H₂浓度(ppm)',
    prop: 'h2Concentration',
  },
  {
    label: 'CO浓度(ppm)',
    prop: 'coConcentration',
  },
  {
    label: '环境温度(℃)',
    prop: 'environmentTemperature',
  },
  {
    label: '当前预警',
    prop: 'warning',
    slot: 'tag'  // 使用插槽名称
  },
]

const data = [
  {
    gasMonitor: '气体监测器1',
    h2Concentration: '10',
    coConcentration: '20',
    environmentTemperature: '30',
    warning: 'CO一级极值严重报警',
  },
]
</script>
