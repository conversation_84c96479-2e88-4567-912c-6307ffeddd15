<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="各簇单体电压曲线">
      <el-date-picker
        class="!w-220px big-screen-select mr-10px"
        v-model="dateRange"
        type="daterange"
        placeholder="请选择"
      />
      <el-select class="!w-240px big-screen-select" v-model="selectedCluster" placeholder="请选择">
        <el-option v-for="item in clusterOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </card-title2>
    <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'
import cardTitle2 from '@/components/public/cardTitle2.vue'

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

interface VoltageData {
  time: string
  values: number[]
}

// 模拟数据 - 每个簇的电压数据
const mockData = (): VoltageData[] => {
  const data: VoltageData[] = []
  const now = new Date()
  const clusterCount = 8 // 8个电池簇

  for (let i = 0; i < 24; i++) {
    const time = new Date(now.getTime() - (23 - i) * 3600 * 1000)
    const values = Array.from({ length: clusterCount }, () => {
      const baseValue = 300 + Math.random() * 200 // 300-500V 之间的基础值
      return Math.round(baseValue * 10) / 10
    })

    data.push({
      time: `${String(time.getHours()).padStart(2, '0')}:00`,
      values,
    })
  }

  return data
}

const dateRange = ref(['2023-01-01 00:00:00', '2023-01-02 00:00:00'])
const selectedCluster = ref('1')
const clusterOptions = Array.from({ length: 8 }, (_, i) => ({
  value: String(i + 1),
  label: `簇${i + 1}电压`,
}))

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  const data = mockData()
  const seriesColors = ['#00B4FF', '#FFD700', '#00E396', '#F86624', '#8B5CF6', '#EC4899', '#10B981', '#F59E0B']

  const option: echarts.EChartsOption = {
    legend: {
      data: clusterOptions.map((_, i) => `簇${i + 1}（V）`),
      top: 10,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      itemWidth: 15,
      itemHeight: 8,
      itemGap: 10,
      type: 'scroll',
      pageIconColor: '#fff',
      pageTextStyle: {
        color: '#fff',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '25%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map((item) => item.time),
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      min: 200,
      max: 600,
      interval: 100,
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
        formatter: '{value}V',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed',
        },
      },
    },
    series: clusterOptions.map((_, index) => ({
      name: `簇${index + 1}（V）`,
      type: 'line',
      smooth: false,
      symbol: 'circle',
      symbolSize: 4,
      showSymbol: false,
      itemStyle: {
        color: seriesColors[index % seriesColors.length],
      },
      lineStyle: {
        width: 1,
        color: seriesColors[index % seriesColors.length],
      },
      data: data.map((item) => item.values[index]),
    })),
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

// 监听选中的簇变化
watch(selectedCluster, () => {
  // 这里可以添加根据选中簇更新图表的逻辑
  console.log('Selected cluster:', selectedCluster.value)
})

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
:deep(.card-title) {
  padding: 10px 20px;
}
</style>
