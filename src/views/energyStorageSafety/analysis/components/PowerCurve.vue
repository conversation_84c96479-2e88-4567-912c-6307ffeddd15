<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]" v-loading="loading">
    <card-title2 title="功率曲线"></card-title2>
    <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import cardTitle2 from '@/components/public/cardTitle2.vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'

const userInfo = useUserInfo()
const props = defineProps({
  batteryCompartmentNum: {
    type: String,
    default: '',
  },
})
const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

const trendData = {
  lineData1: [],
  lineData2: [],
  lineData3: [],
}
const xAxisData = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
]

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  const option: echarts.EChartsOption = {
    legend: {
      data: ['PCS直流侧功率(kW)', 'PCS交流有功功率(kW)', 'PCS交流无功功率(kvar)'],
      top: 10,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 20,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '功率(kW)',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value} kW',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '无功功率(kvar)',
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value} kvar',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: 'PCS直流侧功率(kW)',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00FF00', // 绿色
        },
        lineStyle: {
          width: 1,
          color: '#00FF00',
        },
        data: trendData.lineData1,
      },
      {
        name: 'PCS交流有功功率(kW)',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00B4FF', // 蓝色
        },
        lineStyle: {
          width: 1,
          color: '#00B4FF',
        },
        data: trendData.lineData2,
      },
      {
        name: 'PCS交流无功功率(kvar)',
        type: 'line',
        yAxisIndex: 1, // 使用右侧Y轴
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#FFD700', // 黄色
        },
        lineStyle: {
          width: 1,
          color: '#FFD700',
        },
        data: trendData.lineData3,
      },
    ],
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

const loading = ref(false)
const getData = async () => {
  loading.value = true
  try {
    const res = await $API.post({
      url: '/comprehensiveDataAnalysis/electricityStat',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
        batteryCompartmentNum: props.batteryCompartmentNum,
      },
    })
    if ((res as any).code === 'success') {
      trendData.lineData1 = res.data[0].list.map((v) => parseFloat(v.v))
      trendData.lineData2 = res.data[1].list.map((v) => parseFloat(v.v))
      trendData.lineData3 = res.data[2].list.map((v) => parseFloat(v.v))
      initChart()
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getData()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
:deep(.card-title) {
  padding: 10px 20px;
}
</style>
