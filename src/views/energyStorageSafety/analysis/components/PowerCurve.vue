<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="功率曲线"></card-title2>
    <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import cardTitle2 from '@/components/public/cardTitle2.vue'

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

interface PowerData {
  time: string
  dcPower: number
  acActivePower: number
  acReactivePower: number
}

// 模拟数据
const mockData = (): PowerData[] => {
  const data: PowerData[] = []
  const now = new Date()

  for (let i = 0; i < 24; i++) {
    const time = new Date(now.getTime() - (23 - i) * 3600 * 1000)
    const baseValue = 500 + Math.random() * 1000 // 基础功率值
    data.push({
      time: `${String(time.getHours()).padStart(2, '0')}:00`,
      dcPower: Math.round(baseValue * (0.9 + Math.random() * 0.2)),
      acActivePower: Math.round(baseValue * (0.85 + Math.random() * 0.3)),
      acReactivePower: Math.round(baseValue * 0.3 * (0.8 + Math.random() * 0.4)), // 无功功率约为有功的30%
    })
  }

  return data
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  const data = mockData()

  const option: echarts.EChartsOption = {
    legend: {
      data: ['PCS直流侧功率(kW)', 'PCS交流有功功率(kW)', 'PCS交流无功功率(kvar)'],
      top: 10,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 20,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map((item) => item.time),
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '功率(kW)',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value} kW',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '无功功率(kvar)',
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value} kvar',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: 'PCS直流侧功率(kW)',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00FF00', // 绿色
        },
        lineStyle: {
          width: 1,
          color: '#00FF00',
        },
        data: data.map((item) => item.dcPower),
      },
      {
        name: 'PCS交流有功功率(kW)',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00B4FF', // 蓝色
        },
        lineStyle: {
          width: 1,
          color: '#00B4FF',
        },
        data: data.map((item) => item.acActivePower),
      },
      {
        name: 'PCS交流无功功率(kvar)',
        type: 'line',
        yAxisIndex: 1, // 使用右侧Y轴
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#FFD700', // 黄色
        },
        lineStyle: {
          width: 1,
          color: '#FFD700',
        },
        data: data.map((item) => item.acReactivePower),
      },
    ],
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
:deep(.card-title) {
  padding: 10px 20px;
}
</style>
