<template>
  <div class="data-content h-[calc(100%-60px)] overflow-y-auto grid gap-20px mt-20px">
    <div class="row-span-1 bg-[rgba(27,63,137,0.2)]">
      <card-title2 title="气体监测器"></card-title2>
      <div class="search-box p-10px">
        <el-date-picker
          class="!w-220px flex-none big-screen-select mr-10px"
          v-model="value2"
          type="daterange"
          placeholder="请选择"
        />
        <el-select class="!w-240px big-screen-select" v-model="value3" placeholder="请选择">
          <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="mt-10px">
          <el-select class="!w-full big-screen-select" multiple v-model="value1" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
    </div>
    <div class="row-span-3 bg-[rgba(27,63,137,0.2)]">
      <card-title2 title="测点信息曲线"></card-title2>
      <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import cardTitle2 from '@/components/public/cardTitle2.vue'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null
const value1 = ref([])
const value2 = ref('')
const value3 = ref('1')

// 定义数据类型
interface GasData {
  time: string
  [key: string]: string | number
}

interface GasType {
  name: string
  unit: string
  min: number
  max: number
}

// 模拟气体监测数据
const generateMockData = () => {
  const data: GasData[] = []
  const now = new Date()
  const types: GasType[] = [
    { name: '一氧化碳', unit: 'ppm', min: 0, max: 50 },
    { name: '氢气', unit: '%LEL', min: 0, max: 100 },
    { name: '甲烷', unit: '%LEL', min: 0, max: 100 },
    { name: '温度', unit: '°C', min: 15, max: 35 },
    { name: '湿度', unit: '%RH', min: 20, max: 80 },
  ]

  // 生成24小时数据
  for (let i = 0; i < 24; i++) {
    const time = new Date(now.getTime() - (23 - i) * 3600 * 1000)
    const timeStr = `${String(time.getHours()).padStart(2, '0')}:00`
    
    const item: GasData = { time: timeStr }
    types.forEach((type) => {
      item[type.name] = (Math.random() * (type.max - type.min) + type.min).toFixed(1)
    })
    
    data.push(item)
  }
  
  return { data, types }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  const { data, types } = generateMockData()
  chart = echarts.init(chartRef.value)

  const option: echarts.EChartsOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: types.map(item => item.name),
      textStyle: {
        color: '#fff'
      },
      right: 10,
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: false
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map(item => item.time),
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { show: false }
    },
    series: types.map((type, index) => ({
      name: type.name,
      type: 'line',
      smooth: true,
      showSymbol: false,
      lineStyle: {
        width: 2,
        color: ['#00B4FF', '#FFD700', '#00E396', '#F86624', '#8B5CF6'][index % 5],
      },
      data: data.map((item) => Number(item[type.name])),
    }))
  }

  chart.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
const options = [
  {
    value: '1',
    label: '气体监测器',
  },
  {
    value: '2',
    label: '气体监测器',
  },
]
const options2 = [
  {
    value: '1',
    label: '测点间隔5分钟',
  },
  {
    value: '2',
    label: '测点间隔10分钟',
  },
  {
    value: '3',
    label: '测点间隔15分钟',
  },
  {
    value: '4',
    label: '测点间隔30分钟',
  },
  {
    value: '5',
    label: '测点间隔1小时',
  },
]
</script>
