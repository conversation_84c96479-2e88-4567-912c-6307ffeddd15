<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]" v-loading="loading">
    <card-title2 title="运行统计"></card-title2>
    <div class="flex justify-between flex-wrap">
      <div class="w-1/2 grid grid-cols-2 gap-20px gap-y-40px p-20px py-30px">
        <div class="item-1">
          <div class="text-title weight-400 text-20px text-[#FFAA00]">{{ batteryStatus.totalActivePower }}kw</div>
          <div class="text-14px mt-12px">总有功功率</div>
        </div>
        <div class="item-1">
          <div class="text-title weight-400 text-20px text-[#FFAA00]">{{ batteryStatus.totalDischargePower }}kw</div>
          <div class="text-14px mt-12px">充放电功率</div>
        </div>
        <div class="item-3">
          <div class="text-title weight-400 text-20px text-[#FFAA00]">{{ batteryStatus.totalCharge }}kw</div>

          <div class="text-14px mt-12px">总充电量</div>
        </div>
        <div class="item-4">
          <div class="text-title weight-400 text-20px text-[#FFAA00]">{{ batteryStatus.totalDischarge }}kw</div>
          <div class="text-14px mt-12px">总放电量</div>
        </div>
      </div>
      <div class="w-1/2 flex">
        <div class="legend">
          <div class="flex items-center">
            <div class="block bg-[#1461EA]"></div>
            <span>采集值</span>
          </div>
          <div class="flex items-center">
            <div class="block bg-[#01DFE0]"></div>
            <span>评估值</span>
          </div>
        </div>
        <div class="w-[calc(100%-140px)] flex items-center ml-40px justify-between">
          <div>
            <el-progress
              type="circle"
              :width="140"
              :stroke-width="10"
              color="#1461EA"
              :percentage="90"
              status="success"
            >
              <el-progress
                :width="100"
                :stroke-width="10"
                color="#01DFE0"
                type="circle"
                :percentage="80"
                status="success"
              >
                <span class="weight-400 text-20px text-[#fff]">总SOC</span>
              </el-progress>
            </el-progress>
            <div class="text-14px mt-12px text-center">
              采集值：<span class="text-[#FFAA00] text-[20px]">{{ batteryStatus.soc }}</span
              >%
            </div>
            <div class="text-14px mt-12px text-center">
              评估值：<span class="text-[#FFAA00] text-[20px]">{{ batteryStatus.socEvaluation }}</span
              >%
            </div>
          </div>
          <div>
            <el-progress
              type="circle"
              :width="140"
              :stroke-width="10"
              color="#1461EA"
              :percentage="90"
              status="success"
            >
              <el-progress
                :width="100"
                :stroke-width="10"
                color="#01DFE0"
                type="circle"
                :percentage="80"
                status="success"
              >
                <span class="weight-400 text-20px text-[#fff]">总SOH</span>
              </el-progress>
            </el-progress>
            <div class="text-14px mt-12px text-center">
              采集值：<span class="text-[#FFAA00] text-[20px]">{{ batteryStatus.soh }}</span
              >%
            </div>
            <div class="text-14px mt-12px text-center">
              评估值：<span class="text-[#FFAA00] text-[20px]">{{ batteryStatus.sohEvaluation }}</span
              >%
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import cardTitle2 from '@/components/public/cardTitle2.vue'
import { onMounted, ref } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'

const props = defineProps({
  batteryCompartmentNum: {
    type: String,
    default: '',
  },
})

interface BatteryStatus {
  totalActivePower?: number | string
  totalCharge?: number | string
  totalDischarge?: number | string
  totalDischargePower?: number | string
  soc?: number | string
  soh?: number | string
  socEvaluation?: number | string
  sohEvaluation?: number | string
}
const userInfo = useUserInfo()
const loading = ref(false)
const batteryStatus = ref<BatteryStatus>({})
const getSituation = async () => {
  loading.value = true
  try {
    const res = await $API.post({
      url: '/comprehensiveDataAnalysis/overview',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
        batteryCompartmentNum: props.batteryCompartmentNum,
      },
    })
    if ((res as any).code === 'success') {
      batteryStatus.value = res.data
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getSituation()
})
</script>
<style lang="scss" scoped>
.item-1 {
  height: 64px;
  padding-left: 74px;
  padding-top: 6px;
  background: url('@/assets/image/analysis/1.png') no-repeat;
  background-size: 74px auto;
}
.item-3 {
  height: 64px;
  padding-left: 74px;
  background: url('@/assets/image/analysis/3.png') no-repeat;
  background-size: 74px auto;
}
.item-4 {
  height: 64px;
  padding-left: 74px;
  background: url('@/assets/image/analysis/4.png') no-repeat;
  background-size: 74px auto;
}
.legend {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 20px;
  .block {
    width: 12px;
    height: 12px;
    margin-right: 10px;
  }
}
</style>
