<template>
  <div class="row-span-3 w-918px h-full bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="实时单体电压信息"></card-title2>
    <div class="p-10px">
      <table-list :columns="columns" :data="data"></table-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import CardTitle2 from '@/components/public/cardTitle2.vue'
import tableList from '@/components/public/tableList/index.vue'

const columns = [
  {
    label: '簇',
    prop: 'clusterId',
  },
  {
    label: '簇电压（V）',
    prop: 'clusterVoltage',
  },
  {
    label: '最高单体电压(mV)',
    prop: 'maxVoltage',
  },
  {
    label: '最高单体电压位置',
    prop: 'maxVoltagePosition',
  },
  {
    label: '最低单体电压mV',
    prop: 'minVoltage',
  },
  {
    label: '最低单体电压位置',
    prop: 'minVoltagePosition',
  },
  {
    label: '电压极差（mV）',
    prop: 'voltageRange',
  },
]
const data = [
  {
    clusterId: '1',
    clusterVoltage: '12.3',
    maxVoltage: '12.3',
    maxVoltagePosition: '1',
    minVoltage: '12.3',
    minVoltagePosition: '1',
    voltageRange: '12.3',
  },
]
</script>
<style scoped lang="scss">
.custom-table {
  --el-table-header-bg-color: rgba(66, 111, 191, 0.3);
  --el-table-header-text-color: #fff;
  --el-table-tr-bg-color: transparent;
}
</style>
