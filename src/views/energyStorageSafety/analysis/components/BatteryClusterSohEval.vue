<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="电池簇SOH动态评估">
      <el-select
        class="!w-240px big-screen-select"
        :model-value="modelValue"
        @update:model-value="$emit('update:modelValue', $event)"
        placeholder="请选择"
      >
        <el-option v-for="item in batteryOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </card-title2>
    <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import cardTitle2 from '@/components/public/cardTitle2.vue'

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

interface SocData {
  time: string
  actual: number
  eval: number
}

// 模拟数据
const mockData = (): SocData[] => {
  const data: SocData[] = []
  const now = new Date()

  for (let i = 0; i < 24; i++) {
    const time = new Date(now.getTime() - (23 - i) * 3600 * 1000)
    const baseValue = 30 + Math.random() * 40 // 30-70% 之间的基础值
    data.push({
      time: `${String(time.getHours()).padStart(2, '0')}:00`,
      actual: Math.round(baseValue + (Math.random() * 10 - 5)), // 实际值在基础值上下浮动5%
      eval: Math.round(baseValue), // 评估值
    })
  }

  return data
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  const data = mockData()

  const option: echarts.EChartsOption = {
    legend: {
      data: ['实际值（%）', '评估值（%）'],
      top: 10,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 20,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map((item) => item.time),
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      interval: 20,
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '实际值（%）',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00B4FF',
        },
        lineStyle: {
          width: 1,
          color: '#00B4FF',
        },
        data: data.map((item) => item.actual),
      },
      {
        name: '评估值（%）',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#FFD700',
        },
        lineStyle: {
          width: 1,
          color: '#FFD700',
        },
        data: data.map((item) => item.eval),
      },
    ],
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
:deep(.card-title) {
  padding: 10px 20px;
}
</style>
