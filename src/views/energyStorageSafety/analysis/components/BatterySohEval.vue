<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]" v-loading="loading">
    <card-title2 title="电池舱SOH动态评估"></card-title2>
    <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import cardTitle2 from '@/components/public/cardTitle2.vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'

const userInfo = useUserInfo()
const props = defineProps({
  batteryCompartmentNum: {
    type: String,
    default: '',
  },
})
const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

const trendData = {
  lineData1: [],
  lineData2: [],
}
const xAxisData = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
]

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  const option: echarts.EChartsOption = {
    legend: {
      data: ['实际值（%）', '评估值（%）'],
      top: 10,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 20,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      interval: 20,
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '实际值（%）',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00B4FF',
        },
        lineStyle: {
          width: 1,
          color: '#00B4FF',
        },
        data: trendData.lineData1,
      },
      {
        name: '评估值（%）',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#FFD700',
        },
        lineStyle: {
          width: 1,
          color: '#FFD700',
        },
        data: trendData.lineData2,
      },
    ],
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

const loading = ref(false)
const getData = async () => {
  loading.value = true
  try {
    const res = await $API.post({
      url: '/comprehensiveDataAnalysis/sohDynamicAnalysis',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
        batteryCompartmentNum: props.batteryCompartmentNum,
      },
    })
    if ((res as any).code === 'success') {
      trendData.lineData1 = res.data[0].list.map((v) => parseFloat(v.v))
      trendData.lineData2 = res.data[1].list.map((v) => parseFloat(v.v))
      initChart()
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getData()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
:deep(.card-title) {
  padding: 10px 20px;
}
</style>
