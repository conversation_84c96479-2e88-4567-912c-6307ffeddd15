<template>
  <div class="h-full bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="电池舱SOH动态评估"></card-title2>
    <div class="p-10px">
      <TableList :columns="columns" :data="data" />
    </div>
  </div>
</template>
<script setup lang="ts">
import TableList from '@/components/public/tableList/index.vue'
import cardTitle2 from '@/components/public/cardTitle2.vue'
const columns = [
  {
    label: '设备编号',
    prop: 'clusterId',
  },
  {
    label: '所在电站',
    prop: 'clusterVoltage',
  },
  {
    label: '设备类型',
    prop: 'maxVoltage',
  },
  {
    label: '设备位置',
    prop: 'maxVoltagePosition',
  },
  {
    label: '运行状态',
    prop: 'minVoltage',
  },
  {
    label: '操作',
    prop: 'minVoltagePosition',
  },
]
const data = [
  {
    clusterId: '1',
    clusterVoltage: '12.3',
    maxVoltage: '12.3',
    maxVoltagePosition: '1',
    minVoltage: '12.3',
    minVoltagePosition: '1',
    voltageRange: '12.3',
  },
]
</script>
