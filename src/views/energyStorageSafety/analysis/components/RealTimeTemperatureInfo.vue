<template>
  <div class="row-span-2 w-918px h-full bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="实时单体温度信息"></card-title2>
    <div class="p-10px">
      <table-list :columns="columns" :data="data"></table-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import CardTitle2 from '@/components/public/cardTitle2.vue'
import tableList from '@/components/public/tableList/index.vue'
const columns = [
  {
    label: '簇',
    prop: 'clusterId',
  },
  {
    label: '最高单体温度(°C)',
    prop: 'maxTemp',
  },
  {
    label: '最高单体温度位置',
    prop: 'maxTempPosition',
  },
  {
    label: '最低单体温度(°C)',
    prop: 'minTemp',
  },
  {
    label: '最低单体温度位置',
    prop: 'minTempPosition',
  },
  {
    label: '温度极差(°C)',
    prop: 'tempRange',
  },
]

// 模拟数据
const data = Array.from({ length: 8 }, (_, i) => ({
  clusterId: String(i + 1),
  maxTemp: (35 + Math.random() * 5).toFixed(1),
  maxTempPosition: Math.floor(Math.random() * 20) + 1,
  minTemp: (25 + Math.random() * 5).toFixed(1),
  minTempPosition: Math.floor(Math.random() * 20) + 1,
  tempRange: (8 + Math.random() * 4).toFixed(1),
}))
</script>
