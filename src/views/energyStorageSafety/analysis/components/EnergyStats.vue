<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="电量统计"></card-title2>
    <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import cardTitle2 from '@/components/public/cardTitle2.vue'

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

interface EnergyData {
  time: string
  chargeCurrent: number
  dischargeCurrent: number
  lossRate: number
}

// 模拟数据
const mockData = (): EnergyData[] => {
  const data: EnergyData[] = []
  const today = new Date()

  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)
    const day = date.getDate()

    const baseValue = 1000 + Math.random() * 1000
    data.push({
      time: `${day}日`,
      chargeCurrent: Math.round(baseValue * (0.8 + Math.random() * 0.4)),
      dischargeCurrent: Math.round(baseValue * (0.7 + Math.random() * 0.3)),
      lossRate: Number((2 + Math.random() * 3).toFixed(1)), // 2-5% 的损耗率
    })
  }

  return data
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  const data = mockData()

  const option: echarts.EChartsOption = {
    legend: {
      data: ['日充电电量(kWh)', '日放电电量(kWh)', '损耗率(%)'],
      top: 10,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 14,
      },
      itemGap: 20,
      itemWidth: 20,
      itemHeight: 10,
      selectedMode: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    grid: {
      left: '5%',
      right: '8%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      min: 0,
      max: 6,
      data: data.map((item) => item.time),
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
        interval: Math.floor(data.length / 6), // 显示部分日期标签
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '电量(kWh)',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value} kWh',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '损耗率(%)',
        position: 'right',
        min: 0,
        max: 10,
        interval: 2,
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value}%',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '日充电电量(kWh)',
        type: 'bar',
        barWidth: 15,
        barGap: '50%',
        barCategoryGap: '20%',
        itemStyle: {
          color: '#FF6B6B', // 红色
        },
        data: data.map((item) => item.chargeCurrent),
      },
      {
        name: '日放电电量(kWh)',
        type: 'bar',
        barWidth: 15,
        barGap: '50%',
        barCategoryGap: '20%',
        itemStyle: {
          color: '#4D96FF', // 蓝色
        },
        data: data.map((item) => item.dischargeCurrent),
      },
      {
        name: '损耗率(%)',
        type: 'line',
        yAxisIndex: 1, // 使用右侧Y轴
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#FFD700', // 黄色
        },
        lineStyle: {
          width: 2,
          color: '#FFD700',
        },
        data: data.map((item) => item.lossRate),
      },
    ],
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
:deep(.card-title) {
  padding: 10px 20px;
}
</style>
