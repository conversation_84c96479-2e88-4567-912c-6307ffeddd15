<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]" v-loading="loading">
    <card-title2 title="电量统计"></card-title2>
    <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import cardTitle2 from '@/components/public/cardTitle2.vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'

const userInfo = useUserInfo()
const props = defineProps({
  batteryCompartmentNum: {
    type: String,
    default: '',
  },
})
const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

const trendData = {
  barData1: [],
  barData2: [],
  lineData: [],
}
const xAxisData = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
]

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  const option: echarts.EChartsOption = {
    legend: {
      data: ['日充电电量(kWh)', '日放电电量(kWh)', '损耗率(%)'],
      top: 10,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 14,
      },
      itemGap: 20,
      itemWidth: 20,
      itemHeight: 10,
      selectedMode: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    grid: {
      left: '5%',
      right: '8%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      min: 0,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '电量(kWh)',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value} kWh',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '损耗率(%)',
        position: 'right',
        min: 0,
        interval: 2,
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12,
          formatter: '{value}%',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '日充电电量(kWh)',
        type: 'bar',
        barWidth: 15,
        barGap: '50%',
        barCategoryGap: '20%',
        itemStyle: {
          color: '#FF6B6B', // 红色
        },
        data: trendData.barData1,
      },
      {
        name: '日放电电量(kWh)',
        type: 'bar',
        barWidth: 15,
        barGap: '50%',
        barCategoryGap: '20%',
        itemStyle: {
          color: '#4D96FF', // 蓝色
        },
        data: trendData.barData2,
      },
      {
        name: '损耗率(%)',
        type: 'line',
        yAxisIndex: 1, // 使用右侧Y轴
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#FFD700', // 黄色
        },
        lineStyle: {
          width: 2,
          color: '#FFD700',
        },
        data: trendData.lineData,
      },
    ],
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

const loading = ref(false)
const getData = async () => {
  loading.value = true
  try {
    const res = await $API.post({
      url: '/comprehensiveDataAnalysis/electricityStat',
      data: {
        unitId: userInfo.value.unitId,
        superviseId: userInfo.value.orgCode,
        batteryCompartmentNum: props.batteryCompartmentNum,
      },
    })
    if ((res as any).code === 'success') {
      trendData.barData1 = res.data[0].list.map((v) => parseFloat(v.v))
      trendData.barData2 = res.data[1].list.map((v) => parseFloat(v.v))
      trendData.lineData = res.data[2].list.map((v) => parseFloat(v.v))
      initChart()
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getData()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
:deep(.card-title) {
  padding: 10px 20px;
}
</style>
