<template>
  <div class="w-918px h-280px bg-[rgba(27,63,137,0.2)]">
    <card-title2 title="簇内最大单体压差变化曲线">
      <el-date-picker
        class="!w-220px big-screen-select mr-10px"
        v-model="dateRange"
        type="daterange"
        placeholder="请选择"
      />
      <el-select class="!w-240px big-screen-select" v-model="modelValue" placeholder="请选择">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </card-title2>
    <div ref="chartRef" class="w-full h-[calc(100%-40px)]"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import CardTitle2 from '@/components/public/cardTitle2.vue'

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

interface VoltageDiffData {
  time: string
  actual: number
  eval: number
}

// 模拟数据
const mockData = (): VoltageDiffData[] => {
  const data: VoltageDiffData[] = []
  const now = new Date()

  for (let i = 0; i < 24; i++) {
    const time = new Date(now.getTime() - (23 - i) * 3600 * 1000)
    const baseValue = 10 + Math.random() * 20 // 10-30mV 之间的基础值
    data.push({
      time: `${String(time.getHours()).padStart(2, '0')}:00`,
      actual: Math.round((baseValue + (Math.random() * 5 - 2.5)) * 10) / 10, // 实际值在基础值上下浮动2.5mV
      eval: Math.round(baseValue * 10) / 10, // 评估值
    })
  }

  return data
}

const dateRange = ref(['2023-01-01 00:00:00', '2023-01-02 00:00:00'])
const modelValue = ref('1')
const options = [
  {
    value: '1',
    label: '簇内最大单体压差变化曲线',
  },
  {
    value: '2',
    label: '簇内最大单体压差变化曲线',
  },
]

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  const data = mockData()

  const option: echarts.EChartsOption = {
    legend: {
      data: ['实际值（mV）', '评估值（mV）'],
      top: 10,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 20,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map((item) => item.time),
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 50,
      interval: 10,
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12,
        formatter: '{value}mV',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '实际值（mV）',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00B4FF',
        },
        lineStyle: {
          width: 1,
          color: '#00B4FF',
        },
        data: data.map((item) => item.actual),
      },
      {
        name: '评估值（mV）',
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#FFD700',
        },
        lineStyle: {
          width: 1,
          color: '#FFD700',
        },
        data: data.map((item) => item.eval),
      },
    ],
  }

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
:deep(.card-title) {
  padding: 10px 20px;
}
</style>
