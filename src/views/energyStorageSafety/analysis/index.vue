<template>
  <div class="big-screen-analysis h-full">
    <div class="top-line flex justify-between">
      <div class="flex">
        <el-select class="!w-240px mr-20px big-screen-select" v-model="energyValue" placeholder="请选择电站">
          <el-option v-for="item in energyOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select class="!w-240px big-screen-select" v-model="batteryValue" placeholder="请选择电池仓">
          <el-option v-for="item in batteryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-segmented class="big-screen-segmented" v-model="value" :options="segmentedOptions" @change="handleClick" />
      </div>
    </div>
    <!-- 综合信息 -->
    <div
      v-if="value === '1'"
      class="data-content h-[calc(100%-60px)] overflow-y-auto grid grid-cols-2 gap-20px mt-20px"
    >
      <operation-stats :battery-compartment-num="batteryValue" />
      <battery-soc-eval :battery-compartment-num="batteryValue" />
      <power-curve :battery-compartment-num="batteryValue" />
      <battery-soh-eval :battery-compartment-num="batteryValue" />
      <energy-stats :battery-compartment-num="batteryValue" />
      <battery-cluster-soh-eval
        :battery-compartment-num="batteryValue"
        :battery-options="batteryOptions"
        :model-value="batteryValue"
        @update:model-value="batteryValue = $event"
      />
    </div>
    <!-- 电压信息 -->
    <div
      v-else-if="value === '2'"
      class="data-content h-[calc(100%-60px)] overflow-y-auto grid grid-cols-2 gap-20px mt-20px"
    >
      <real-time-voltage-info />
      <voltage-trend />
      <max-voltage-diff-curve />
      <cluster-voltage-curve />
    </div>
    <!-- 温度信息 -->
    <div
      v-else-if="value === '3'"
      class="data-content h-[calc(100%-60px)] overflow-y-auto grid grid-cols-2 gap-20px mt-20px"
    >
      <real-time-temperature-info />
      <max-temperature-diff-curve />
      <cluster-temperature-curve />
    </div>
    <!-- 气体信息 -->
    <div
      v-else-if="value === '4'"
      class="data-content h-[calc(100%-60px)] overflow-y-auto grid grid-rows-2 gap-20px mt-20px"
    >
      <gas-monitor-info />
      <gas-monitor-chart />
    </div>
    <!-- 设备信息 -->
    <div v-else-if="value === '5'" class="data-content h-[calc(100%-60px)] mt-20px">
      <device-info />
    </div>
    <!-- 自定义查询 -->
    <gas-monitor v-else-if="value === '6'" />
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'
import RealTimeTemperatureInfo from './components/RealTimeTemperatureInfo.vue'
import MaxTemperatureDiffCurve from './components/MaxTemperatureDiffCurve.vue'
import ClusterTemperatureCurve from './components/ClusterTemperatureCurve.vue'
import GasMonitorInfo from './components/GasMonitorInfo.vue'
import GasMonitorChart from './components/GasMonitorChart.vue'
import RealTimeVoltageInfo from './components/RealTimeVoltageInfo.vue'
import VoltageTrend from './components/VoltageTrend.vue'
import MaxVoltageDiffCurve from './components/MaxVoltageDiffCurve.vue'
import ClusterVoltageCurve from './components/ClusterVoltageCurve.vue'
import OperationStats from './components/OperationStats.vue'
import BatterySocEval from './components/BatterySocEval.vue'
import PowerCurve from './components/PowerCurve.vue'
import BatterySohEval from './components/BatterySohEval.vue'
import EnergyStats from './components/EnergyStats.vue'
import BatteryClusterSohEval from './components/BatteryClusterSohEval.vue'
import DeviceInfo from './components/DeviceInfo.vue'
import GasMonitor from './components/GasMonitor.vue'

const userInfo = useUserInfo()
// 电站选项
const energyOptions = ref<any[]>([])

// 电池仓选项
const batteryOptions = ref<any[]>([])

const segmentedOptions = [
  {
    value: '1',
    label: '综合信息',
  },
  {
    value: '2',
    label: '电压信息',
  },
  {
    value: '3',
    label: '温度信息',
  },
  {
    value: '4',
    label: '气体信息',
  },
  {
    value: '5',
    label: '设备信息',
  },
  {
    value: '6',
    label: '自定义查询',
  },
]
const energyValue = ref('1')
const batteryValue = ref('eqm000')
const value = ref('1')
const handleClick = (tab: string) => {
  console.log(tab)
}

const getSituation = async () => {
  const res = await $API.post({
    url: '/safetySituation/stationSafetySituation',
    data: {
      unitId: userInfo.value.unitId,
      superviseId: userInfo.value.orgCode,
    },
  })
  if ((res as any).code === 'success') {
    energyOptions.value = res.data.map((v) => {
      return {
        label: v.unitName,
        value: v.unitId,
      }
    })
  }
}

onMounted(() => {
  getSituation()
})
</script>
<style lang="scss" scoped></style>
