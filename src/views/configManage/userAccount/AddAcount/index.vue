<template>
  <div class="reportAdd">
    <div class="form-title">
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        status-icon
        :disabled="isDiabled"
      >
        <el-form-item label="姓名" prop="reportName">
          <el-input placeholder="请输入姓名" v-model="ruleForm.reportName" maxlength="50" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="手机号" prop="reportName">
          <el-input placeholder="请输入手机号" v-model="ruleForm.reportName" maxlength="50" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="所属机构" prop="projectId">
          <el-select
            class="w-full"
            v-model="ruleForm.projectId"
            placeholder="请选择"
            clearable
            filterable
            @change="handleProjectChange"
          >
            <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户角色" prop="reportName">
          <el-radio-group v-model="ruleForm.roleVal">
            <el-radio v-for="option in roleOptions" :key="option.value" :label="option.value">
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="isEdit !== 2" class="form-footer">
      <el-button @click="resetForm(ruleFormRef)" class="big-screen-button">取消</el-button>
      <el-button type="primary" @click="submitForm(ruleFormRef)" :loading="saveLoading" class="big-screen-button">
        确认
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits, ref, reactive, watchEffect, onMounted } from 'vue'
import { FormInstance, ElMessage } from 'element-plus'
// import { postaddReportAPI, postupdateReportAPI } from '@/api/report'
// import { postQuerryListAPI } from '@/api/projectManagement'
const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  isEdit: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['close'])
const saveLoading = ref(false)
const projectList = ref<any[]>([])
const isDiabled = ref(false)
const ruleFormRef = ref<FormInstance>()
const ruleForm: any = ref({
  id: '', // 报告id
  projectId: '', // 项目id
  projectName: '', // 项目名称
  reportName: '', // 报告名称
  roleVal: '',
})

const roleOptions = ref([
  { label: '超级管理员', value: '0' },
  { label: '管理员', value: '1' },
  { label: '普通用户', value: '2' },
  { label: '访客', value: '3' },
])
const rules = reactive({
  projectId: [
    {
      required: true,
      message: '请选择项目名称',
      trigger: 'blur',
    },
  ],
  reportName: [
    {
      required: true,
      message: '请输入报告名称',
      trigger: 'blur',
    },
  ],
})

const handleProjectChange = (val) => {
  console.log('🚀 ~ val:', val)
  const project = projectList.value.find((item) => item.id === val)

  ruleForm.value.projectName = project?.projectName || ''
}

const projectData = async () => {
  // const res = await postQuerryListAPI({})
  const res = [
    { id: '1', projectName: '项目一' },
    { id: '2', projectName: '项目二' },
    { id: '3', projectName: '项目三' },
  ]

  projectList.value = res || []
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  try {
    const valid = await formEl.validate()

    if (!valid) return
    saveLoading.value = true
    const params = {
      detectStartTime: ruleForm.value.monitorDate[0],
      detectEndTime: ruleForm.value.monitorDate[1],
      projectId: ruleForm.value.projectId,
      reportName: ruleForm.value.reportName,
      description: ruleForm.value.description,
    }

    if (props.isEdit === 1) {
      const postupdateReportAPI = (p) =>
        new Promise((resolve) => {
          setTimeout(() => {
            resolve(p)
          }, 1000)
        })
      postupdateReportAPI({
        ...params,
        id: ruleForm.value.id,
      })
        .then(() => {
          ElMessage.success('编辑成功')
          emit('close', true)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          saveLoading.value = false
        })
    } else {
      const postaddReportAPI = (p) =>
        new Promise((resolve) => {
          setTimeout(() => {
            resolve(p)
          }, 1000)
        })
      postaddReportAPI(params)
        .then(() => {
          ElMessage.success('新增成功')
          emit('close', true)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          saveLoading.value = false
        })
    }
    formEl.resetFields()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  emit('close')
}

watchEffect(() => {
  // console.log('🚀 ~ watchEffect ~ props:', props);
  if (props.isEdit === 1) {
    isDiabled.value = false
    const { id, projectId, projectName, reportName, description, detectStartTime, detectEndTime } = props.info

    ruleForm.value = {
      id,
      projectId,
      projectName,
      reportName,
      description,
      monitorDate: detectStartTime && detectEndTime ? [detectStartTime, detectEndTime] : [],
    }
  } else if (props.isEdit === 0) {
    ruleForm.value = {
      id: '',
      projectId: '',
      projectName: '',
      reportName: '',
      description: '',
      monitorDate: [],
    }
    isDiabled.value = false
  } else if (props.isEdit === 2) {
    isDiabled.value = true
    ruleForm.value = {
      ...props.info,
    }
  }
})

onMounted(projectData)
</script>

<style lang="scss" scoped>
:deep(.el-date-editor .el-range__close-icon) {
  width: 16px;
  height: 16px;
}

.reportAdd {
  height: calc(100vh - 220px);
  position: relative;
  margin-top: 10px;
  overflow-y: auto;
  overflow-x: hidden;

  .form-footer {
    position: fixed;
    bottom: 30px;
    right: 40px;
    z-index: 99;
  }
}
</style>
