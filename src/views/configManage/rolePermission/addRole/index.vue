<template>
  <div class="form-box">
    <div class="form-title">
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        status-icon
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model.trim="ruleForm.roleName"
            placeholder="请输入角色名称"
            :disabled="isEdit === 'detail'"
            show-word-limit
            maxlength="15"
          />
        </el-form-item>
        <el-form-item label="角色权限" class="is-required">
          <div class="">web网页端</div>
          <div class="auth-tree">
            <el-tree
              class="tree-scroll"
              v-loading="showWebTreeLoading"
              empty-text="暂无数据"
              show-checkbox
              :expand-on-click-node="false"
              node-key="id"
              ref="webTreeRef"
              :props="treeProps"
              :data="webTreeData"
              @check-change="handleWebCheckChange"
            >
              <template #empty>
                <el-empty description="暂无数据" :size="100" />
              </template>
            </el-tree>
          </div>
          <div class="error-message" v-if="showPermissionErrorMessage">请勾选角色权限</div>
        </el-form-item>
      </el-form>
    </div>
    <div class="form-footer" v-if="isEdit !== 'detail'">
      <el-button @click="resetForm(ruleFormRef)" class="big-screen-button">取消</el-button>
      <el-button type="primary" @click="submitForm(ruleFormRef)" :loading="saveLoading" class="big-screen-button">
        确认
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch, nextTick, computed } from 'vue'
import { FormInstance, ElMessage } from 'element-plus'
// import { getAuthTree, addAuth, updateAuth } from '@/api/auth.ts'
import { useUserInfo } from '~/store'
const ui = useUserInfo()
console.log('ui', ui)
const props = defineProps({
  info: {
    type: Object,
    default: () => {
      return {}
    },
  },
  isEdit: {
    type: String,
    default: 'add',
  },
})

const webTreeRef = ref<any>(null)
const appTreeRef = ref<any>(null)
const treeProps = ref({
  label: 'resName',
  children: 'children',
  disabled: 'isDisable',
})

function recursiveTreeData(data: any) {
  data.forEach((item: any) => {
    item.isDisable = true
    if (item.children && item.children.length > 0) {
      recursiveTreeData(item.children)
    }
  })
}

interface TreeNode {
  id: string
  resName: string
  children?: TreeNode[]
}

const webTreeData = ref<TreeNode[]>([])
const showWebTreeLoading = ref(false)
const showAppTreeLoading = ref(false)

const getAuthTreeList = async () => {
  console.log('获取权限树数据开始')
  showWebTreeLoading.value = true
  showAppTreeLoading.value = true

  try {
    /* const res = await getAuthTree({
      roleId: props.isEdit === 'add' ? '' : props.info?.id || '',
    }) */

    const res = {
      webResources: [
        {
          id: '1',
          resName: '系统管理',
          children: [
            {
              id: '11',
              resName: '用户管理',
            },
            {
              id: '12',
              resName: '角色权限',
            },
            {
              id: '13',
              resName: '日志管理',
            },
          ],
        },
        {
          id: '2',
          resName: '数据报表',
          children: [
            {
              id: '21',
              resName: '报表一',
            },
            {
              id: '22',
              resName: '报表二',
            },
          ],
        },
      ],
    }

    if (res) {
      if (props.isEdit === 'detail') {
        if (res.webResources && Array.isArray(res.webResources)) {
          recursiveTreeData(res.webResources)
        }
      }
      webTreeData.value = res.webResources || []
    } else {
      webTreeData.value = []
      console.error('获取权限树数据为空', res)
    }
  } catch (error) {
    console.error('获取权限树失败', error)
    webTreeData.value = []
  } finally {
    showWebTreeLoading.value = false
    showAppTreeLoading.value = false
    console.log('获取权限树数据结束')
  }
}

const getWebCheckedNodes = () => (webTreeRef.value ? webTreeRef.value.getCheckedKeys(false) : [])
const getWebHalfCheckedKeys = () => (webTreeRef.value ? webTreeRef.value.getHalfCheckedKeys() : [])
const setWebCheckedNodes = (checkedKeys: any) => {
  if (webTreeRef.value && Array.isArray(checkedKeys)) {
    webTreeRef.value.setCheckedKeys(checkedKeys)
  }
}

const getAppCheckedNodes = () => (appTreeRef.value ? appTreeRef.value.getCheckedKeys(false) : [])
const getAppHalfCheckedKeys = () => (appTreeRef.value ? appTreeRef.value.getHalfCheckedKeys() : [])
const setAppCheckedNodes = (checkedKeys: any) => {
  if (appTreeRef.value && Array.isArray(checkedKeys)) {
    appTreeRef.value.setCheckedKeys(checkedKeys)
  }
}

const showPermissionError = ref(false)
const showPermissionErrorMessage = computed(() => showPermissionError.value && props.isEdit !== 'detail')

function handleWebCheckChange(data: any, checked: boolean) {
  console.log('handleWebCheckChange', data, checked)
  ruleForm.value.webRoleAuths = getWebCheckedNodes().join(',')
  checkPermissionsSelected()
}

function checkPermissionsSelected() {
  const webSelected = getWebCheckedNodes().length > 0
  const appSelected = getAppCheckedNodes().length > 0
  showPermissionError.value = !(webSelected || appSelected)
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = ref({
  roleName: '',
  roleRemark: '',
  webRoleAuths: '',
  appRoleAuths: '',
})

function initForm() {
  ruleForm.value = {
    roleName: '',
    roleRemark: '',
    webRoleAuths: '',
    appRoleAuths: '',
  }
}

const rules = reactive({
  roleName: [
    {
      required: true,
      message: '请输入角色名称',
      trigger: 'blur',
    },
  ],
  roleAuths: [
    {
      required: true,
      message: '请选择角色权限',
      trigger: 'change',
    },
  ],
})

watch(
  () => props.info,
  (val) => {
    if (val) {
      ruleForm.value = JSON.parse(JSON.stringify(val))
      const webAuths = val?.webResources ? val.webResources.split(',') : []
      const appAuths = val?.appResources ? val.appResources.split(',') : []
      nextTick(() => {
        if (webTreeRef.value) {
          setWebCheckedNodes(webAuths)
        }
        if (appTreeRef.value) {
          setAppCheckedNodes(appAuths)
        }
      })
    } else {
      initForm()
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

watch(
  () => props.isEdit,
  (val) => {
    if (val) {
      getAuthTreeList()
    }
  },
  {
    immediate: true,
  }
)

const emit = defineEmits(['close'])
const saveLoading = ref(false)

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      saveLoading.value = true
      try {
        const webFullCheckedKeys = getWebCheckedNodes()
        const webHalfCheckedKeys = getWebHalfCheckedKeys()
        const webResources = [...webFullCheckedKeys, ...webHalfCheckedKeys]
        const appFullCheckedKeys = getAppCheckedNodes()
        const appHalfCheckedKeys = getAppHalfCheckedKeys()
        const appResources = [...appFullCheckedKeys, ...appHalfCheckedKeys]
        if (webResources.length === 0 && appResources.length === 0) {
          showPermissionError.value = true
          saveLoading.value = false
          return
        }
        showPermissionError.value = false
        if (props.isEdit === 'edit') {
          /* await updateAuth({
            roleName: ruleForm.value.roleName,
            roleRemark: ruleForm.value.roleRemark,
            resourceIds: webResources.length > 0 ? webResources.join(',') : '',
            appResourceIds: appResources.length > 0 ? appResources.join(',') : '',
            updateUserId: ui.value.id,
          }) */
        } else {
          /* await addAuth({
            roleName: ruleForm.value.roleName,
            roleRemark: ruleForm.value.roleRemark,
            resourceIds: webResources.length > 0 ? webResources.join(',') : '',
            appResourceIds: appResources.length > 0 ? appResources.join(',') : '',
            createUserId: ui.value.id,
          }) */
        }
        saveLoading.value = false
        ElMessage.success(`${props.isEdit === 'edit' ? '修改' : '新增'}成功`)
        formEl.resetFields()
        emit('close', true)
      } catch (error) {
        saveLoading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  emit('close', false)
}

onMounted(async () => {
  // getAuthTreeList()
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content span) {
  color: #898e98;
}

:deep(.el-input--suffix) {
  height: auto;
}

.form-box {
  height: calc(100vh - 220px);
  position: relative;
  margin-top: 10px;
  overflow-y: auto;
  overflow-x: hidden;

  .all-select {
    &:deep(span) {
      color: #fff;
      font-size: 12px;
    }
  }

  .tree-scroll {
    margin-top: 10px;
    // height: calc(100% - 432px);
    overflow-y: scroll;
    background: transparent;

    :deep(.el-tree-node__label) {
      color: #303133;
    }
  }

  .form-footer {
    position: fixed;
    bottom: 30px;
    right: 40px;
    z-index: 99;
  }

  :deep(.el-form-item--default .el-form-item__label) {
    color: #303133;
  }

  :deep(.el-date-editor.el-input) {
    width: 100%;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-form-item__content:last-child) {
    display: block;
  }

  :deep(.el-form-item__content .add-btn span) {
    color: #fff;
  }

  :deep(.el-select) {
    width: 100%;
  }

  .error-message {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
  }
}
</style>
