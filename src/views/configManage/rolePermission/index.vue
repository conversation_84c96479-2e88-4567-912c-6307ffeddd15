<template>
  <div class="h-full w-full page-content">
    <div class="pad item_content">
      <div class="query-conditions-form main_search">
        <el-row class="w-full mb-10px color-#fff">
          <header-item title="角色名称" :span="4">
            <el-input
              class="big-screen-input"
              size="large"
              @input="getData"
              clearable
              v-model="searchFrom.userName"
              placeholder="请输入角色名称"
              :suffix-icon="Search"
            >
              <template #suffix></template>
            </el-input>
          </header-item>
          <header-item title="所属机构" :span="4">
            <el-select
              class="big-screen-select"
              @change="handleOrgCodeChange"
              v-model="searchFrom.orgCode"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in orgOptions"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </header-item>
          <header-item title="创建时间" :span="4">
            <el-date-picker
              class="big-screen-input"
              v-model="searchFrom.createTime"
              placeholder="请选择日期"
              type="date"
              value-format="YYYY-MM-DD"
              @change="handleCreateChange"
            />
          </header-item>
          <div class="flex-1 flex justify-end">
            <el-button class="search-btn big-screen-button" type="primary" @click="getData">查询</el-button>
            <el-button class="search-btn big-screen-button" type="primary" @click="handleEdit({}, 'add')">
              创建
            </el-button>
          </div>
        </el-row>
      </div>
      <div class="table">
        <table-list :columns="columns" :data="tableData" :pagination="pagination" :no-page="false">
          <template #operate="{ row }">
            <div class="table-operate">
              <span class="operate-item" @click="handleEdit(row, 2)">详情</span>
              <span class="operate-item" @click="handleEdit(row, 1)">编辑</span>
              <span class="operate-item delete-btn" @click="handleDelete(row.authId)">删除</span>
              <span class="operate-item" @click="handlePasswd(row)">重置密码</span>
            </div>
          </template>
        </table-list>
      </div>
    </div>
    <popup-side-black
      v-model="editPopupShow"
      :popupTitle="isEdit === 'add' ? '创建角色' : isEdit === 'edit' ? '编辑角色' : '角色详情'"
      @close="close"
    >
      <AddRole @close="close" :info="currentInfo" :isEdit="isEdit" />
    </popup-side-black>
  </div>
</template>

<script lang="ts" setup>
import popupSideBlack from '@/components/public/popup/popupSideBlack.vue'
import { onMounted, ref, reactive, onBeforeMount } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { PageModel } from '@/types'
import { useUserInfo } from '~/store'
import { Search } from '@element-plus/icons-vue'
import AddRole from './addRole/index.vue'
// import { findUserList, findRoleList, resetPassword, deleteAccount } from '@/api/systemSet'
import tableList, { IColumn, IPaginationProps } from '@/components/public/tableList/index.vue'

const pagination = ref<IPaginationProps>({
  total: 10,
})

const ui = useUserInfo()
// const roleOptions = ref<[]>()
interface OrgOption {
  dictValue: string | number
  dictLabel: string
}
const orgOptions = ref<OrgOption[]>([]) // 机构下拉

const handleOrgCodeChange = (val) => {
  console.log('选择的机构', val)
  searchFrom.orgCode = val
  getData()
}
const unitPersonTotal = ref(0)

// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
})
const searchFrom = reactive({
  userName: '', // 用户名模糊匹配
  userAccount: '', // 用户账号模糊匹配
  orgCode: '',
  createTime: '', // 创建时间
})
const loading = ref<boolean>(false)
const tableData = ref<any>([])
const columns = ref<IColumn[]>([])

function handleCreateChange(val) {
  console.log('选择的创建时间', val)
  searchFrom.createTime = val
  getData()
}

function getTableColumns() {
  columns.value = [
    {
      label: '序号',
      type: 'index',
      width: 80,
    },
    {
      label: '角色名称',
      prop: 'userName',
    },
    {
      label: '创建机构',
      prop: 'orgName',
    },
    {
      label: '创建人',
      prop: 'userTelphone',
    },
    {
      label: '创建时间',
      prop: 'lastLoginTime',
    },
    {
      label: '操作',
      type: 'operate',
      slot: 'operate',
      width: 220,
    },
  ]
}

// 新增 or 编辑
const editPopupShow: any = ref(false)
const isEdit = ref('add')
const currentInfo = ref({})

function handleEdit(data: any, flag) {
  console.log(data)
  console.log('编辑或查看', flag)
  isEdit.value = flag
  editPopupShow.value = true

  currentInfo.value = data
}

const close = (val) => {
  editPopupShow.value = false
  // 刷新列表
  if (val) {
    getData()
  }
}

// 重置
function handlePasswd(data: any) {
  // console.log(data)
  ElMessageBox.confirm('重置密码将重置为默认密码(Aa@123456),原密码将失效,是否继续?', '重置密码', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(async () => {
    if (data.id) {
      /* await resetPassword({
        updateUserId: ui.value.authId,
        userId: data.id,
      }) */
    }
    ElMessage.success('密码重置成功')
    getData()
  })
}

// 删除
function handleDelete(ids: any) {
  console.log(ids)
  ElMessageBox.confirm('确定删除该账号？', '删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(async () => {
    /* await deleteAccount({
      authId: ids,
    }) */
    ElMessage.success('删除成功')
    getData()
  })
}

async function getData() {
  // console.log(ui.value)
  loading.value = true
  if (ui.value.isGroup === '2') searchFrom.orgCode = ui.value.orgCode
  /* const res = await findUserList({
    pageNo: pageModel.pageNo,
    pageSize: pageModel.pageSize,
    q: searchFrom.userName,
    orgCode: searchFrom.orgCode,
    // ...searchFrom
  }) */
  const res = {
    rows: [
      {
        authId: '1',
        orgName: '测试单位',
        userName: '张三',
        userTelphone: '***********',
        roleName: '管理员',
        lastLoginTime: '2023-10-01 12:00:00',
        accountStatus: '0', // 0 禁用 1 启用
      },
      {
        authId: '2',
        orgName: '测试单位',
        userName: '李四',
        userTelphone: '***********',
        roleName: '普通用户',
        lastLoginTime: '2023-10-02 14:00:00',
        accountStatus: '1', // 0 禁用 1 启用
      },
      // 更多数据...
    ],
    pageNo: 1,
    pageSize: 10,
    total: 1,
  }

  loading.value = false
  tableData.value = res.rows || []
  tableData.value.forEach((element) => {
    element.userAuthStatusStr = element.userAuthStatus === '0' ? '正常' : '锁定'
  })
  pageModel.pageNo = res.pageNo
  pageModel.pageSize = res.pageSize
  pageModel.total = res.total
  unitPersonTotal.value = res.total
}

onMounted(async () => {
  // 用户角色下拉
  /* roleOptions.value = await findRoleList({
    sysCode: 'ss_cloud_platform',
  }) */
  getData()
})

onBeforeMount(() => {
  getTableColumns()
})
</script>
<style lang="scss" scoped>
.page-content {
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  height: 100%;

  // display: flex;
  .table-list {
    height: calc(100vh - 195px);
    width: 100%;
    display: flex;

    :deep(.table-list_wrap) {
      background: rgba(27, 63, 137, 0.2);
    }
  }

  .item_content {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .table {
      // flex: 1;
      height: 100%;
    }
  }
}

.header-item .header-item_control > * {
  width: 200px !important;
}

:deep(.header-item_label) {
  color: #ffffff !important;
  // font-size: 14px !important;
}

.query-conditions-form {
  background-color: transparent;
  position: relative;
  margin: 0;

  .unit-name {
    font-weight: 500;
    font-size: 16px;
  }

  .search-btn {
    right: 20px;
    height: 32px;
    width: 80px;
  }
}

.table-operate {
  display: flex;
  align-items: center;
  gap: 8px;

  .operate-item {
    color: var(--el-color-primary);
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;

    &:hover {
      opacity: 0.8;
    }
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.el-icon {
  font-size: 16px;
  color: var(--el-color-primary);
}

:deep(.pagination-wrap) {
  padding-top: 12px;
}

:deep(.el-table__header th),
:deep(.el-table__body td) {
  height: 48px !important;
  line-height: 48px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 0;
  padding-right: 0;
  // background: #18284a;
  // background: rgba(27, 63, 137, 0.2);
}

:deep(.el-table__header .cell),
:deep(.el-table__body .cell) {
  height: 48px !important;
  line-height: 48px !important;
  display: flex;
  align-items: center;
  // color: #fff;
}

/* :deep(.el-table__header-wrapper),
:deep(.el-table__header .cell) {
  background: #52678f;
} */

:deep(.search-name) {
  width: 300px;
}

:deep(.operate-item.delete-btn) {
  color: rgba(246, 52, 52, 1) !important;
}

.delete-btn.disable {
  color: #ccc !important;
  cursor: not-allowed !important;
}

.search-name {
  height: 32px;
}

:deep(.el-table__row) {
  .el-tooltip {
    span {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
