<template>
  <div class="right-comp">
    <!-- 右侧列表 -->
    <!-- <div class="title">总平面布局</div> -->
    <card-title2 :title="treeInfo.name">
      <template v-if="!model.isEdit">
        <el-button @click="model.isEdit = !model.isEdit" type="primary" class="big-screen-button btn">编辑</el-button>
      </template>
      <template v-else>
        <el-button @click="submit" type="primary" :disabled="!model.isEdit" class="big-screen-button btn">
          保存
        </el-button>
        <el-button @click="cancel" class="big-screen-button btn">取消</el-button>
      </template>
    </card-title2>
    <div class="radio-box">
      <div v-for="itemData in listData" :key="itemData.title" class="item-data">
        <p class="title">{{ itemData.title }}</p>
        <el-radio-group v-model="itemData.checkValue" :disabled="!model.isEdit">
          <el-radio v-for="option in itemData.options" :key="option.value" :label="option.value">
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, defineProps, ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import cardTitle2 from '@/components/public/cardTitle2.vue'

const props = defineProps({
  treeInfo: {
    type: Object,
    default: () => {
      return {}
    },
  },
})

const listData = ref([
  {
    title: '1、建筑之间的防火间距是否符合规范要求',
    checkValue: 2,
    options: [
      { label: '建筑之间的防火间距全部符合要求', value: 1 },
      { label: '主要建筑之间的防火间距符合要求', value: 2 },
      { label: '主要建筑之间的防火间距不符合要求', value: 3 },
    ],
  },
  {
    title: '2、建筑物的防火分区是否符合规范要求',
    checkValue: 1,
    options: [
      { label: '建筑之间的防火间距全部符合要求', value: 1 },
      { label: '主要建筑之间的防火间距符合要求', value: 2 },
      { label: '主要建筑之间的防火间距不符合要求', value: 3 },
    ],
  },
  {
    title: '3、建筑物的防火墙是否符合规范要求',
    checkValue: 1,
    options: [
      { label: '符合', value: 1 },
      { label: '不符合', value: 0 },
    ],
  },
  {
    title: '4、建筑物的防火门是否符合规范要求',
    checkValue: 1,
    options: [
      { label: '建筑之间的防火间距全部符合要求', value: 1 },
      { label: '主要建筑之间的防火间距符合要求', value: 2 },
      { label: '主要建筑之间的防火间距不符合要求', value: 3 },
    ],
  },
  {
    title: '5、建筑物的消防设施是否符合规范要求',
    checkValue: 1,
    options: [
      { label: '符合', value: 1 },
      { label: '不符合', value: 0 },
    ],
  },
])

const model = reactive({
  isShowCropper: false,
  loading: false,
  isEdit: false,
})

const loading = ref(false)

async function submit() {
  loading.value = true
  try {
    // const params = {}
    // const res = await systemSet(params)
    loading.value = false
    ElMessage.success('保存成功')
    model.isEdit = false
    // await getSysConf()
  } catch (e) {
    loading.value = false
    console.error('保存异常', e)
    ElMessage.error('保存失败')
  }
}

const cancel = () => {
  model.isEdit = false
  // getSysConf()
}

watch(
  () => props.treeInfo,
  (val) => {
    if (val) {
      console.log('🚀 ~ watch ~ props.treeInfo:', props.treeInfo)
    }
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style scoped lang="scss">
.right-comp {
  height: 100%;
  width: 100%;
  padding: 15px;
  background: rgba(16, 32, 70, 0.5);
  display: flex;
  flex-direction: column;

  .radio-box {
    margin-top: 30px;
    margin-left: 15px;
    flex: 1;
    overflow: auto;
  }

  .item-data {
    margin-bottom: 25px;

    .title {
      color: #fff;
      font-size: 16px;
      margin-bottom: 10px;
    }
  }
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
:deep(.el-radio) {
  margin-left: 15px;
}
</style>
