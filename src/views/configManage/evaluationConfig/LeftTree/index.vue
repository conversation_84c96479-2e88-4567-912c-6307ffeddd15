<!-- eslint-disable max-len -->
<template>
  <div class="unitTree h-full flex flex-col justify-start justify-items-center">
    <div class="flex">
      <el-input
        v-model="filterText"
        clearable
        placeholder="请输入关键词"
        :suffix-icon="Search"
        class="big-screen-input"
      />
    </div>
    <el-tree
      class="tree-scroll"
      empty-text="暂无数据"
      :current-node-key="''"
      default-expand-all
      highlight-current
      :expand-on-click-node="false"
      node-key="id"
      ref="treeRef"
      :props="defaultProps"
      :data="treeData"
      @node-click="handleNodeClick"
      :filter-node-method="filterNode"
    >
      <template #default="{ data }">
        <span class="custom-tree-node">
          <span class="node-unit-name" :title="data.tenantName">{{ data.tenantName }}</span>
        </span>
      </template>
      <template #empty>
        <el-empty description="暂无数据" :image="emptyPng" />
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, watch, defineProps, onBeforeUnmount, onMounted } from 'vue'
import { useUserInfo } from '@/store'
import { Search } from '@element-plus/icons-vue'
import { ElTree, ElMessage } from 'element-plus'
import emptyPng from '@/assets/image/empty.png'

import PubSub from 'pubsub-js'
// import { tenantList } from '@/api/systemSet'

const ui = useUserInfo()
console.log('ui', ui)
const defaultProps = ref<any>({
  children: 'child',
  label: 'tenantName',
  disabled: 'sort',
})
const props = defineProps({
  unitIdFlag: { type: Number, default: 0 },
  type: { type: Number, default: 0 },
  title: { type: String, default: '组织结构树' },
  hasTitle: { type: Boolean, default: true },
  isLinkTree: { type: Boolean, default: false },
  showTree: { type: Boolean, default: false },
})

const filterText = ref('')
const treeRef = ref<any>()
const treeData: any = ref([])
const emits = defineEmits(['updateVal'])
const currentInfo = ref({})
const isRootDiy = ref(false)
// const rootNode = ref({})
const currentSelectedId = ref('')

const handleCommand = (command) => {
  currentInfo.value = command
  isRootDiy.value = false
}

watch(filterText, (val: any) => {
  treeRef.value.filter(val)
})

const filterNode = (value, data) => {
  if (!value) return true
  return (data.tenantName || '').includes(value)
}

const getOrgUnitTree = async () => {
  try {
    // const res = await tenantList({ official: 0 })
    // 模拟数据
    const res = {
      data: [
        {
          id: '1',
          tenantName: '租户A',
          child: [
            { id: '1-1', tenantName: '租户A-子租户1', child: [] },
            { id: '1-2', tenantName: '租户A-子租户2', child: [] },
          ],
        },
        { id: '2', tenantName: '租户B', child: [] },
        { id: '3', tenantName: '租户C', child: [] },
      ],
    }
    treeData.value = res.data.map((item: any) => ({
      ...item,
      leafType: props.isLinkTree ? item.level : item.leafType,
      tenantName: item.tenantName || item.unitName,
      id: item.id || item.unitId,
      child: item.child || [],
    }))
    nextTick(() => {
      // 默认选中第一个节点
      if (treeRef.value && treeData.value.length > 0) {
        const firstId = treeData.value[0].id
        treeRef.value.setCurrentKey(firstId)
        const currentNode = treeRef.value.getNode(firstId)
        if (currentNode) {
          handleNodeClick(currentNode.data)
        }
      }
    })
  } catch (error) {
    ElMessage.error('获取树数据失败')
  }
}

const handleNodeClick = async (node: any) => {
  currentSelectedId.value = node.id
  currentInfo.value = { ...node }
  let id, name, leafType
  if (!props.isLinkTree) {
    id = node.tenantName === '全部租户' ? '' : node.id
    leafType = node.leafType
  } else {
    id = node.id
    leafType = node.level
  }
  name = node.unitName || node.tenantName
  emits('updateVal', { id, name, leafType })
}

watch(
  () => props.showTree,
  (val) => {
    console.log(val, '---------')
    // debugger
    if (val && ui.value.isGroup === '2') {
      const res = {
        unitId: ui.value.orgCode,
        unitName: ui.value.orgName,
        leafType: 'unit',
        child: [],
      }

      treeData.value = [res]
      emits('updateVal', {
        id: ui.value.orgCode,
        name: ui.value.orgName,
        leafType: 'unit',
      })
      console.log(treeData.value, '---------')
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

defineExpose({ handleCommand })

onBeforeUnmount(() => {
  currentSelectedId.value = ''
  PubSub.unsubscribe('refreshTree')
})

onMounted(() => {
  getOrgUnitTree()
})

PubSub.subscribe('refreshTree', () => {
  getOrgUnitTree()
})
</script>

<style lang="scss" scoped>
.unitTree {
  // background: #18284a;
  background: rgba(16, 32, 70, 0.4);
  flex: 1;
  margin-right: 15px;
  padding: 10px;
  width: 380px;
  box-sizing: border-box;

  .title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #606266;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 3px;
      height: 14px;
      background: #4070ff;
      border-radius: 2px 2px 2px 2px;
      display: inline-block;
      margin-right: 5px;
    }
  }
}

.elIcon {
  margin-right: 7px;
}

.tree-scroll {
  margin-top: 10px;
  // height: calc(100% - 432px);
  overflow-y: scroll;
  background: transparent;
}

.custom-tree-node {
  // flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  position: relative;
}

:deep(.el-input--suffix) {
  height: 30px;
}

.operate-area {
  display: none;
}

// .custom-tree-node:hover .operate-area {
//     display: block;
// }

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content .operate-area) {
  display: block !important;
}

:deep(.el-tree-node__content) {
  padding: 20px 0;
  width: 100%;
  box-sizing: border-box;

  .custom-tree-node {
    width: calc(100% - 35px);
    overflow: hidden;

    .node-unit-name {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

:deep(.el-dialog) {
  background: #fff !important;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background: #192f4d;
}
</style>
