<template>
  <div class="evaluation-config">
    <!-- 左侧树状图 -->
    <div>
      <LeftTree @updateVal="updateVal" :unitIdFlag="1" :title="'租户信息'" />
    </div>
    <!-- 右侧列表 -->
    <div>
      <RighComp :treeInfo="treeInfo" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import RighComp from './RightComp/index.vue'
import LeftTree from './LeftTree/index.vue'
import { ref } from 'vue'

const treeInfo = ref({})

function updateVal(obj) {
  console.log('更新右侧页面', obj)
  treeInfo.value = obj
}
</script>

<style scoped lang="scss">
.evaluation-config {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden; // 防止flex撑开
  background: url('@/assets/image/energyStorageSafety/bg.png') no-repeat center;
  background-size: cover;

  & > div:nth-child(2) {
    flex: 1;
  }
}
</style>
