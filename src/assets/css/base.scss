@import '../iconfont/iconfont.css';
.block-title {
  color: #3093fa;
  position: relative;
  padding-left: 15px;
  font-size: 20px;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: #3093fa;
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 0;
    border-radius: 2px;
    transform: translateY(-50%);
  }
}
.block-title.round {
  padding-left: 0;
  &::before {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: inherit;
    margin-right: 5px;
  }
}
.el-collapse {
  border-top: 0;
}
.gs-collapse {
  // background: #fff;
  border-radius: 10px;
  border-bottom: unset;
  padding-bottom: 0.1px;
  .el-collapse-item {
    border-radius: 10px;
    background: #fff;
    padding: 0 10px;
  }
  // el-collapse-item__header is-active
  .el-collapse-item__header {
    font-size: 16px;
    position: relative;
    font-weight: bold;
    height: auto;
    line-height: 1em;
    // border-bottom-color: transparent;
    border-bottom: 0px !important;
    padding: 20px 0;
    &.is-active {
      // border-bottom: 0.00521rem solid var(--el-collapse-border-color);
      &::after {
        content: '';
        position: absolute;
        right: 0px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .el-collapse-item__arrow {
      transform: rotate(90deg);
      &.is-active {
        position: relative;
        // right: 30px;
        transform: rotate(-90deg);
      }
    }
  }

  .el-collapse-item__wrap {
    // margin-bottom: 20px;
    border-bottom: none !important;
    .el-collapse-item__content {
      padding-bottom: 0;
      line-height: 1;
    }
  }
  .collapse {
    border: 1px solid #e9e9e9;
    border-radius: 10px;
    text-indent: 10px;
    color: #333333;
    font-weight: 400;
    margin-bottom: 16px;

    .collapse-div {
      margin-top: 0;
    }
  }
}

.detail-popup-top-tab {
  .el-tabs__header {
    width: 100%;
    padding: 8px;
    // background: #fff;
    background: #f0f2f5;
    border-radius: 10px;

    .el-tabs__nav-wrap {
      &::after {
        display: none;
      }
      .el-tabs__nav-scroll {
        padding-left: 0;
        .el-tabs__nav {
          width: 100%;
          display: flex;
          .el-tabs__item {
            flex: 1;
            text-align: center;
            // color: #999;
            color: #666666;
            &.is-active {
              // color: #333;
              background: #0080ff;
              border-radius: 8px;
              color: #ffffff;
            }
          }
          .el-tabs__active-bar {
            position: absolute;
            top: 0;
            // left: -6px;
            height: 100%;
            // background: #edf2f5;
            background: #0080ff;
            border-radius: 8px;
            z-index: 0;
            color: #ffffff;
          }
        }
      }
    }
  }
}
.table-operate {
  white-space: nowrap;
  .operate-item {
    font-size: 14px;
    color: #0080ff;
    cursor: pointer;
  }
  .operate-item::before {
    content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }
  .operate-disable-single {
    font-size: 14px;
    pointer-events: none;
    user-select: none;
    cursor: not-allowed;
    color: #ccc;
  }
  .operate-disable {
    font-size: 14px;
    color: #ccc;
    cursor: not-allowed;
    // margin-right: 10px;
    pointer-events: none;
    user-select: none;
  }
  .operate-disable::before {
    content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }
  .operate-item:first-child::before {
    display: none;
  }

  .operate-disable1 {
    font-size: 14px;
    // pointer-events: none;
    color: #ccc;
    cursor: not-allowed;
    user-select: none;
  }
  .operate-disable1::before {
    // content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }
  .operate-disable1 + .operate-disable1::before {
    content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }
}

.confirm-btn {
  // padding-right: 20px;
  .el-button {
    width: 80px;
    border-color: #dcdfe6;
  }
  .c-cancel {
    color: #666666;
  }
  .c-delete {
    color: #e02424;
    border-color: #e02424;
  }
  .c-save {
    color: #ffffff;
    background: #0080ff;
    border-radius: 3px;
  }
}

* {
  scrollbar-color: var(--el-scrollbar-bg-color) var(--el-bg-color); /* 滑块颜色  滚动条背景颜色 */
}

::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar:horizontal {
  height: 6px;
}
::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color);
  border-radius: 10px;
  transition: all 0.2s ease-in-out;

  &:hover {
    cursor: pointer;
    background-color: var(--el-border-color);
  }
}

.message_tabs .el-tabs__nav-wrap::after {
  height: 0px !important;
}

.el-tabs .el-tabs__item {
  // font-size: 18px !important;
  &:focus-visible {
    box-shadow: unset;
  }
}

.el-form-item {
  .el-form-item__label {
    // font-size: 16px;
  }
}

// .el-icon svg {
//   height: inherit !important;
//   width: inherit !important;
// }

.el-main .video-tree {
  min-width: 300px;
}

/*隐藏文字*/
.el-menu--collapse .el-submenu__title span {
  display: none;
}

/*隐藏 > */
.el-menu--collapse .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.table-list {
  .el-button.is-disabled,
  .el-button.is-disabled:focus,
  .el-button.is-disabled:hover {
    background-color: transparent;
  }

  /* 不显示下划线 */
  .el-table__inner-wrapper::before {
    height: 0;
  }
}

.custom-video-popup .el-dialog__body {
  padding: 0;
}

.authorization-tips .el-dialog__body {
  text-align: left;
  padding-top: 23px;
  max-height: 100%;
  flex: 1;
  -ms-flex: 1 1 auto; /* 兼容IE */
  overflow-y: auto;
  overflow-x: hidden;
  // text-align: center;
  // padding: 120px 20px;
  // border-top: 1px solid #eee;
  // border-bottom: 1px solid #eee;
}

#multipleTable thead tr th {
  color: #333;
  background-color: #fafafa;
}
.aqzs-box-unit {
  position: absolute;
  right: 10px;
  top: 0;
  width: 119px;
  height: 117px;
  background-image: url(@/assets/image/yzt/excellent.png);
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .num {
    font-size: 32px;
    font-family: D-DIN, sans-serif;
    font-weight: bold;
  }
  .score {
    position: absolute;
    right: 17px;
    bottom: 59px;
    left: 80px;
  }
  .tip {
    position: absolute;
    left: 50%;
    bottom: -6px;
    transform: translate(-50%, 30%);
    width: 54px;
    height: 24px;
    line-height: 23px;
    // font-size: 12px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
    // border: 1px solid #63dde0;
    background: linear-gradient(0deg, #0061e4 0%, #54fffd 100%);
    border-radius: 12px;
  }
}
.aqzs2-box-unit {
  position: relative;
  right: 10px;
  top: 0;
  width: 206px;
  height: 204px;
  background-image: url(@/assets/image/yzt/excellent.png);
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .num {
    font-size: 56px;
    font-family: D-DIN, sans-serif;
    font-weight: bold;
  }
  .score {
    position: absolute;
    right: 0;
    bottom: 104px;
    left: 140px;
    top: 85px;
  }
  .word-tips {
    font-size: 18px;
    font-family: Microsoft YaHei, sans-serif;
    font-weight: 400;
    color: #4b5f72;
  }
  .tip {
    position: absolute;
    left: 50%;
    bottom: -6px;
    transform: translate(-50%, 30%);
    width: 54px;
    height: 24px;
    line-height: 23px;
    // font-size: 12px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
    // border: 1px solid #63dde0;
    background: linear-gradient(0deg, #0061e4 0%, #54fffd 100%);
    border-radius: 12px;
  }
}

.excellent-text-color {
  background: linear-gradient(0deg, #006766 0%, #00dcb0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.good-text-color {
  background: linear-gradient(0deg, #004188 0%, #00d6d8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pass-text-color {
  background: linear-gradient(0deg, #9d4600 0%, #dfa300 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.danger-text-color {
  background: linear-gradient(0deg, #8f1310 0%, #ff3939 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

// 兼容老式safari浏览器
.el-main {
  flex-basis: 0;
}

.el-input__wrapper:not(.topSearch .select-box .el-select .el-input__wrapper) {
  box-shadow: 0 0 0 0.00821rem
    var(--el-input-border-color, var(--el-border-color)) inset !important;
  &:hover {
    box-shadow: 0 0 0 0.00821rem
      var(--el-input-border-color, var(--el-border-color)) inset !important;
  }
}

.el-input__wrapper.is-focus:not(
    .topSearch .select-box .el-select .el-input__wrapper
  ) {
  box-shadow: 0 0 0 0.00821rem
    var(--el-input-focus-border-color, var(--el-color-primary)) inset !important;
}

.el-select
  .el-input__wrapper.is-focus:not(
    .topSearch .select-box .el-select .el-input__wrapper
  ) {
  box-shadow: 0 0 0 0.00821rem
    var(--el-input-focus-border-color, var(--el-color-primary)) inset !important;
}

.el-select
  .el-input.is-focus
  .el-input__wrapper:not(.topSearch .select-box .el-select .el-input__wrapper) {
  box-shadow: 0 0 0 0.00821rem
    var(--el-input-focus-border-color, var(--el-color-primary)) inset !important;
}

.el-message {
  box-sizing: content-box;
}
