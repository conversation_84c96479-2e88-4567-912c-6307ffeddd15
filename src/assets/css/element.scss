// 使用 CSS 变量定义主题
:root {
  // 主色
  --el-color-primary: #409eff;
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-error: #f56c6c;
  --el-color-info: #909399;
  
  // 文字颜色
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;
  --el-text-color-placeholder: #a8abb2;
  --el-text-color-disabled: #c0c4cc;
}

// 导入编译后的 CSS 而不是 SCSS
@import 'element-plus/theme-chalk/index.css';

// 移除弹窗 body 的上下padding
.el-dialog__body {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

[v-cloak] {
  display: none;
}

.big-screen-segmented {
  --el-segmented-bg-color: transparent;
  --el-segmented-item-selected-bg-color: rgba(25, 79, 185, 0.75);
  .el-segmented__item {
    padding: 6px 22px;
    background: rgba(69, 125, 238, 0.2);
    border-right: 1px solid #244378;
  }
  .el-segmented__group {
    border-radius: 16px;
    overflow: hidden;
  }
  .el-segmented__group > label.el-segmented__item:first-of-type {
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px;
  }
  .el-segmented__group > label.el-segmented__item:last-of-type {
    border-top-right-radius: 16px;
    border-bottom-right-radius: 16px;
    border-right: none;
  }
}


.small-screen-segmented {
  --el-segmented-bg-color: transparent;
  --el-segmented-item-selected-bg-color: rgba(25, 79, 185, 0.75);
  .el-segmented__item {
    padding: 6px 12px;
    background: rgba(69, 125, 238, 0.2);
    border-right: 1px solid #244378;
  }
  .el-segmented__group {
    border-radius: 16px;
    overflow: hidden;
  }
  .el-segmented__group > label.el-segmented__item:first-of-type {
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px;
  }
  .el-segmented__group > label.el-segmented__item:last-of-type {
    border-top-right-radius: 16px;
    border-bottom-right-radius: 16px;
    border-right: none;
  }
}

.big-screen-select {
  height: 32px;
  background: linear-gradient( 270deg, rgba(8,139,255,0.2) 0%, rgba(8,139,255,0) 53%, rgba(8,139,255,0.2) 100%);
}
.big-screen-button {
  background: url('@/assets/image/energyStorageSafety/btn-bg.png') no-repeat center;
  background-size: 100% 100%;
  font-weight: 400;
  font-size: 14px;
}
.big-screen-input {
  background: linear-gradient( 270deg, rgba(8,139,255,0.2) 0%, rgba(8,139,255,0) 53%, rgba(8,139,255,0.2) 100%);
}