// 处置节点公共样式添加
.tab-content {
  // padding-left: 24px;
  padding-bottom: 24px;
  margin-left: -10px;
}
li.disposal-node {
  position: relative;
  padding-bottom: 26px;
  list-style: none;
  font-family: Source <PERSON>, Source Han San<PERSON> CN-Medium, sans-serif;
  color: #161616;
}

li.disposal-node:last-child {
  padding-bottom: 0;
  &::before {
    display: none;
  }
}

li.disposal-node .dispose-node {
  margin-left: 20px;
  word-break: break-all;
}

li.disposal-node::before {
  content: '';
  position: absolute;
  top: 9px;
  left: 0;
  width: 1px;
  height: 100%;
  border-left: 1px dashed #409eff;
  background: transparent;
  border-radius: 1px;
  z-index: 1;
}

li.disposal-node .arrow {
  position: absolute;
  top: 2px;
  left: 0px;
  z-index: 10;
  background-color: #fff;
  transform: translateX(-50%);
}

/* li.disposal-node:last-child::before{
    display: none;
} */

li.disposal-node::after {
  // content: '';
  // box-sizing: border-box;
  // position: absolute;
  // top: 1px;
  // left: -7px;
  // border-radius: 50%;
  // width: 16px;
  // height: 16px;
  // opacity: 1;
  // // background: #ffffff;
  // border: 2px solid #f30c0c;
}

li.disposal-node .trend-item {
  margin-left: 15px;
  word-break: break-all;
}

li.disposal-node .trend-item-title {
  font-size: 16px;
  // line-height: 21px;
  color: #000000;
  margin-bottom: 16px;
  margin-top: 0;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

li.disposal-node .trend-item-time {
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 5px;
}

li.disposal-node .trend-item-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 14px;
  overflow: hidden;
  align-items: center;
}

li.disposal-node .list-item-label {
  color: #333333;
  vertical-align: top;
}

li.disposal-node .list-item-content {
  flex: 1;
  color: #666666;
  font-size: 14px;
  font-weight: normal;
  word-break: break-all;
}
li.disposal-node .list-item-content.trueWarnColor {
  color: #ea1313;
}
li.disposal-node .list-item-content.list-imgs {
  padding-top: 10px;
}

li.disposal-node .list-imgs .el-image {
  // width: 77px;
  // height: 45px;
  width: 70px;
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 5px;
  cursor: pointer;
  overflow: hidden;
}
