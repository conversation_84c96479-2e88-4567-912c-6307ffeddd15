* {
  font-size: 14px;
  font-family: 'Arial', sans-serif;
}

.font-hp-regular {
  font-family: 'Alibaba-PuHuiTi-Regular', sans-serif;
}

.font-hp-bd {
  font-family: 'Alibaba-PuHuiTi-Bold', sans-serif;
}

.font-hp-md {
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

.font-hp-rl {
  font-family: 'Alibaba-PuHuiTi-Regular', sans-serif;
}
.font-din-num {
  font-family: 'D-DIN-PRO-500-Medium', sans-serif;
}
.Arial {
  font-family: 'Arial', sans-serif;
}

.shadow-btn {
  background: #0080ff;
  border-radius: 4px;
  width: 65px;
  height: 32px;
  /* box-shadow: 0px 0px 20px rgba(64, 158, 255, 0.3); */
}

.gray-1 {
  color: #333;
}

.el-select--default {
  height: 41px;
}
.select-trigger {
  height: 100%;
}
.el-input--suffix {
  height: 100%;
}
.el-input__inner {
  min-height: 100%;
  max-height: 100%;
  transform: translateX(3px);
}
.el-table__header tr,
.el-table__header th {
  padding: 0;
  height: 43px;
  line-height: 43px;
}
.el-table__body tr,
.el-table__body td {
  padding: 0;
  height: 54px;
  line-height: 54px;
}

.el-select .el-input__inner {
  height: 40px;
  /* line-height: 40px; */
}

.el-tabs .el-tabs__item {
  font-size: 16px;
}

.el-table .el-scrollbar__view {
  height: 100%;
}

.el-loading-mask {
  z-index: 999;
}

.line-item {
  width: 100%;
  display: flex;
  margin-bottom: 10px;
}
.left-content {
  min-width: 6em;
  color: #333333;
  flex-shrink: 0;
  text-align: justify;
  padding: 5px 0;
  text-justify: distribute-all-lines;
  -moz-text-align-last: justify;
       text-align-last: justify;
}
.right-content {
  flex: 1;
  color: #666666;
  padding: 5px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 
.line-item {
  display: grid;
  grid-template-columns: auto 1fr;
  margin-bottom: 20px;
}
.left-content {
  min-width: 100px;
  text-align: justify;
  text-justify: distribute-all-lines;
  text-align-last: justify;
}
.right-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} */

.theme-color {
  color: #1890ff;
}
.export {
  float: right;
  display: inline-flex;
  flex-grow: 1;
  justify-content: flex-end;
  /* margin-bottom: 0.10417rem; */
  margin-left: 0.10417rem;
  margin-bottom: 20px;
  /* display: flex; */
  /* position: absolute;
    right: 0.10417rem;
    bottom: 0.10417rem;
    margin-top: 0.10417rem; */
  /* display: inline-flex;
    flex-grow: 1;
    justify-content: flex-end;
    margin-bottom: 0.10417rem;
    margin-left: 0.10417rem; */
}

.calc-tree-width {
  width: calc(100% - 350px);
}
.page-btn {
  color: #666666;
  /* width: 65px;
  height: 32px; */
  background: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.export-btn {
  background: #0080ff;
  border-radius: 4px;
  color: #ffffff;
}
.goback-btn {
  width: 65px;
  height: 32px;
  background: #0080ff;
  border-radius: 4px;
}
.auto_hidden {
  overflow: hidden;
}
.auto_hidden:hover {
  overflow-y: auto;
}
/* .vjs-error .vjs-error-display:before {
  content: '';
} */
