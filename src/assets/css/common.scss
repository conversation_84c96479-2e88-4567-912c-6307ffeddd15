// 列表页搜索区域
.query-conditions-form {
  position: relative;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 20px 20px 0;
  margin-bottom: 20px;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.query-conditions-form-user {
  position: relative;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 20px 20px 10px 20px;
  // margin-bottom: 20px;
  // overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.query-conditions-form-role {
  position: relative;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 20px 20px 0px 20px;
  // margin-bottom: 20px;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.query-conditions-form-jc {
  height: 53px !important;
  position: relative;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 20px 20px 20px 15px;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  // align-items: center;
}
.query-conditions-form-sbda {
  position: relative;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 20px 20px 20px 20px;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.query-conditions-form-lgjc {
  position: relative;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 20px 20px 0px 20px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.el-table__empty-block {
  min-height: 300px;
}

// table的点,默认值为蓝色
.table-column-dot {
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  vertical-align: middle;
  margin: 0 5px 0 10px;
  border-radius: 50%;
  background: transparent;
}

.table-column-dot:first-child {
  margin-left: 0;
}

.main_search {
  width: 100%;
  height: auto;
  padding: 20px;
  padding-bottom: 0;
  background-color: #ffffff;

  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
}

.expand-put-away-action {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  line-height: 1;
  height: 0.16667rem;
  white-space: nowrap;
  cursor: pointer;
  transition: 0.1s;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  padding: 0.04167rem 0.07813rem;
  color: #409eff;
}
.table-column-single-dot {
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  vertical-align: middle;
  margin-bottom: 0.01042rem;
  border-radius: 50%;
  background: transparent;
}

.fqpg-btn {
  font-size: 22px;
  color: #ffffff;
  width: 132px;
  height: 50px;
  background: #0080ff;
  border-radius: 4px;
  left: 780px;
  position: relative;
}
.pagination-wrap-charts .el-pagination.is-background .btn-next,
.pagination-wrap-charts .el-pagination.is-background .btn-prev,
.pagination-wrap-charts .el-pagination.is-background .el-pager li {
  background-color: #fff;
}

.pagination-wrap-charts .el-pagination.is-background .el-pager li.is-active {
  color: var(--el-pagination-hover-color);
}

.pagination-wrap-charts
  .el-pagination.is-background
  .el-select
  .el-input__inner {
  height: 35px;
  line-height: 35px;
  margin-top: 2px;
  margin-left: 10px;
}

.pagination-wrap-charts .el-pagination.is-background .el-pagination__sizes {
  margin-right: 0;
}
.device_label_button {
  // background: #eeeeee;
  background: #ffffff;
  color: #666666;
  // border: 0;
  border-color: #d9d9d9;
  width: 83px;
  height: 32px;
}
.device_active {
  background: #ffffff;
  border: 1px solid #0080ff;
  border-radius: 4px;
  font-weight: 400;
  color: #0080ff;
}

.dataUpdateTime {
  display: flex;
  align-items: baseline;

  > span:first-child {
    margin-right: 10px;
    color: #333333;
    white-space: nowrap;
  }

  > span:last-child {
    flex: 1;
    text-align: right;
    color: #666666;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
@keyframes ani1 {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
@-webkit-keyframes ani1 {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.is-standard {
  text-align: center;
  background: #e1f4f9;
  // padding: 5px 0 5px 5px !important;
  font-size: 12px;
  padding: 5px 5px;
  color: #04beec;
  border-radius: 3px;
}
.is-standard-bgf {
  text-align: center;
  border-radius: 3px;
  color: #fd0505;
  background: #f9e2e2;
  padding: 5px 5px;
  font-size: 12px;
}
.underlines-my {
  border-bottom: 1px solid #f0f0f0;
}

.mapInfo {
  position: relative;
  padding: 1px 0 1px 12px;
  img {
    position: absolute;
    top: 0;
    left: 0;
  }
}
.user-havior-group {
  li {
    width: 174px;
    height: 76px;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    background: #eaf2fa;
    color: #333333;
    box-sizing: border-box;
  }

  .active {
    background-image: url('@/assets/image/realTimeMonitor/ssjc_bg.png');
    background-size: 116% 122%;
    background-position-x: center;
    background-position-y: center;
    width: 174px;
    height: 76px;
    position: relative;
    border: none;
    color: #ffffff;
  }
}

.el-table__row--level-1 {
  td:first-child {
    position: relative;
    // overflow: inherit;

    &::before {
      position: absolute;
      top: 50%;
      transform: translateY(-60%);
      left: 5%;
      z-index: 200;
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid #3093fa;
      background-color: #fff;
    }
    &::after {
      position: absolute;
      top: -40%;
      left: 8.5%;
      z-index: 199;
      content: '';
      display: inline-block;
      width: 1px;
      height: 100%;
      border: none;

      border-left: 1px dashed #9c9c9c;
    }
  }
}
.el-table__row:first-child {
  td {
    &::after {
      display: none;
    }
  }
}

.table-list {
  .el-table--border::before {
    width: 0px !important;
  }
  .el-table--border::after {
    width: 0px !important;
  }
  .el-table__border-left-patch {
    width: 0px;
  }
  // .table-list .el-table__inner-wrapper .el-table__header-wrapper .el-table__header .el-table__cell

  .el-table__header-wrapper {
    .el-table__header {
      .el-table__cell {
        border-left: none !important;
        // border-left: none!important;
        border-right: none !important;
        .cell:hover {
          border-right: 1px solid #e5e7eb;
        }
      }
      // .el-table__cell:last-child{
      //  border-right: none!important;
      // }
    }
  }

  header {
    background: white;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    position: relative;
    .right-content {
      position: absolute;
      right: 20px;
      bottom: 20px;
      margin-top: 20px;
    }

    .el-row {
      margin-bottom: 0;
    }
  }

  .el-table__inner-wrapper {
    display: flex;
    flex-direction: column;

    .el-table__header-wrapper {
      .el-table__header {
        .el-table__cell {
          background: #fafafa;
          padding: 5px 0;
          .cell {
            color: #333333;
          }
        }
      }
    }

    .el-table__body-wrapper {
      flex: 1;
    }
    .el-table__row {
      .el-table__cell {
        // border: none!important;
        border-left: none !important;
        border-right: none !important;
      }
    }
  }

  .el-table__body {
    .el-table__row {
      &:hover {
        // background: #e6f7ff !important;
        td {
          background: #e6f7ff !important;
        }
      }

      .el-table__cell {
        color: #333333;
        // border-bottom: none;
        border-bottom: 1px solid #e5e7eb;
      }
    }

    .el-table__row--striped {
      .el-table__cell {
        background: #f8faff !important;
      }
    }
  }

  .table-list_wrap {
    background: white;
    padding: 20px 20px 55px 20px;
    box-sizing: border-box;

    .el-table {
      height: 100%;

      .el-table__inner-wrapper,
      .el-table__body-wrapper {
        height: 100%;
      }
    }
  }
  .hasTop {
    // padding-top: 0;
  }

  .pagination-wrap {
    // position: absolute;
    // right: 10px;
    // top: 800px;
    .el-pagination {
      button {
        margin: 0 5px !important;
      }

      .btn-prev {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
      }

      .btn-next {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
      }

      .el-pager li {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
        margin: 0 3px;
      }

      .el-pager li.is-active {
        border: 1px #335cff solid;
        border-radius: 4px 4px 4px 4px;
      }

      .el-pagination__sizes {
        margin-left: 5px;
      }

      .el-input {
        .el-input__inner {
          padding-left: 10px !important;
          text-align: left;
        }
      }

      .el-select {
        height: 32px;

        .el-input__inner {
          height: 32px;
        }

        .el-input {
          width: 110px;
        }
      }
    }
  }
  .pagination-wrap-new {
    .el-pagination {
      position: absolute;
      right: -20px;
      bottom: 0;

      .el-pagination__total {
        margin-right: 10px;
      }

      button {
        // margin: 0 1px !important;
      }

      .btn-prev {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
      }

      .btn-next {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
      }

      .el-pager li {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
        margin: 0 2px;
      }

      .el-pager li.is-active {
        border: 1px #335cff solid;
        border-radius: 4px 4px 4px 4px;
      }

      .el-pagination__sizes {
        margin-left: 2px;
      }

      .el-select {
        height: 32px;

        .el-input__inner {
          height: 32px;
        }

        .el-input {
          width: 110px;
        }
      }
    }
  }
}

.el-table .el-table__cell {
  padding: 5px 0;
}
.foot-title {
  color: #848587;
  font-size: 14px;
  font-weight: 400;
}

.text_tip .el-input__inner,
.text_tip .el-textarea__inner {
  color: #999;
}

.my-notification.el-notification .el-notification__icon {
  color: #409eff;
  svg {
    width: 1.4em;
    height: 1.4em;
  }
}

.organizationalStructureTree {
  .el-cascader__tags {
    z-index: 9;
  }

  .el-popper {
    inset: 0px auto auto -8px !important;
  }
}

.scrollbar-overflow-auto {
  overflow: hidden;
  &:hover {
    overflow-y: overlay;
    scrollbar-gutter: stable;
  }
}

.el-tabs:not(.tabs) .el-tabs__content {
  overflow: hidden !important;
  &:hover {
    overflow-y: overlay !important;
    scrollbar-gutter: stable;
  }
}

.complex.el-popper {
  background: #0d2048 !important;
  border: 1px solid #15aaff !important;
}

.tip.complex.el-popper {
  max-width: 460px;
}

.complex.tips.el-popper {
  max-width: 450px;
}

.complex .el-select-dropdown__item,
.complex .el-select-dropdown__item.hover,
.complex .el-cascader-node {
  color: #fff;
  &:hover {
    background: #527397;
    color: #09c2ff;
  }
}

.complex .el-select-dropdown__item.hover,
.complex .el-cascader-node:not(.is-disabled):hover,
.complex .el-cascader-node:not(.is-disabled):focus {
  background: #527397;
  color: #09c2ff;
}

.complex .el-picker-panel {
  background: #0d2048;
}

.complex .el-date-picker__header-label,
.complex .el-picker-panel__icon-btn,
.complex .el-date-table th,
.complex .el-date-table td,
.complex .el-date-table td.disabled .el-date-table-cell {
  color: #fff;
  background: #0d2048;
}

.complex .el-date-table td.disabled .el-date-table-cell {
  background: #527397;
  .el-date-table-cell__text {
    color: #ccc;
  }
}

.complex .el-popper__arrow {
  &::before {
    background: #0d2048 !important;
    border: 1px solid #15aaff !important;
  }
}

.complex .el-date-picker__header--bordered {
  border-bottom: unset;
}

.complex .el-year-table td .cell {
  color: #fff;
}

.riskFactorsPopper {
  max-width: 50em;
}

.deviceTypeCascader {
  position: relative;
  z-index: 9;
}

.deviceTypeCascader .el-cascader__tags {
  flex-wrap: nowrap;
}

.deviceTypeCascader .el-popper.is-light {
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  max-height: 120px;
  overflow-y: auto;
  overflow-x: hidden;
}

.deviceTypeCascader .el-tag__content {
  height: 100%;
  display: inline-flex;
  align-items: center;
}

.text-title {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
}