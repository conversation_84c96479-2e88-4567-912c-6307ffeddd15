<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3617508" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">chartcolumn</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">分类</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">AK-YK_饼图_fill</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b2;</span>
                <div class="name">pie-chart</div>
                <div class="code-name">&amp;#xe7b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">退出全屏</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ba;</span>
                <div class="name">监控</div>
                <div class="code-name">&amp;#xe6ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67c;</span>
                <div class="name">安全设备</div>
                <div class="code-name">&amp;#xe67c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe689;</span>
                <div class="name">问题</div>
                <div class="code-name">&amp;#xe689;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">安全整改on</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">订单分布</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">指标结果</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">1-2-1-分布式</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe87b;</span>
                <div class="name">趋势</div>
                <div class="code-name">&amp;#xe87b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b7;</span>
                <div class="name">帮助文档</div>
                <div class="code-name">&amp;#xe6b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">企业认证2</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">运维-系统检测</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ea;</span>
                <div class="name">分包单位</div>
                <div class="code-name">&amp;#xe6ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">图表</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ce;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe7ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">导出</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">新增</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c9;</span>
                <div class="name">设备</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">楼房</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">组织机构</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a0;</span>
                <div class="name">监测预警</div>
                <div class="code-name">&amp;#xe6a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe791;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe791;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">过程监控-监督核查</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">系统管理</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1670470788225') format('woff2'),
       url('iconfont.woff?t=1670470788225') format('woff'),
       url('iconfont.ttf?t=1670470788225') format('truetype'),
       url('iconfont.svg?t=1670470788225#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-chartcolumn"></span>
            <div class="name">
              chartcolumn
            </div>
            <div class="code-name">.icon-chartcolumn
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paixu"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenlei"></span>
            <div class="name">
              分类
            </div>
            <div class="code-name">.icon-fenlei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yk_bingtu_fill"></span>
            <div class="name">
              AK-YK_饼图_fill
            </div>
            <div class="code-name">.icon-yk_bingtu_fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-piechart"></span>
            <div class="name">
              pie-chart
            </div>
            <div class="code-name">.icon-piechart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-quanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuichuquanping"></span>
            <div class="name">
              退出全屏
            </div>
            <div class="code-name">.icon-tuichuquanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiankong"></span>
            <div class="name">
              监控
            </div>
            <div class="code-name">.icon-jiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-anquanshebei"></span>
            <div class="name">
              安全设备
            </div>
            <div class="code-name">.icon-anquanshebei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenti"></span>
            <div class="name">
              问题
            </div>
            <div class="code-name">.icon-wenti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-anquanzhenggaion"></span>
            <div class="name">
              安全整改on
            </div>
            <div class="code-name">.icon-anquanzhenggaion
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingdanfenbu"></span>
            <div class="name">
              订单分布
            </div>
            <div class="code-name">.icon-dingdanfenbu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhibiaojieguo"></span>
            <div class="name">
              指标结果
            </div>
            <div class="code-name">.icon-zhibiaojieguo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenbushi"></span>
            <div class="name">
              1-2-1-分布式
            </div>
            <div class="code-name">.icon-fenbushi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qushi"></span>
            <div class="name">
              趋势
            </div>
            <div class="code-name">.icon-qushi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bangzhuwendang"></span>
            <div class="name">
              帮助文档
            </div>
            <div class="code-name">.icon-bangzhuwendang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiyerenzheng2"></span>
            <div class="name">
              企业认证2
            </div>
            <div class="code-name">.icon-qiyerenzheng2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yunwei-xitongjiance"></span>
            <div class="name">
              运维-系统检测
            </div>
            <div class="code-name">.icon-yunwei-xitongjiance
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenbaodanwei"></span>
            <div class="name">
              分包单位
            </div>
            <div class="code-name">.icon-fenbaodanwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tubiao"></span>
            <div class="name">
              图表
            </div>
            <div class="code-name">.icon-tubiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liebiao"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.icon-liebiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjian"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.icon-wenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daochu"></span>
            <div class="name">
              导出
            </div>
            <div class="code-name">.icon-daochu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinzeng"></span>
            <div class="name">
              新增
            </div>
            <div class="code-name">.icon-xinzeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shebei"></span>
            <div class="name">
              设备
            </div>
            <div class="code-name">.icon-shebei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-loufang"></span>
            <div class="name">
              楼房
            </div>
            <div class="code-name">.icon-loufang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuzhijigou"></span>
            <div class="name">
              组织机构
            </div>
            <div class="code-name">.icon-zuzhijigou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jianceyujing"></span>
            <div class="name">
              监测预警
            </div>
            <div class="code-name">.icon-jianceyujing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.icon-shouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guochengjiankong-jianduhecha"></span>
            <div class="name">
              过程监控-监督核查
            </div>
            <div class="code-name">.icon-guochengjiankong-jianduhecha
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xitongguanli"></span>
            <div class="name">
              系统管理
            </div>
            <div class="code-name">.icon-xitongguanli
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chartcolumn"></use>
                </svg>
                <div class="name">chartcolumn</div>
                <div class="code-name">#icon-chartcolumn</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenlei"></use>
                </svg>
                <div class="name">分类</div>
                <div class="code-name">#icon-fenlei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yk_bingtu_fill"></use>
                </svg>
                <div class="name">AK-YK_饼图_fill</div>
                <div class="code-name">#icon-yk_bingtu_fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-piechart"></use>
                </svg>
                <div class="name">pie-chart</div>
                <div class="code-name">#icon-piechart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-quanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuichuquanping"></use>
                </svg>
                <div class="name">退出全屏</div>
                <div class="code-name">#icon-tuichuquanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiankong"></use>
                </svg>
                <div class="name">监控</div>
                <div class="code-name">#icon-jiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-anquanshebei"></use>
                </svg>
                <div class="name">安全设备</div>
                <div class="code-name">#icon-anquanshebei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenti"></use>
                </svg>
                <div class="name">问题</div>
                <div class="code-name">#icon-wenti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-anquanzhenggaion"></use>
                </svg>
                <div class="name">安全整改on</div>
                <div class="code-name">#icon-anquanzhenggaion</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingdanfenbu"></use>
                </svg>
                <div class="name">订单分布</div>
                <div class="code-name">#icon-dingdanfenbu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhibiaojieguo"></use>
                </svg>
                <div class="name">指标结果</div>
                <div class="code-name">#icon-zhibiaojieguo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenbushi"></use>
                </svg>
                <div class="name">1-2-1-分布式</div>
                <div class="code-name">#icon-fenbushi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qushi"></use>
                </svg>
                <div class="name">趋势</div>
                <div class="code-name">#icon-qushi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bangzhuwendang"></use>
                </svg>
                <div class="name">帮助文档</div>
                <div class="code-name">#icon-bangzhuwendang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiyerenzheng2"></use>
                </svg>
                <div class="name">企业认证2</div>
                <div class="code-name">#icon-qiyerenzheng2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunwei-xitongjiance"></use>
                </svg>
                <div class="name">运维-系统检测</div>
                <div class="code-name">#icon-yunwei-xitongjiance</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenbaodanwei"></use>
                </svg>
                <div class="name">分包单位</div>
                <div class="code-name">#icon-fenbaodanwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tubiao"></use>
                </svg>
                <div class="name">图表</div>
                <div class="code-name">#icon-tubiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiao"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#icon-liebiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjian"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#icon-wenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daochu"></use>
                </svg>
                <div class="name">导出</div>
                <div class="code-name">#icon-daochu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinzeng"></use>
                </svg>
                <div class="name">新增</div>
                <div class="code-name">#icon-xinzeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shebei"></use>
                </svg>
                <div class="name">设备</div>
                <div class="code-name">#icon-shebei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-loufang"></use>
                </svg>
                <div class="name">楼房</div>
                <div class="code-name">#icon-loufang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuzhijigou"></use>
                </svg>
                <div class="name">组织机构</div>
                <div class="code-name">#icon-zuzhijigou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianceyujing"></use>
                </svg>
                <div class="name">监测预警</div>
                <div class="code-name">#icon-jianceyujing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#icon-shouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guochengjiankong-jianduhecha"></use>
                </svg>
                <div class="name">过程监控-监督核查</div>
                <div class="code-name">#icon-guochengjiankong-jianduhecha</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xitongguanli"></use>
                </svg>
                <div class="name">系统管理</div>
                <div class="code-name">#icon-xitongguanli</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
