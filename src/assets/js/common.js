/*
 * @Descripttion:
 * @Author: llb
 * @Date:20220408
 * @LastEditors: Please set LastEditors
 * @LastEditTime:
 */
import { parseTime } from '@/utils'
/**
 * @name: 用于共用组件时  不同模块请求不同接口方法
 * @param {*} apis => 当前模块引入整体接口文件
 * @param {*} apiObj => 共用请求接口对应接口名称数据对象
 * @param {*} name => 共用请求接口对应名称通过name取请求接口方法名
 * @param {*} data => 接口请求传入参数
 */
export const getApi = (apis, apiObj, name, data) => {
  const currentApiName = apiObj[name]
  return new Promise((resolve, reject) => {
    apis[currentApiName](data)
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

export const btnListSort = (data) => {
  const compare = (property) => {
    return function (a, b) {
      return a[property] - b[property]
    }
  }
  if (Array.isArray(data) && data.length > 0) {
    data.sort(compare('buttonXh'))
    return data
  } else {
    return []
  }
}

// export const dealShowFlowBtn = (data) => {
//   return data.map((item) => {
//     switch (item.buttonName) {
//       case "提交":
//         item.btnType = "success";
//         item.btnIcon = "el-icon-check";
//         break;
//       case "驳回":
//         item.btnType = "danger";
//         item.btnIcon = "el-icon-back";
//         break;
//       case "通过":
//         item.btnType = "success";
//         item.btnIcon = "el-icon-circle-check";
//         break;
//     }
//     return item;
//   });
// }

// 过滤对象中为null/undefined/''/[]/{}的属性值
export const clearDeep = (data) => {
  let obj = JSON.parse(JSON.stringify(data))
  if (!obj || !typeof obj == 'object') return

  const keys = Object.keys(obj)
  for (var key of keys) {
    const val = obj[key]
    if (typeof val === 'undefined' || ((typeof val === 'object' || typeof val === 'string') && !val)) {
      // 如属性值为null或undefined或''，则将该属性删除
      delete obj[key]
    } else if (typeof val === 'object') {
      // 属性值为对象，递归调用
      this.clearDeep(obj[key])

      if (Object.keys(obj[key]).length === 0) {
        // 如某属性的值为不包含任何属性的独享，则将该属性删除
        delete obj[key]
      }
    }
  }
  return obj
}

// 设置默认日期范围 （最近7天，如：2017-05-01 至 2017-05-07）
export const initDateTime = () => {
  const currentDate = new Date()
  const old7Date = currentDate.setDate(currentDate.getDate() - 7)
  const format = '{y}-{m}-{d}'
  return [parseTime(old7Date, format), parseTime(new Date(), format)]
}

// 字符串首字母转大写
export const firstToUpper = (str) => {
  return str.toLowerCase().replace(/( |^)[a-z]/g, (L) => L.toUpperCase())
}
