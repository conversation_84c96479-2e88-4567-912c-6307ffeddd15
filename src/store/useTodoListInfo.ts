import { defineStore } from 'pinia'
import { computed } from 'vue'

const useTodoListStore = defineStore('todoList', {
  state: () => {
    return {
      todoListInfo: {},
      popupShow: false
    }
  },

  actions: {
    setTodoListInfo(payload) {
      this.todoListInfo = payload
    },
    setPopupShow(isShow) {
      this.popupShow = isShow
    }
  },

  persist: true
})

export function useTodoListInfo() {
  const uis = useTodoListStore()

  return computed({
    get: () => uis.todoListInfo || {},

    set(val: any) {
      uis.setTodoListInfo(val)
    }
  })
}
export function updatePopupShow() {
  const uis = useTodoListStore()

  return computed({
    get: () => uis.popupShow || false,

    set(val: any) {
      uis.setPopupShow(val)
    }
  })
}
