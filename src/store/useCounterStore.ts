import { defineStore } from 'pinia'

export default defineStore('counter', {
  state: () =>
    ({
      count: 0,
      isCollapse: false,
      isShowMapDetail: false,
      currentUnit: {},
      ztUnit: {},
      recordsData: {},
      messageNum: 0,
    }) as {
      count: number
      isCollapse: boolean
      isShowMapDetail: boolean
      currentUnit: any
      ztUnit: any
      recordsData: any
      messageNum: number
    },

  actions: {
    increase() {
      this.count++
    },
  },
})
