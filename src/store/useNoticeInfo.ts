import { defineStore } from 'pinia'
import { computed } from 'vue'

const useNoticeStore = defineStore('notice', {
  state: () => {
    return {
      noticeInfo: {}
    }
  },

  actions: {
    setNoticeInfo(payload) {
      this.noticeInfo = payload
    }
  },

  persist: true
})

export default function useNoticeInfo() {
  const uis = useNoticeStore()

  return computed({
    get: () => uis.noticeInfo || {},

    set(val: any) {
      uis.setNoticeInfo(val)
    }
  })
}
