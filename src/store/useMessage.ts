import { defineStore } from 'pinia'
import { getMessageGroupList } from '@/common/services'
import $API from '~/common/api'

export default defineStore('message', {
    state() {
        return {
            count: 0,
            messages: [] as any[],
            announcementList: [] as any[]
        }
    },

    actions: {
        async updateMessageCount() {
            const [count, messages] = await getMessageGroupList();
            this.count = count as number;
            this.messages = messages as any[];
        },

        // getAnnouncement(unitId) {
        //     $API.post({
        //         url: '/notice/queryNoticeInfoList',
        //         params: {
        //             unitId,
        //             pageNo: -1,
        //             noticeDisplayLocation: 1,
        //             publishStatus: 1
        //         }
        //     })
        //     .then((res: any) => {
        //         if(res && res.code == "success"){
        //             this.announcementList = res.data.rows
        //         }
        //     })
        // }
    }
})