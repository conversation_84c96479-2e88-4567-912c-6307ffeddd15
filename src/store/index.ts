import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

const piniaInstance = createPinia()

piniaInstance.use(
  createPersistedState({
    storage: window.sessionStorage,
    beforeRestore: () => {},
    afterRestore: () => {},
    serializer: {
      serialize: JSON.stringify,
      deserialize: JSON.parse
    }
  })
)

export default piniaInstance
export { default as useCounterStore } from './useCounterStore'
export { default as useUserInfo } from './useUserInfo'
export { default as unitMapStore } from './unitMap'
export { default as useNoticeInfo } from './useNoticeInfo'
export { default as useInformationInfo } from './useInformation'
export { useTodoListInfo, updatePopupShow } from './useTodoListInfo'
export { userReportInfoStore, useSystemDynamicsInfo } from './useReportInfo'
// export {default as useMessage} from './useMessage'
