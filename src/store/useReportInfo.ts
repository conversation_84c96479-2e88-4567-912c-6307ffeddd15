import { defineStore } from 'pinia'
import { computed } from 'vue'

export const userReportInfoStore = defineStore('report', {
  state: () => {
    return {
      bool: false,
      loading: false
    }
  },

  actions: {},

  getters: {}
})

const useSystemDynamicsStore = defineStore('systemDynamics', {
  state: () => {
    return {
      systemDynamicsInfo: {}
    }
  },

  actions: {
    setSystemDynamicsStore(payload) {
      this.systemDynamicsInfo = payload
    }
  },

  persist: true
})

export function useSystemDynamicsInfo() {
  const uis = useSystemDynamicsStore()

  return computed({
    get: () => uis.systemDynamicsInfo || {},

    set(val: any) {
      uis.setSystemDynamicsStore(val)
    }
  })
}
