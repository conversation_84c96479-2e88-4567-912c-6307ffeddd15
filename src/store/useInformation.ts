import { defineStore } from 'pinia'
import { computed } from 'vue'

const useInformationStore = defineStore('information', {
  state: () => {
    return {
      informationInfo: {}
    }
  },

  actions: {
    setInformationInfo(payload) {
      this.informationInfo = payload
    }
  },

  persist: true
})

export default function useInformationInfo() {
  const uis = useInformationStore()

  return computed({
    get: () => uis.informationInfo || {},

    set(val: any) {
      uis.setInformationInfo(val)
    }
  })
}
