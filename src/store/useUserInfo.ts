import { defineStore } from 'pinia'
import { computed } from 'vue'
import { UserInfo } from '@/types'
import config from '@/config'
import { getUserInfo } from '@/common/utils'

const userInfoStore = defineStore(config.USER_IFNO_NAMESPACE, {

    state: () => {
 
        return {
            userInfo: getUserInfo()
        } as {
            userInfo: UserInfo
        }
    },

    actions: {
        setUserInfo(payload) {
            this.userInfo = payload;
        }
    },

    persist: true
})

export default function useUserInfo() {
    const uis = userInfoStore();

    return computed({
        get: () => uis.userInfo || {},
        
        set(val: UserInfo) {
            uis.setUserInfo(val)
        }
    })
}