interface Window {
  GISShare: any
  IndoorMap: any
  IndoorScene: any
  ol: any
  //
  IndoorService: any
  //
  CONST_OVBuildStyleInfo_Default: any
  //
  CONST_GSCache: GSCache
  CONST_GSUrlHeader: GSUrlHeader
  CONST_GSOptions: GSOptions
  CONST_GSParams: GSParams
  //
  onBuildPopupTop: any
  onBuildPopupClose: any
  //
  CONST_Indoor: any
  CONST_Indoor_Preview: any
  CONST_Indoor_Map: any
  //
  QueryVideoDevice: any
  //
  ConfigureScene: any
  CONST_GSOptions_configure: GSOptions
  mapSender: any
}

interface GSCache {
  adminCodeDicCache: any
  indoorAreaDicCache_Map: any
  indoorAreaDicCache_Scene: any
  indoorAreaExtentDicCache_Map: any
  indoorAreaExtentDicCache_Scene: any
  gridAreaDicCache_Map: any
  gridAreaDicCache_Scene: any
  ovUnitModelInfoDicCache_Scene: any
}
interface GSUrlHeader {
  bwUrlHeader: string
  gsUrlHeader: string
}
interface GSOptions {
  dbService: any
  dbService_Record: any
  unitUrlHeader: string
  wmsURL: string
  deviceIconUrlHeader: string
  deviceIconAlarmGifUrl: string
  skyUrl: any[]
  ovUnitModelUrlHeader: string
  deviceFieldNameState: string
  deviceFieldNameOnlineState: string
  deviceStateValueConvertFun: any
}
interface GSParams {
  styleInfo_2D: any
  styleInfoGrid_2D: any
  styleInfo_3D: any
  styleInfoGrid_3D: any
  tileURL: string
  tileURL_Satellite: string
  tileURL_Traffic: string
  tileURL_Annotation: string
}

declare let H5sPlayerCreate: any
declare const CONST_GeoData_China_15:any
declare const CONST_GeoData_China_line_15:any
declare const CONST_GeoData_Item_15:any
declare const CONST_GeoData_Item_inline_15:any
declare const CONST_GeoData_Item_outline_15:any
