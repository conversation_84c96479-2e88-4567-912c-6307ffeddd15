/*
 * @Author: <PERSON>
 * @Date: 2023-09-14 15:51:29
 * @LastEditors: <PERSON> su
 * @LastEditTime: 2023-09-14 18:06:58
 * @FilePath: /supervise-clnt-platform-web/.stylelintrc.js
 */
module.exports = {
  root: true,
  extends: ['stylelint-config-standard', 'stylelint-config-recommended-vue'],
  rules: {
    // 代码缩进
    indentation: 4,
    // 是否需要在属性声明前空一行
    'declaration-empty-line-before': 'never',
    // string: "lower"|"upper"。通过设置null关闭指定单位大小写
    'unit-case': null,
    // 在开括号之后要求有一个换行符
    'block-opening-brace-newline-after': 'always',
    // 在闭括号之前要求有一个换行符或禁止有空白
    'block-closing-brace-newline-before': 'always',

    // class命名规范
    'selector-class-pattern': null,
    // 禁止空源
    'no-empty-source': null,
    // 在自定义属性之前要求或禁止空行
    'custom-property-empty-line-before': 'never',
    // 限制数值的小数位数
    'number-max-precision': null
  }
}
