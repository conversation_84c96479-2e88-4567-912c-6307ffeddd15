import { ref, computed, ComputedRef, unref } from 'vue'

import { useEventListener } from './useEventListener'

// 导入断点枚举值，用于判断当前屏幕大小
import { screenMap, sizeEnum, screenEnum } from './breakpointEnum'

// 声明全局的ComputedRef类型的变量，用于存储屏幕大小枚举值
let globalScreenRef: ComputedRef<sizeEnum | undefined>
// 声明全局的ComputedRef类型的变量，用于存储屏幕宽度
let globalWidthRef: ComputedRef<number>
// 声明全局的ComputedRef类型的变量，用于存储实际屏幕宽度
let globalRealWidthRef: ComputedRef<number>

// 定义一个CreateCallbackParams接口，用于定义传递给回调函数的参数类型
export interface CreateCallbackParams {
  screen: ComputedRef<sizeEnum | undefined>
  width: ComputedRef<number>
  realWidth: ComputedRef<number>
  screenEnum: typeof screenEnum
  screenMap: Map<sizeEnum, number>
  sizeEnum: typeof sizeEnum
}

// 定义一个useBreakpoint函数，返回一个对象，其中包含当前屏幕大小、屏幕宽度、屏幕枚举和实际宽度
export function useBreakpoint() {
  return {
    // 使用全局的screenRef，但转换为非ref的形式
    screenRef: computed(() => unref(globalScreenRef)),
    // 使用全局的宽度ref
    widthRef: globalWidthRef,
    // 导入的screenEnum
    screenEnum,
    // 使用全局的实际宽度ref
    realWidthRef: globalRealWidthRef,
  }
}

// 定义一个createBreakpointListen函数，用于监听窗口大小变化，并触发回调函数
export function createBreakpointListen(fn?: (opt: CreateCallbackParams) => void) {
  // 使用ref创建一个响应式的屏幕大小枚举值变量
  const screenRef = ref<sizeEnum>(sizeEnum.XL)
  // 使用ref创建一个响应式的实际屏幕宽度变量
  const realWidthRef = ref(window.innerWidth)

  // 定义一个函数，用于获取窗口宽度并设置screenRef的值
  function getWindowWidth() {
    const width = document.body.clientWidth
    const xs = screenMap.get(sizeEnum.XS)!
    const sm = screenMap.get(sizeEnum.SM)!
    const md = screenMap.get(sizeEnum.MD)!
    const lg = screenMap.get(sizeEnum.LG)!
    const xl = screenMap.get(sizeEnum.XL)!
    if (width < xs) {
      screenRef.value = sizeEnum.XS
    } else if (width < sm) {
      screenRef.value = sizeEnum.SM
    } else if (width < md) {
      screenRef.value = sizeEnum.MD
    } else if (width < lg) {
      screenRef.value = sizeEnum.LG
    } else if (width < xl) {
      screenRef.value = sizeEnum.XL
    } else {
      screenRef.value = sizeEnum.XXL
    }
    realWidthRef.value = width
  }

  // 使用useEventListener监听窗口的resize事件
  useEventListener({
    el: window,
    name: 'resize',

    // 当窗口大小变化时，调用getWindowWidth函数并触发resizeFn函数
    listener: () => {
      getWindowWidth()
      resizeFn()
    },
    // // 可选：等待一段时间后再触发事件，此处未启用
    // wait: 100,
  })

  // 初始化窗口宽度和screenRef的值
  getWindowWidth()

  // 创建全局的screenRef、widthRef和realWidthRef的computed版本
  globalScreenRef = computed(() => unref(screenRef))
  globalWidthRef = computed((): number => screenMap.get(unref(screenRef)!)!)
  globalRealWidthRef = computed((): number => unref(realWidthRef))

  // 定义一个resizeFn函数，用于触发回调函数，并传递相关参数
  function resizeFn() {
    fn?.({
      screen: globalScreenRef,
      width: globalWidthRef,
      realWidth: globalRealWidthRef,
      screenEnum,
      screenMap,
      sizeEnum,
    })
  }
}
