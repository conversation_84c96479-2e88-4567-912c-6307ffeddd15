// 定义尺寸枚举，表示不同的尺寸标签
export enum sizeEnum {
  XS = 'XS', // 超小尺寸
  SM = 'SM', // 小尺寸
  MD = 'MD', // 中等尺寸
  LG = 'LG', // 大尺寸
  XL = 'XL', // 超大尺寸
  XXL = 'XXL', // 特大尺寸
}

// 定义屏幕尺寸枚举，表示不同尺寸的屏幕宽度（单位：像素）
export enum screenEnum {
  XS = 320, // 超小屏幕宽度
  SM = 640, // 小屏幕宽度
  MD = 768, // 中等屏幕宽度
  LG = 960, // 大屏幕宽度
  XL = 1280, // 超大屏幕宽度
  XXL = 1536, // 特大屏幕宽度
}

// 创建一个映射，将sizeEnum枚举的尺寸标签映射到screenEnum枚举的屏幕宽度
const screenMap = new Map<sizeEnum, number>()

// 设置映射关系
screenMap.set(sizeEnum.XS, screenEnum.XS)
screenMap.set(sizeEnum.SM, screenEnum.SM)
screenMap.set(sizeEnum.MD, screenEnum.MD)
screenMap.set(sizeEnum.LG, screenEnum.LG)
screenMap.set(sizeEnum.XL, screenEnum.XL)
screenMap.set(sizeEnum.XXL, screenEnum.XXL)

// 导出screenMap供外部使用
export { screenMap }
