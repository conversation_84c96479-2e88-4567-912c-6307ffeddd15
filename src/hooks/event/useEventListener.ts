import type { Ref } from 'vue'
import { ref, watch, unref } from 'vue'
import { useThrottleFn, useDebounceFn } from '@vueuse/core'

// 定义移除事件的函数类型
export type RemoveEventFn = () => void

// 定义使用事件监听器的参数接口
export interface UseEventParams {
  el?: Element | Ref<Element | undefined> | Window | any
  name: string
  listener: EventListener
  options?: boolean | AddEventListenerOptions
  autoRemove?: boolean
  isDebounce?: boolean
  wait?: number
}

// 定义一个函数用于添加事件监听器
export function useEventListener({
  el = window, // 默认监听对象为window
  name, // 事件名称
  listener, // 事件处理函数
  options, // 事件监听选项
  autoRemove = true, // 是否自动移除监听器
  isDebounce = true, // 是否使用防抖
  wait = 80, // 防抖或节流的等待时间
}: UseEventParams): { removeEvent: RemoveEventFn } {
  let remove: RemoveEventFn = () => {}
  const isAddRef = ref(false) // 标记是否已添加监听器

  if (el) {
    const element = ref(el as Element) as Ref<Element>

    // 根据isDebounce选择使用防抖或节流函数包装listener
    const handler = isDebounce ? useDebounceFn(listener, wait) : useThrottleFn(listener, wait)
    const realHandler = wait ? handler : listener // 如果设置了wait时间，则使用包装后的handler

    // 定义移除事件监听器的函数
    const removeEventListener = (e: Element) => {
      isAddRef.value = true
      e.removeEventListener(name, realHandler, options)
    }

    // 定义添加事件监听器的函数
    const addEventListener = (e: Element) => e.addEventListener(name, realHandler, options)

    // 监视element的变化，添加或移除事件监听器
    const removeWatch = watch(
      element,
      (v, _ov, cleanUp) => {
        if (v) {
          !unref(isAddRef) && addEventListener(v)
          cleanUp(() => {
            autoRemove && removeEventListener(v)
          })
        }
      },
      { immediate: true }
    )

    // 定义移除所有监听器的函数
    remove = () => {
      removeEventListener(element.value)
      removeWatch()
    }
  }
  return { removeEvent: remove }
}
