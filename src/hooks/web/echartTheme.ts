// 定义一个名为riskTheme的主题配置对象，用于ECharts图表
export const riskTheme = {
  // 定义图表使用的颜色数组
  color: ['#ff4b0f', '#ff9702', '#ff7200', '#ffb400', '#f9896f', '#f9b36f', '#f9da6f', '#f96f6f'],
  // 图表的背景颜色，透明
  backgroundColor: 'rgba(0, 0, 0, 0)',
  // 全局文本样式（默认为空对象）
  textStyle: {},
  // 标题的样式配置
  title: {
    textStyle: {
      color: '#9b540f', // 标题文本颜色
    },
    subtextStyle: {
      color: '#9b540f', // 副标题文本颜色
    },
  },
  // 线图的配置
  line: {
    itemStyle: {
      borderWidth: '0', // 数据点的边框宽度
    },
    lineStyle: {
      width: '2', // 线的宽度
    },
    symbol: 'emptyCircle', // 数据点的形状
    smooth: false, // 是否平滑曲线
  },
  // 饼图的配置
  pie: {
    color: ['#ff9702', '#ff7200', '#ffb400', '#f9896f', '#ff4b0f', '#f9b36f', '#f9da6f', '#f96f6f'],
    itemStyle: {
      borderWidth: 0, // 数据块的边框宽度
    },
  },
  // 类目轴的配置
  categoryAxis: {
    axisLine: {
      show: true, // 是否显示轴线
      lineStyle: {
        color: 'rgba(254,136,23,0.5)', // 轴线的颜色
      },
    },
    axisTick: {
      show: false, // 是否显示刻度
      lineStyle: {
        color: '#6E7079',
      },
    },
    axisLabel: {
      show: true, // 是否显示标签
      color: '#9b540f', // 标签颜色
    },
    splitLine: {
      show: false, // 是否显示分隔线
      lineStyle: {
        color: ['#E0E6F1'],
      },
    },
    splitArea: {
      show: false, // 是否显示分隔区域
      areaStyle: {
        color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
      },
    },
    nameTextStyle: {
      color: '#9b540f', // 轴名称的文本样式
    },
  },
  // 值轴的配置
  valueAxis: {
    nameTextStyle: {
      color: '#9b540f', // 轴名称的文本样式
    },
    axisLine: {
      show: false, // 是否显示轴线
      lineStyle: {
        color: 'rgba(254,136,23,0.1)', // 轴线的颜色
      },
    },
    axisTick: {
      show: false, // 是否显示刻度
      lineStyle: {
        color: '#6E7079',
      },
    },
    axisLabel: {
      show: true, // 是否显示标签
      color: '#9b540f', // 标签颜色
    },
    splitLine: {
      show: true, // 是否显示分隔线
      lineStyle: {
        color: ['rgba(254,136,23,0.1)'],
      },
    },
    splitArea: {
      show: false, // 是否显示分隔区域
      areaStyle: {
        color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
      },
    },
  },
  // 图例的文本样式
  legend: {
    textStyle: {
      color: '#333', // 文本颜色
    },
  },
  // 视觉映射组件的颜色配置
  visualMap: {
    color: ['#ff4b0f', '#ffb400'],
  },
  // 数据区域缩放组件的样式配置
  dataZoom: {
    backgroundColor: 'rgba(255, 255, 255, 0)', // 背景颜色，透明
    dataBackground: {
      lineStyle: {
        color: 'rgba(190, 188, 188, 1)', // 数据区域线的颜色
      },
      areaStyle: {
        color: 'rgba(224, 224, 224, 1)', // 数据区域背景的颜色
      },
    },
    selectedDataBackground: {
      lineStyle: {
        color: 'rgba(180, 180, 180, 1)', // 选中区域线的颜色
      },
      areaStyle: {
        color: 'rgba(239, 239, 239, 1)', // 选中区域背景的颜色
      },
    },
    fillerColor: 'rgba(215, 106, 106, 0)', // 填充颜色，透明
    borderColor: 'rgba(200, 200, 200, 1)', // 边框颜色
    handleStyle: {
      color: 'rgba(255, 255, 255, 1)', // 控制柄的颜色
      borderColor: 'rgba(203, 203, 203, 1)', // 控制柄边框的颜色
    },
    moveHandleStyle: {
      color: 'rgba(222, 222, 222, 1)', // 移动控制柄的颜色
      borderColor: null, // 移动控制柄边框的颜色，无
    },
    emphasis: {
      handleStyle: {
        color: null, // 焦点时控制柄的颜色，无
        borderColor: 'rgba(247, 131, 60, 1)', // 焦点时控制柄边框的颜色
      },
      moveHandleStyle: {
        color: 'rgba(247, 131, 60, 1)', // 焦点时移动控制柄的颜色
        borderColor: null, // 焦点时移动控制柄边框的颜色，无
      },
    },
    brushStyle: {
      color: 'rgba(235, 234, 234, 0.5)', // 刷选工具的颜色
    },
    height: 15, // 组件高度
    moveHandleSize: 5, // 移动控制柄的大小
  },
  // 标记点的配置
  markPoint: {
    symbolSize: 30, // 标记点的大小
    label: {
      position: 'top', // 标签的位置
    },
  },
}
