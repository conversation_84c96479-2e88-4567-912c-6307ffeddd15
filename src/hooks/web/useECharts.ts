import type { Ref } from 'vue'
import { tryOnUnmounted, useDebounceFn, useTimeoutFn } from '@vueuse/core'
import { unref, nextTick, watch, computed, ref } from 'vue'
// 引入自定义的事件监听器和断点钩子
import { useEventListener } from '../event/useEventListener'
import { useBreakpoint } from '../event/useBreakpoint'
import * as echarts from 'echarts'
import { isUnDef } from '@/utils/is'
// 引入ECharts主题配置
import { riskTheme } from './echartTheme'
import type { EChartsOption, SetOptionOpts } from 'echarts'

// 扩展ECharts的配置选项类型
interface MySetOptionOpts extends SetOptionOpts {
  clear?: boolean
  isEmpty?: boolean
  groupId?: string
}

// 定义通用函数类型
type Fn = (...args: any[]) => any

// 定义一个空的ECharts配置对象，用于无数据时显示
const emptyOption = {
  title: {
    text: '暂无数据',
    left: 'center',
    top: 'center',
  },
} as EChartsOption

// 定义一个钩子函数用于初始化和管理ECharts实例
export function useECharts(
  elRef: Ref<HTMLDivElement>, // ECharts容器的引用
  theme: 'light' | 'dark' | 'default' = 'default' // 主题配置，默认为'default'
) {
  // 计算当前的主题模式
  const getDarkMode = computed(() => {
    return theme === 'default' ? theme : theme //后期可以根据换肤去调整颜色
  })

  let chartInstance: echarts.ECharts | null = null // ECharts实例
  let resizeFn: Fn = resize // 用于调整图表大小的函数
  const cacheOptions = ref({}) as Ref<EChartsOption> // 缓存图表配置的引用
  let removeResizeFn: Fn = () => {} // 移除resize事件监听的函数

  // 使用防抖函数包装resize函数，防止频繁调用
  resizeFn = useDebounceFn(resize, 200)

  // 计算当前的图表配置
  const getOptions = computed((): EChartsOption => {
    if (getDarkMode.value !== 'dark') {
      return {
        toolbox: {
          show: false,
          right: 0,
          feature: {
            saveAsImage: {
              type: 'png',
              title: '下载',
            },
          },
        },
        ...cacheOptions.value,
      } as EChartsOption
    }
    return {
      toolbox: {
        show: true,
        right: 0,
        feature: {
          saveAsImage: {
            type: 'png',
            title: '下载',
            backgroundColor: '#ffffff',
          },
        },
      },
      backgroundColor: 'transparent',
      ...cacheOptions.value,
    } as EChartsOption
  })

  // 初始化ECharts图表
  function initCharts(t = theme, groupId?: string) {
    const el = unref(elRef) // 获取DOM元素
    if (!el || !unref(el)) {
      return
    }
    const theme = t == 'light' ? riskTheme : t // 根据主题选择配置
    chartInstance = echarts.init(el, theme) // 初始化图表
    groupId ? (chartInstance.group = groupId) : null // 设置图表组ID

    // 添加窗口大小变化的事件监听
    const { removeEvent } = useEventListener({
      el: window,
      name: 'resize',
      listener: resizeFn,
    })
    removeResizeFn = removeEvent
    const { widthRef, screenEnum } = useBreakpoint() // 获取屏幕宽度的响应式引用
    if (unref(widthRef) <= screenEnum.MD || el.offsetHeight === 0) {
      useTimeoutFn(() => {
        resizeFn() // 调整图表大小
      }, 30)
    }
  }

  // 设置图表的配置选项
  function setOptions(options: EChartsOption, opts?: MySetOptionOpts) {
    const myOpts = {
      clear: true,
      groupId: null,
      ...opts,
    } as MySetOptionOpts
    const { clear, isEmpty, groupId } = myOpts
    isUnDef(options.title) && (options.title = { text: '' }) // 如果标题未定义，则设置为空
    cacheOptions.value = isEmpty ? emptyOption : options // 缓存配置选项
    if (unref(elRef)?.offsetHeight === 0) {
      useTimeoutFn(() => {
        setOptions(unref(getOptions))
      }, 30)
      return
    }
    nextTick(() => {
      useTimeoutFn(() => {
        if (!chartInstance) {
          initCharts(getDarkMode.value as 'default', groupId)

          if (!chartInstance) return
        }
        ;(clear || isEmpty) && chartInstance?.clear() // 清空图表
        chartInstance?.setOption(unref(getOptions) as any, myOpts) // 设置图表配置
      }, 30)
    })
  }

  // 调整图表大小
  function resize() {
    chartInstance?.resize()
  }

  // 监听主题模式变化
  watch(
    () => getDarkMode.value,
    (theme) => {
      if (chartInstance) {
        chartInstance.dispose() // 销毁图表实例
        initCharts(theme as 'default') // 重新初始化图表
        setOptions(cacheOptions.value) // 设置图表配置
      }
    }
  )

  // 在组件卸载时执行清理操作
  tryOnUnmounted(() => {
    if (!chartInstance) return
    removeResizeFn() // 移除resize事件监听
    chartInstance.dispose() // 销毁图表实例
    chartInstance = null
  })

  // 获取当前的ECharts实例
  function getInstance(): echarts.ECharts | null {
    if (!chartInstance) {
      initCharts(getDarkMode.value as 'default') // 初始化图表
    }
    return chartInstance
  }

  // 返回公开的函数和实例
  return {
    setOptions, // 设置图表配置
    resize, // 调整图表大小
    echarts, // ECharts库
    getInstance, // 获取ECharts实例
  }
}
