/*
 * @Author: “sunheng” “<EMAIL>”
 * @Date: 2025-06-16 18:46:34
 * @LastEditors: “sunheng” “<EMAIL>”
 * @LastEditTime: 2025-06-16 18:48:46
 * @FilePath: \supervise-clnt-platform-web\src\config\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const isPro = Object.is(process.env.NODE_ENV, 'production')
// let base_host = import.meta.env.VITE_BASE_HOST || '' //正式环境
let base_host = 'http://*************:9862' // 测试环境

if (isPro) {
  base_host = ''
}
base_host = ''
// VITE_BASE_SERVE
// '/api/v3/bw-clnt-supervise-service'
const config = {
  isOpenFrosua: false, // 是否开启消息埋点  仅招商云环境需要放开

  isFrosuaPro: isPro ? 'prod' : 'dev',

  USER_IFNO_NAMESPACE: '@@supervise-web_userInfo',

  base_host,
  gis_url: base_host,

  base_url: import.meta.env.VITE_BASE_SERVE,

  socket_url: '/api/v3/bw-svc-message-ws-service/ws',

  gis_serve_url: '/api/v3/bw-svc-enterprise-gis-service',

  image_url: base_host,

  image_url_new: base_host + '/img1/floorImage/',

  root_dir: import.meta.env.VITE_ROOT_DIR,

  //单位一张图gis地址
  gisDeviceIcon: base_host + '/img1/deviceIcons/_v3.0',
  gisUnitModel: base_host + '/img1/indoor',
  gisSkyUrl: base_host + '/img1/deviceIcons/z/sky/box/5',
  gisGifUrl: base_host + '/img1/deviceIcons/gif/alarm.gif',

  indoorService: base_host + '/api/v3/bw-svc-indoor-gis-service/record',
  dbService: base_host + '/api/v3/bw-svc-indoor-gis-service/indoorMap',
  // END
}
export default config
