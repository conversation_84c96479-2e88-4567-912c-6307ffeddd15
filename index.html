<!DOCTYPE html>
<html lang="en" class="dark">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <script src="./setRem.ts"></script>
  <link rel="stylesheet" href="/css/iconFamily/family.css" />
  <link rel="stylesheet" href="/css/aliFont/iconfont.css" />
 <link
  rel="stylesheet"
  type="text/css"
  href="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackageCSS?mepName=Only_IndoorThree.LMSDisplay_CSS&wgId=93"
/>
  <title>大唐（内蒙古）能源储能电站早期风险监测预警系统</title>
</head>

<body>
  <div id="app"></div>

  <script src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackage?mepName=Only_IndoorThree.LMSDisplay&wgId=93"></script>
  <script src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackage?mepName=Only_IndoorThree.LMSDisplay_GeoData&wgId=93"></script>
  
  <script type="module" src="/src/main.ts"></script>
  <!-- 消息埋点的引入js文件,仅招商云环境需要放开注释 -->
  <!-- <script src="/js/index.min.js"></script> -->

  <script src="/js/sockjs.min.js"></script>
  <script src="/js/stomp.min.js"></script>
  <!--本地化引入的echarts文件 -->
  <!-- rtsp video  播放 js START-->
  <script src="/js/video/adapter.js"></script>
  <script src="/js/video/platform.js"></script>
  <script src="/js/video/h5splayer.js"></script>
  <script src="/js/video/h5splayerhelper.js"></script>
  <!-- rtsp END -->

</html>