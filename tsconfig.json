{"compilerOptions": {"target": "ESNext", "noImplicitAny": false, "allowSyntheticDefaultImports": true, "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "types": ["element-plus/global"], "paths": {"~/*": ["./src/*"], "@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}